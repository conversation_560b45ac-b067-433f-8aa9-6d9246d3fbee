#!/usr/bin/env python3
"""
Comprehensive testing script for Angel One Trading Bot
Runs all tests and generates detailed reports
"""
import os
import sys
import subprocess
import time
from datetime import datetime
import json

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def run_unit_tests():
    """Run unit tests"""
    print("🧪 Running Unit Tests...")
    print("=" * 50)
    
    test_files = [
        "tests/test_risk_manager.py",
        "tests/test_technical_analysis.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\nRunning {test_file}...")
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "pytest", test_file, "-v"],
                    capture_output=True,
                    text=True,
                    timeout=60
                )
                
                if result.returncode == 0:
                    print(f"✅ {test_file} - PASSED")
                    results[test_file] = "PASSED"
                else:
                    print(f"❌ {test_file} - FAILED")
                    print(f"Error: {result.stderr}")
                    results[test_file] = "FAILED"
                    
            except subprocess.TimeoutExpired:
                print(f"⏰ {test_file} - TIMEOUT")
                results[test_file] = "TIMEOUT"
            except Exception as e:
                print(f"💥 {test_file} - ERROR: {str(e)}")
                results[test_file] = "ERROR"
        else:
            print(f"⚠️  {test_file} - NOT FOUND")
            results[test_file] = "NOT FOUND"
    
    return results

def run_integration_tests():
    """Run integration tests"""
    print("\n🔗 Running Integration Tests...")
    print("=" * 50)
    
    test_file = "tests/test_integration.py"
    
    if os.path.exists(test_file):
        print(f"Running {test_file}...")
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pytest", test_file, "-v"],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print(f"✅ Integration Tests - PASSED")
                return "PASSED"
            else:
                print(f"❌ Integration Tests - FAILED")
                print(f"Error: {result.stderr}")
                return "FAILED"
                
        except subprocess.TimeoutExpired:
            print(f"⏰ Integration Tests - TIMEOUT")
            return "TIMEOUT"
        except Exception as e:
            print(f"💥 Integration Tests - ERROR: {str(e)}")
            return "ERROR"
    else:
        print(f"⚠️  {test_file} - NOT FOUND")
        return "NOT FOUND"

def run_backtest():
    """Run backtesting"""
    print("\n📈 Running Backtest...")
    print("=" * 50)
    
    try:
        # Import and run backtest
        from tests.test_backtest import BacktestEngine
        
        engine = BacktestEngine()
        
        # Test multiple symbols
        symbols = ["RELIANCE", "TCS", "HDFCBANK"]
        backtest_results = {}
        
        for symbol in symbols:
            print(f"Backtesting {symbol}...")
            results = engine.run_backtest(symbol, days=10)
            backtest_results[symbol] = results
            
            print(f"  Total Trades: {results['total_trades']}")
            print(f"  Win Rate: {results['win_rate']:.1f}%")
            print(f"  Total Return: {results['total_return']:.2f}%")
            print(f"  Max Drawdown: {results['max_drawdown']:.2f}%")
        
        print("✅ Backtest - COMPLETED")
        return backtest_results
        
    except Exception as e:
        print(f"❌ Backtest - ERROR: {str(e)}")
        return None

def run_setup_validation():
    """Run setup validation"""
    print("\n⚙️  Running Setup Validation...")
    print("=" * 50)
    
    try:
        result = subprocess.run(
            [sys.executable, "test_setup.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ Setup Validation - PASSED")
            return "PASSED"
        else:
            print("❌ Setup Validation - FAILED")
            print(result.stdout)
            return "FAILED"
            
    except subprocess.TimeoutExpired:
        print("⏰ Setup Validation - TIMEOUT")
        return "TIMEOUT"
    except Exception as e:
        print(f"💥 Setup Validation - ERROR: {str(e)}")
        return "ERROR"

def run_live_api_tests(has_credentials=False):
    """Run live API tests with real Angel One data"""
    print("\n🔌 Running Live API Tests...")
    print("=" * 50)

    if not has_credentials:
        print("⚠️  Angel One credentials not configured - SKIPPING live API tests")
        print("   Run: python scripts/test_credentials.py")
        return "SKIPPED"

    try:
        # Double-check credentials are available
        from config import env_config
        creds = env_config.get_angel_credentials()

        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            print("⚠️  Angel One credentials incomplete - SKIPPING live API tests")
            return "SKIPPED"

        # Run live API tests
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "tests/test_live_api.py", "-v", "-x"],
            capture_output=True,
            text=True,
            timeout=300  # 5 minutes timeout
        )

        if result.returncode == 0:
            print("✅ Live API Tests - PASSED")
            return "PASSED"
        else:
            print("❌ Live API Tests - FAILED")
            print(f"Error: {result.stderr}")
            return "FAILED"

    except subprocess.TimeoutExpired:
        print("⏰ Live API Tests - TIMEOUT")
        return "TIMEOUT"
    except Exception as e:
        print(f"❌ Live API Tests - ERROR: {str(e)}")
        return "ERROR"

def run_paper_trading_test(has_credentials=False):
    """Run paper trading test with real API"""
    print("\n📝 Running Paper Trading Test...")
    print("=" * 50)

    if not has_credentials:
        print("⚠️  Angel One credentials not configured - using mock test")
        return run_mock_paper_trading_test()

    try:
        # Check if credentials are available
        from config import env_config
        creds = env_config.get_angel_credentials()

        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            print("⚠️  Angel One credentials not configured - using mock test")
            return run_mock_paper_trading_test()

        # Import trading bot
        from trading_bot import TradingBot

        # Create bot instance
        bot = TradingBot()
        bot.paper_trading = True

        # Test real login
        import asyncio
        login_success = asyncio.run(bot.login())
        if login_success:
            print("✅ Paper Trading Login - SUCCESS")
        else:
            print("❌ Paper Trading Login - FAILED")
            return "FAILED"

        # Test market data fetch
        asyncio.run(bot._update_market_data())
        if len(bot.market_data_cache) > 0:
            print("✅ Market Data Fetch - SUCCESS")
        else:
            print("❌ Market Data Fetch - FAILED")
            return "FAILED"

        print("✅ Paper Trading Test - PASSED")
        return "PASSED"

    except Exception as e:
        print(f"❌ Paper Trading Test - ERROR: {str(e)}")
        return "ERROR"

def run_mock_paper_trading_test():
    """Run paper trading test with mocked API"""
    try:
        from trading_bot import TradingBot
        from unittest.mock import patch

        # Create bot instance
        bot = TradingBot()
        bot.paper_trading = True

        # Mock API responses
        with patch.object(bot.angel_api, 'login', return_value=True), \
             patch.object(bot.angel_api, 'get_profile', return_value={'name': 'Test User'}), \
             patch.object(bot.angel_api, 'get_funds', return_value={'availablecash': 100000}), \
             patch.object(bot.angel_api, 'get_ltp', return_value=1000.0):

            # Test signal processing
            from technical_analysis import TechnicalSignal
            import pandas as pd

            test_signal = TechnicalSignal(
                symbol="TESTSTOCK",
                signal_type="BUY",
                strength=0.8,
                strategy="test",
                indicators={},
                timestamp=pd.Timestamp.now(),
                entry_price=1000,
                stop_loss=950,
                target=1100,
                confidence=0.7
            )

            # Process signal (should work in paper trading)
            import asyncio
            asyncio.run(bot._process_signal(test_signal))

            # Check if position was added
            if "TESTSTOCK" in bot.risk_manager.positions:
                print("✅ Mock Paper Trading - SUCCESS")
                return "PASSED"
            else:
                print("❌ Mock Paper Trading - FAILED")
                return "FAILED"

    except Exception as e:
        print(f"❌ Mock Paper Trading Test - ERROR: {str(e)}")
        return "ERROR"

def generate_test_report(results):
    """Generate comprehensive test report"""
    print("\n📊 Test Report")
    print("=" * 60)
    
    # Count results
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for category, result in results.items():
        if isinstance(result, dict):
            for test, status in result.items():
                total_tests += 1
                if status == "PASSED":
                    passed_tests += 1
                else:
                    failed_tests += 1
        else:
            total_tests += 1
            if result == "PASSED":
                passed_tests += 1
            else:
                failed_tests += 1
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDetailed Results:")
    print("-" * 40)
    
    for category, result in results.items():
        print(f"\n{category}:")
        if isinstance(result, dict):
            for test, status in result.items():
                status_icon = "✅" if status == "PASSED" else "❌"
                print(f"  {status_icon} {test}: {status}")
        else:
            status_icon = "✅" if result == "PASSED" else "❌"
            print(f"  {status_icon} {result}")
    
    # Save report to file
    report_data = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': failed_tests,
        'success_rate': (passed_tests/total_tests)*100,
        'results': results
    }
    
    os.makedirs('test_reports', exist_ok=True)
    report_file = f"test_reports/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2, default=str)
    
    print(f"\n📄 Report saved to: {report_file}")
    
    return passed_tests == total_tests

def check_credentials():
    """Check if Angel One credentials are configured"""
    print("🔐 Checking Angel One Credentials...")
    print("=" * 50)

    try:
        from config import env_config
        creds = env_config.get_angel_credentials()

        if all([creds['api_key'], creds['client_id'], creds['password'], creds['totp_secret']]):
            print("✅ Angel One credentials found")
            return True
        else:
            print("⚠️  Angel One credentials missing or incomplete")
            print("\n📋 Required credentials:")
            print("   - ANGEL_API_KEY")
            print("   - ANGEL_CLIENT_ID")
            print("   - ANGEL_PASSWORD")
            print("   - ANGEL_TOTP_SECRET")
            print("\n🔧 Setup options:")
            print("1. Run: python scripts/setup_bot.py")
            print("2. Run: python scripts/test_credentials.py")
            print("3. See: CREDENTIALS_SETUP.md")
            print("\n⚠️  Some tests will be skipped without credentials")
            return False

    except Exception as e:
        print(f"❌ Error checking credentials: {e}")
        print("\n🔧 Setup required:")
        print("1. Run: python scripts/setup_bot.py")
        print("2. See: CREDENTIALS_SETUP.md")
        return False

def main():
    """Main testing function"""
    print("🤖 Angel One Trading Bot - Comprehensive Testing")
    print("=" * 60)
    print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Check credentials first
    has_credentials = check_credentials()

    start_time = time.time()
    
    # Run all tests
    results = {}

    # 1. Setup Validation
    results['Setup Validation'] = run_setup_validation()

    # 2. Unit Tests
    results['Unit Tests'] = run_unit_tests()

    # 3. Integration Tests
    results['Integration Tests'] = run_integration_tests()

    # 4. Live API Tests (with real Angel One API)
    results['Live API Tests'] = run_live_api_tests(has_credentials)

    # 5. Paper Trading Test
    results['Paper Trading'] = run_paper_trading_test(has_credentials)

    # 6. Backtest
    backtest_results = run_backtest()
    if backtest_results:
        results['Backtest'] = "PASSED"
        results['Backtest Results'] = backtest_results
    else:
        results['Backtest'] = "FAILED"
    
    # Generate report
    all_passed = generate_test_report(results)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️  Total Test Duration: {duration:.2f} seconds")
    
    if all_passed:
        print("\n🎉 ALL TESTS PASSED!")

        if has_credentials:
            print("✅ Your trading bot is ready for deployment with real Angel One API!")
            print("\nNext Steps:")
            print("1. Review backtest results")
            print("2. Start paper trading: python main.py")
            print("3. Monitor: tail -f logs/trading_bot.log")
            print("4. After successful paper trading, consider live trading")
        else:
            print("✅ Basic tests passed, but Angel One credentials needed for full functionality!")
            print("\nNext Steps:")
            print("1. Setup credentials: python scripts/setup_bot.py")
            print("2. Test credentials: python scripts/test_credentials.py")
            print("3. Re-run tests: python scripts/run_tests.py")
            print("4. See: CREDENTIALS_SETUP.md")

        return 0
    else:
        print("\n❌ SOME TESTS FAILED! Please fix issues before deployment.")
        print("\nRecommendations:")
        print("1. Check error messages above")
        print("2. Verify environment setup")
        print("3. Ensure all dependencies are installed")

        if not has_credentials:
            print("4. Setup Angel One credentials: python scripts/setup_bot.py")
            print("5. See: CREDENTIALS_SETUP.md")

        print("6. Re-run tests after fixes")
        return 1

if __name__ == "__main__":
    sys.exit(main())
