#!/bin/bash
# ULTIMATE TRADING BOT - LINUX/MAC STARTUP SCRIPT
# Run this script to start the bot control dashboard

echo "🤖 ULTIMATE TRADING BOT - LINUX/MAC STARTUP"
echo "============================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed"
    echo "💡 Please install Python 3.8+ from your package manager"
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if bot files exist
if [ ! -f "deploy_ultimate_bot.py" ]; then
    echo "❌ Bot files not found in current directory"
    echo "💡 Make sure you're in the correct folder"
    read -p "Press Enter to exit..."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found"
    echo "💡 Copy .env.example to .env and configure your credentials"
    echo
    read -p "Would you like to create .env from template? (y/n): " create_env
    if [[ $create_env =~ ^[Yy]$ ]]; then
        cp .env.example .env
        echo "✅ .env file created from template"
        echo "💡 Please edit .env file with your Angel One credentials"
        echo
    fi
fi

# Create logs directory
mkdir -p logs

# Make script executable
chmod +x "$0"

echo "🚀 Starting Bot Control Dashboard..."
echo
echo "📋 AVAILABLE OPTIONS:"
echo "   1. Paper Trading (Safe testing)"
echo "   2. Live Trading (Real money)"
echo "   3. View Performance"
echo "   4. View Logs"
echo "   5. Configuration"
echo

# Start the control dashboard
python3 bot_control_dashboard.py

echo
echo "👋 Bot Control Dashboard closed"
read -p "Press Enter to exit..."
