# 🚀 Angel One Trading Bot - Quick Start Guide

Get your trading bot running with real Angel One SmartAPI in 15 minutes!

## ⚡ **Prerequisites (5 minutes)**

### **1. Angel One Account**
- ✅ Active Angel One trading account
- ✅ SmartAPI subscription enabled
- ✅ Account funded (minimum ₹10,000 for testing)

### **2. System Requirements**
- ✅ Python 3.8+ installed
- ✅ Stable internet connection
- ✅ Windows/Linux/macOS

---

## 🔧 **Step 1: Setup Environment (3 minutes)**

### **Clone & Install**
```bash
# Clone the repository
git clone <repository-url>
cd angel-one-trading-bot

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

---

## 🔑 **Step 2: Configure Angel One Credentials (5 minutes)**

### **Get Your Credentials**
1. **Login** to [Angel One SmartAPI Portal](https://smartapi.angelbroking.com/)
2. **Generate API Key** in API section
3. **Note your Client ID** (trading account number)
4. **Setup 2FA** and get TOTP secret
5. **Use your login password**

### **Setup Credentials**
```bash
# Run interactive setup
python scripts/setup_bot.py
```

**OR manually create .env file:**
```bash
cat > .env << 'EOF'
# Angel One SmartAPI Credentials
ANGEL_API_KEY=your_api_key_here
ANGEL_CLIENT_ID=your_client_id_here
ANGEL_PASSWORD=your_password_here
ANGEL_TOTP_SECRET=your_totp_secret_here

# Trading Configuration
PAPER_TRADING=True
INITIAL_CAPITAL=100000
MAX_DAILY_LOSS=5000
EOF
```

### **Test Credentials**
```bash
# Verify credentials work
python scripts/test_credentials.py
```

**Expected output:**
```
✅ Login successful
✅ Profile: Your Name
✅ Available cash: ₹100,000
🎉 ALL CREDENTIAL TESTS PASSED!
```

---

## 🧪 **Step 3: Run Tests (5 minutes)**

### **Quick Test**
```bash
# Run all tests
python scripts/run_tests.py
```

### **Live Data Test**
```bash
# Test with real market data
python scripts/live_data_test.py
```

**Expected results:**
```
✅ API connectivity working
✅ Real market data fetching
✅ Technical analysis working
✅ Risk management active
🎉 Ready for trading!
```

---

## 📈 **Step 4: Start Paper Trading (2 minutes)**

### **Start the Bot**
```bash
# Ensure paper trading mode
echo "PAPER_TRADING=True" >> .env

# Start trading bot
python main.py
```

### **Monitor the Bot**
```bash
# Watch logs in real-time
tail -f logs/trading_bot.log

# Check trades
tail -f logs/trades.log
```

**You should see:**
```
[INFO] Trading bot started in PAPER TRADING mode
[INFO] Connected to Angel One API
[INFO] Market data streaming active
[INFO] Scanning for trading signals...
```

---

## 🎯 **What Happens Next**

### **Paper Trading Phase (2+ weeks)**
- ✅ Bot trades with real market data
- ✅ No real money at risk
- ✅ All trades are simulated
- ✅ Performance tracking active
- ✅ Risk management tested

### **Monitor Daily**
```bash
# Check performance
grep "daily_pnl" logs/trading_bot.log | tail -5

# Check trades
grep "TRADE_EXECUTED" logs/trades.log

# Check for errors
tail logs/errors.log
```

### **Success Criteria for Live Trading**
- ✅ 2+ weeks stable operation
- ✅ Win rate > 50%
- ✅ Consistent daily performance
- ✅ No system crashes
- ✅ Risk limits working

---

## 🔴 **Troubleshooting**

### **Issue: Credentials Don't Work**
```bash
# Test individual components
python -c "
from src.angel_api import AngelOneAPI
api = AngelOneAPI()
print('Login:', api.login())
"

# Check TOTP generation
python -c "
import pyotp
totp = pyotp.TOTP('YOUR_TOTP_SECRET')
print('TOTP:', totp.now())
"
```

### **Issue: No Market Data**
```bash
# Check market hours
python -c "
from config import is_market_hours, is_trading_day
print('Trading Day:', is_trading_day())
print('Market Hours:', is_market_hours())
"

# Test specific symbol
python -c "
from src.angel_api import AngelOneAPI
api = AngelOneAPI()
api.login()
print('RELIANCE LTP:', api.get_ltp('RELIANCE'))
"
```

### **Issue: Bot Not Trading**
```bash
# Check paper trading mode
grep "PAPER_TRADING" .env

# Check trading symbols
grep "trading_symbols" logs/trading_bot.log

# Check signal generation
grep "SIGNAL" logs/trading_bot.log
```

---

## 📊 **Expected Performance**

### **Paper Trading Results (Example)**
```
📈 Paper Trading - Day 10
========================
Total Trades: 25
Win Rate: 64%
Daily P&L: ₹1,250
Max Drawdown: -2.8%
Sharpe Ratio: 1.4
✅ Performance: GOOD
```

### **Key Metrics to Watch**
- **Win Rate**: Should be > 50%
- **Daily P&L**: Should be positive most days
- **Max Drawdown**: Should be < 10%
- **Trade Frequency**: 5-15 trades per day
- **Risk Management**: No single trade > 2% loss

---

## 🚨 **Safety First**

### **NEVER Skip These Steps:**
1. ❌ **Don't start with live trading**
2. ❌ **Don't skip paper trading phase**
3. ❌ **Don't ignore risk limits**
4. ❌ **Don't trade without monitoring**
5. ❌ **Don't use all your capital initially**

### **Always Do This:**
1. ✅ **Start with paper trading**
2. ✅ **Monitor every trade initially**
3. ✅ **Test for 2+ weeks minimum**
4. ✅ **Start live with small capital**
5. ✅ **Keep risk limits conservative**

---

## 🎉 **Success Checklist**

### **Before Going Live:**
- [ ] Angel One credentials working
- [ ] Paper trading successful 2+ weeks
- [ ] Win rate > 50%
- [ ] Risk management tested
- [ ] No system crashes
- [ ] Comfortable with bot behavior
- [ ] Emergency procedures understood

### **Ready for Live Trading:**
```bash
# Switch to live trading (small capital first)
echo "PAPER_TRADING=False" > .env
echo "INITIAL_CAPITAL=10000" >> .env
echo "MAX_DAILY_LOSS=500" >> .env

# Start with minimal risk
python main.py
```

---

## 📞 **Support**

### **Need Help?**
1. **Check logs**: `tail -f logs/trading_bot.log`
2. **Run diagnostics**: `python test_setup.py`
3. **Test credentials**: `python scripts/test_credentials.py`
4. **See documentation**: `CREDENTIALS_SETUP.md`, `TESTING_GUIDE.md`

### **Angel One API Issues:**
- Check [SmartAPI Documentation](https://smartapi.angelbroking.com/docs)
- Contact Angel One support
- Verify account permissions

---

## 🚀 **You're Ready!**

**Your Angel One trading bot is now configured and ready to trade with real market data!**

### **Next Steps:**
1. **Monitor paper trading** for 2+ weeks
2. **Analyze performance** daily
3. **Optimize strategies** based on results
4. **Gradually transition** to live trading
5. **Scale up** as confidence grows

**Happy Trading! 📈💰**

---

## 📋 **Quick Commands Reference**

```bash
# Setup
python scripts/setup_bot.py
python scripts/test_credentials.py

# Testing
python scripts/run_tests.py
python scripts/live_data_test.py

# Trading
python main.py  # Start bot

# Monitoring
tail -f logs/trading_bot.log
tail -f logs/trades.log
grep "daily_pnl" logs/trading_bot.log
```

**Remember: Start with paper trading, monitor closely, scale gradually! 🎯**
