"""
Configuration settings for Angel One Intraday Trading Bot
"""
import os
from dataclasses import dataclass
from typing import List, Dict
import pytz

@dataclass
class TradingConfig:
    """Main trading configuration"""
    
    # Account Settings
    INITIAL_CAPITAL: float = 100000.0  # ₹1 Lakh
    MAX_RISK_PER_TRADE: float = 0.02   # 2% per trade
    MAX_DAILY_LOSS: float = 0.05       # 5% daily loss limit
    MAX_POSITIONS: int = 4             # Maximum simultaneous positions
    
    # Trading Hours (IST)
    MARKET_START_TIME: str = "09:15"
    MARKET_END_TIME: str = "15:20"     # Square off 10 minutes before close
    PRE_MARKET_START: str = "09:00"
    
    # Position Sizing
    MIN_POSITION_SIZE: float = 5000.0  # Minimum ₹5000 per position
    MAX_POSITION_SIZE: float = 25000.0 # Maximum ₹25000 per position
    LEVERAGE_MULTIPLIER: float = 3.0   # Conservative leverage for MIS
    
    # Risk Management
    DEFAULT_STOP_LOSS_PCT: float = 0.015  # 1.5% stop loss
    DEFAULT_TARGET_PCT: float = 0.03      # 3% target (1:2 risk reward)
    TRAILING_STOP_PCT: float = 0.01       # 1% trailing stop
    
    # Technical Analysis
    RSI_PERIOD: int = 14
    RSI_OVERSOLD: float = 30.0
    RSI_OVERBOUGHT: float = 70.0
    
    EMA_FAST: int = 9
    EMA_SLOW: int = 21
    
    VOLUME_MULTIPLIER: float = 1.5  # Volume should be 1.5x average
    
    # Market Data
    CANDLE_INTERVAL: str = "ONE_MINUTE"
    HISTORICAL_DAYS: int = 30
    
    # Timezone
    TIMEZONE = pytz.timezone('Asia/Kolkata')

@dataclass 
class AngelOneConfig:
    """Angel One API configuration"""
    
    # API Endpoints
    BASE_URL: str = "https://apiconnect.angelbroking.com"
    LOGIN_URL: str = f"{BASE_URL}/rest/auth/angelbroking/user/v1/loginByPassword"
    LOGOUT_URL: str = f"{BASE_URL}/rest/secure/angelbroking/user/v1/logout"
    PROFILE_URL: str = f"{BASE_URL}/rest/secure/angelbroking/user/v1/getProfile"
    
    # WebSocket URLs
    WS_URL: str = "wss://smartapisocket.angelone.in/smart-stream"
    
    # Order Types
    ORDER_TYPE_MARKET: str = "MARKET"
    ORDER_TYPE_LIMIT: str = "LIMIT"
    ORDER_TYPE_SL: str = "STOPLOSS_LIMIT"
    ORDER_TYPE_SL_M: str = "STOPLOSS_MARKET"
    
    # Product Types
    PRODUCT_MIS: str = "MIS"  # Margin Intraday Square Off
    PRODUCT_CNC: str = "CNC"  # Cash and Carry
    PRODUCT_NRML: str = "NRML"  # Normal
    
    # Transaction Types
    TRANSACTION_BUY: str = "BUY"
    TRANSACTION_SELL: str = "SELL"
    
    # Exchanges
    EXCHANGE_NSE: str = "NSE"
    EXCHANGE_BSE: str = "BSE"
    EXCHANGE_NFO: str = "NFO"  # NSE F&O
    
    # Duration
    DURATION_DAY: str = "DAY"
    DURATION_IOC: str = "IOC"  # Immediate or Cancel

@dataclass
class StockUniverse:
    """Predefined stock universe for trading"""
    
    # Nifty 50 High Volume Stocks
    NIFTY_STOCKS: List[str] = None
    
    # Bank Nifty Stocks  
    BANK_NIFTY_STOCKS: List[str] = None
    
    # High Volume Stocks
    HIGH_VOLUME_STOCKS: List[str] = None
    
    def __post_init__(self):
        self.NIFTY_STOCKS = [
            "RELIANCE", "TCS", "HDFCBANK", "BHARTIARTL", "ICICIBANK",
            "INFOSYS", "ITC", "SBIN", "LT", "HCLTECH",
            "MARUTI", "SUNPHARMA", "TITAN", "ULTRACEMCO", "ONGC",
            "NESTLEIND", "KOTAKBANK", "ASIANPAINT", "WIPRO", "M&M"
        ]
        
        self.BANK_NIFTY_STOCKS = [
            "HDFCBANK", "ICICIBANK", "SBIN", "KOTAKBANK", 
            "AXISBANK", "INDUSINDBK", "PNB", "BANKBARODA",
            "FEDERALBNK", "IDFCFIRSTB", "AUBANK", "BANDHANBNK"
        ]
        
        self.HIGH_VOLUME_STOCKS = [
            "RELIANCE", "TCS", "HDFCBANK", "ICICIBANK", "INFOSYS",
            "ITC", "SBIN", "BHARTIARTL", "LT", "WIPRO",
            "MARUTI", "ONGC", "NTPC", "POWERGRID", "COALINDIA"
        ]

@dataclass
class StrategyConfig:
    """Trading strategy configurations"""
    
    # Opening Range Breakout (ORB)
    ORB_PERIOD_MINUTES: int = 15  # First 15 minutes range
    ORB_MIN_RANGE_POINTS: float = 10.0  # Minimum range for ORB
    ORB_MAX_RANGE_POINTS: float = 100.0  # Maximum range for ORB
    
    # VWAP Strategy
    VWAP_DEVIATION_PCT: float = 0.005  # 0.5% deviation from VWAP
    VWAP_VOLUME_CONFIRMATION: bool = True
    
    # Momentum Strategy
    MOMENTUM_PERIOD: int = 20
    MOMENTUM_THRESHOLD: float = 0.02  # 2% momentum threshold
    
    # Mean Reversion
    MEAN_REVERSION_PERIOD: int = 20
    MEAN_REVERSION_STD_DEV: float = 2.0  # 2 standard deviations
    
    # Strategy Weights (should sum to 1.0)
    STRATEGY_WEIGHTS: Dict[str, float] = None
    
    def __post_init__(self):
        self.STRATEGY_WEIGHTS = {
            "orb": 0.4,           # Opening Range Breakout
            "vwap": 0.3,          # VWAP Strategy  
            "momentum": 0.2,      # Momentum Strategy
            "mean_reversion": 0.1 # Mean Reversion
        }

# Environment Variables
class EnvConfig:
    """Environment-based configuration"""
    
    @staticmethod
    def get_angel_credentials():
        """Get Angel One credentials from environment"""
        return {
            'api_key': os.getenv('ANGEL_API_KEY'),
            'client_id': os.getenv('ANGEL_CLIENT_ID'), 
            'password': os.getenv('ANGEL_PASSWORD'),
            'totp_secret': os.getenv('ANGEL_TOTP_SECRET')  # For 2FA
        }
    
    @staticmethod
    def get_database_url():
        """Get database URL"""
        return os.getenv('DATABASE_URL', 'sqlite:///trading_bot.db')
    
    @staticmethod
    def get_log_level():
        """Get logging level"""
        return os.getenv('LOG_LEVEL', 'INFO')
    
    @staticmethod
    def is_paper_trading():
        """Check if running in paper trading mode"""
        return os.getenv('PAPER_TRADING', 'True').lower() == 'true'

# Global Configuration Instances
trading_config = TradingConfig()
angel_config = AngelOneConfig()
stock_universe = StockUniverse()
strategy_config = StrategyConfig()
env_config = EnvConfig()

# Market Holidays (2024) - Update annually
MARKET_HOLIDAYS_2024 = [
    "2024-01-26",  # Republic Day
    "2024-03-08",  # Holi
    "2024-03-29",  # Good Friday
    "2024-04-11",  # Eid ul Fitr
    "2024-04-14",  # Ambedkar Jayanti
    "2024-04-17",  # Ram Navami
    "2024-05-01",  # Maharashtra Day
    "2024-06-17",  # Eid ul Adha
    "2024-08-15",  # Independence Day
    "2024-08-26",  # Janmashtami
    "2024-10-02",  # Gandhi Jayanti
    "2024-10-12",  # Dussehra
    "2024-11-01",  # Diwali
    "2024-11-15",  # Guru Nanak Jayanti
    "2024-12-25",  # Christmas
]

def is_trading_day():
    """Check if today is a trading day"""
    from datetime import datetime
    today = datetime.now(trading_config.TIMEZONE).strftime("%Y-%m-%d")
    
    # Check if weekend
    weekday = datetime.now(trading_config.TIMEZONE).weekday()
    if weekday >= 5:  # Saturday = 5, Sunday = 6
        return False
    
    # Check if holiday
    if today in MARKET_HOLIDAYS_2024:
        return False
        
    return True

def is_market_hours():
    """Check if current time is within market hours"""
    from datetime import datetime
    now = datetime.now(trading_config.TIMEZONE)
    current_time = now.strftime("%H:%M")
    
    return (trading_config.MARKET_START_TIME <= current_time <= trading_config.MARKET_END_TIME 
            and is_trading_day())
