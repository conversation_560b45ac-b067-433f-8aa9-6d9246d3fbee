"""
Risk Management Module
Handles position sizing, stop losses, and portfolio risk controls
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import pandas as pd

from config import trading_config, env_config

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """Represents a trading position"""
    symbol: str
    quantity: int
    entry_price: float
    current_price: float
    side: str  # 'BUY' or 'SELL'
    entry_time: datetime
    stop_loss: float
    target: float
    order_id: str
    unrealized_pnl: float = 0.0
    
    def update_price(self, new_price: float):
        """Update current price and calculate unrealized P&L"""
        self.current_price = new_price
        
        if self.side == 'BUY':
            self.unrealized_pnl = (new_price - self.entry_price) * self.quantity
        else:  # SELL
            self.unrealized_pnl = (self.entry_price - new_price) * self.quantity
    
    def get_position_value(self) -> float:
        """Get current position value"""
        return self.current_price * self.quantity
    
    def is_stop_loss_hit(self) -> bool:
        """Check if stop loss is triggered"""
        if self.side == 'BUY':
            return self.current_price <= self.stop_loss
        else:  # SELL
            return self.current_price >= self.stop_loss
    
    def is_target_hit(self) -> bool:
        """Check if target is reached"""
        if self.side == 'BUY':
            return self.current_price >= self.target
        else:  # SELL
            return self.current_price <= self.target

@dataclass
class RiskMetrics:
    """Portfolio risk metrics"""
    total_capital: float
    available_capital: float
    used_capital: float
    daily_pnl: float
    total_pnl: float
    max_drawdown: float
    current_positions: int
    max_risk_per_trade: float
    daily_loss_limit: float
    
class RiskManager:
    """Comprehensive risk management system"""
    
    def __init__(self):
        self.positions: Dict[str, Position] = {}
        self.daily_trades: List[Dict] = []
        self.daily_pnl: float = 0.0
        self.total_pnl: float = 0.0
        self.max_drawdown: float = 0.0
        self.peak_capital: float = trading_config.INITIAL_CAPITAL
        self.available_capital: float = trading_config.INITIAL_CAPITAL
        
        # Risk limits
        self.max_daily_loss = trading_config.INITIAL_CAPITAL * trading_config.MAX_DAILY_LOSS
        self.max_risk_per_trade = trading_config.INITIAL_CAPITAL * trading_config.MAX_RISK_PER_TRADE
        
        # Trading state
        self.trading_enabled: bool = True
        self.risk_alerts: List[str] = []
        
        logger.info("Risk Manager initialized")
    
    def calculate_position_size(self, entry_price: float, stop_loss: float, 
                              risk_amount: Optional[float] = None) -> int:
        """Calculate optimal position size based on risk parameters"""
        
        if risk_amount is None:
            risk_amount = self.max_risk_per_trade
        
        # Calculate risk per share
        risk_per_share = abs(entry_price - stop_loss)
        
        if risk_per_share == 0:
            logger.warning("Risk per share is zero, cannot calculate position size")
            return 0
        
        # Basic position size calculation
        basic_position_size = int(risk_amount / risk_per_share)
        
        # Apply position value limits
        position_value = basic_position_size * entry_price
        
        # Check minimum position size
        if position_value < trading_config.MIN_POSITION_SIZE:
            logger.info(f"Position value {position_value} below minimum {trading_config.MIN_POSITION_SIZE}")
            return 0
        
        # Check maximum position size
        if position_value > trading_config.MAX_POSITION_SIZE:
            max_quantity = int(trading_config.MAX_POSITION_SIZE / entry_price)
            logger.info(f"Position size reduced to maximum allowed: {max_quantity}")
            return max_quantity
        
        # Check available capital
        if position_value > self.available_capital:
            max_affordable = int(self.available_capital / entry_price)
            logger.info(f"Position size reduced due to capital constraint: {max_affordable}")
            return max_affordable
        
        # Check maximum positions limit
        if len(self.positions) >= trading_config.MAX_POSITIONS:
            logger.warning("Maximum positions limit reached")
            return 0
        
        return basic_position_size
    
    def validate_trade(self, symbol: str, side: str, quantity: int, 
                      entry_price: float, stop_loss: float) -> Tuple[bool, str]:
        """Validate if a trade can be executed"""
        
        # Check if trading is enabled
        if not self.trading_enabled:
            return False, "Trading is disabled due to risk limits"
        
        # Check if symbol already has a position
        if symbol in self.positions:
            return False, f"Position already exists for {symbol}"
        
        # Check daily loss limit
        if self.daily_pnl <= -self.max_daily_loss:
            self.trading_enabled = False
            return False, "Daily loss limit exceeded"
        
        # Check position count
        if len(self.positions) >= trading_config.MAX_POSITIONS:
            return False, "Maximum positions limit reached"
        
        # Calculate position value
        position_value = quantity * entry_price
        
        # Check minimum position size
        if position_value < trading_config.MIN_POSITION_SIZE:
            return False, f"Position value {position_value} below minimum"
        
        # Check maximum position size
        if position_value > trading_config.MAX_POSITION_SIZE:
            return False, f"Position value {position_value} exceeds maximum"
        
        # Check available capital
        if position_value > self.available_capital:
            return False, "Insufficient capital available"
        
        # Validate stop loss
        if side == 'BUY' and stop_loss >= entry_price:
            return False, "Invalid stop loss for BUY order"
        elif side == 'SELL' and stop_loss <= entry_price:
            return False, "Invalid stop loss for SELL order"
        
        # Calculate risk amount
        risk_amount = abs(entry_price - stop_loss) * quantity
        if risk_amount > self.max_risk_per_trade:
            return False, f"Risk amount {risk_amount} exceeds maximum per trade"
        
        return True, "Trade validation passed"
    
    def add_position(self, symbol: str, quantity: int, entry_price: float,
                    side: str, stop_loss: float, target: float, order_id: str) -> bool:
        """Add a new position to the portfolio"""
        
        # Validate the trade first
        is_valid, message = self.validate_trade(symbol, side, quantity, entry_price, stop_loss)
        if not is_valid:
            logger.warning(f"Cannot add position for {symbol}: {message}")
            return False
        
        # Create position
        position = Position(
            symbol=symbol,
            quantity=quantity,
            entry_price=entry_price,
            current_price=entry_price,
            side=side,
            entry_time=datetime.now(trading_config.TIMEZONE),
            stop_loss=stop_loss,
            target=target,
            order_id=order_id
        )
        
        # Add to positions
        self.positions[symbol] = position
        
        # Update available capital
        position_value = quantity * entry_price
        self.available_capital -= position_value
        
        logger.info(f"Position added: {symbol} {side} {quantity} @ {entry_price}")
        return True
    
    def remove_position(self, symbol: str, exit_price: float, exit_reason: str) -> Optional[float]:
        """Remove a position and calculate realized P&L"""
        
        if symbol not in self.positions:
            logger.warning(f"No position found for {symbol}")
            return None
        
        position = self.positions[symbol]
        
        # Calculate realized P&L
        if position.side == 'BUY':
            realized_pnl = (exit_price - position.entry_price) * position.quantity
        else:  # SELL
            realized_pnl = (position.entry_price - exit_price) * position.quantity
        
        # Update P&L
        self.daily_pnl += realized_pnl
        self.total_pnl += realized_pnl
        
        # Update available capital
        exit_value = position.quantity * exit_price
        self.available_capital += exit_value
        
        # Record trade
        trade_record = {
            'symbol': symbol,
            'side': position.side,
            'quantity': position.quantity,
            'entry_price': position.entry_price,
            'exit_price': exit_price,
            'entry_time': position.entry_time,
            'exit_time': datetime.now(trading_config.TIMEZONE),
            'realized_pnl': realized_pnl,
            'exit_reason': exit_reason
        }
        self.daily_trades.append(trade_record)
        
        # Remove position
        del self.positions[symbol]
        
        # Update drawdown
        self._update_drawdown()
        
        logger.info(f"Position closed: {symbol} P&L: {realized_pnl:.2f} Reason: {exit_reason}")
        return realized_pnl
    
    def update_position_prices(self, price_data: Dict[str, float]):
        """Update current prices for all positions"""
        
        for symbol, position in self.positions.items():
            if symbol in price_data:
                position.update_price(price_data[symbol])
    
    def check_stop_losses(self) -> List[str]:
        """Check which positions have hit stop loss"""
        
        stop_loss_hits = []
        
        for symbol, position in self.positions.items():
            if position.is_stop_loss_hit():
                stop_loss_hits.append(symbol)
                logger.warning(f"Stop loss hit for {symbol} at {position.current_price}")
        
        return stop_loss_hits
    
    def check_targets(self) -> List[str]:
        """Check which positions have hit target"""
        
        target_hits = []
        
        for symbol, position in self.positions.items():
            if position.is_target_hit():
                target_hits.append(symbol)
                logger.info(f"Target hit for {symbol} at {position.current_price}")
        
        return target_hits
    
    def get_portfolio_summary(self) -> RiskMetrics:
        """Get current portfolio risk metrics"""
        
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        used_capital = trading_config.INITIAL_CAPITAL - self.available_capital
        
        return RiskMetrics(
            total_capital=trading_config.INITIAL_CAPITAL,
            available_capital=self.available_capital,
            used_capital=used_capital,
            daily_pnl=self.daily_pnl + total_unrealized_pnl,
            total_pnl=self.total_pnl + total_unrealized_pnl,
            max_drawdown=self.max_drawdown,
            current_positions=len(self.positions),
            max_risk_per_trade=self.max_risk_per_trade,
            daily_loss_limit=self.max_daily_loss
        )
    
    def _update_drawdown(self):
        """Update maximum drawdown"""
        current_capital = self.available_capital + sum(pos.get_position_value() for pos in self.positions.values())
        
        if current_capital > self.peak_capital:
            self.peak_capital = current_capital
        
        current_drawdown = (self.peak_capital - current_capital) / self.peak_capital
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
    
    def reset_daily_metrics(self):
        """Reset daily metrics at start of new trading day"""
        self.daily_pnl = 0.0
        self.daily_trades = []
        self.trading_enabled = True
        self.risk_alerts = []
        
        logger.info("Daily risk metrics reset")
    
    def emergency_stop(self, reason: str):
        """Emergency stop all trading"""
        self.trading_enabled = False
        self.risk_alerts.append(f"EMERGENCY STOP: {reason}")
        logger.critical(f"Emergency stop triggered: {reason}")
    
    def get_position_summary(self) -> Dict:
        """Get summary of all current positions"""
        summary = {}
        
        for symbol, position in self.positions.items():
            summary[symbol] = {
                'quantity': position.quantity,
                'entry_price': position.entry_price,
                'current_price': position.current_price,
                'side': position.side,
                'unrealized_pnl': position.unrealized_pnl,
                'stop_loss': position.stop_loss,
                'target': position.target,
                'entry_time': position.entry_time.strftime('%H:%M:%S')
            }
        
        return summary
