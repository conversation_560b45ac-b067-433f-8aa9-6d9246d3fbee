<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Ultimate Trading Bot</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
            animation: slideInUp 0.6s ease-out;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo i {
            font-size: 4rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .logo h2 {
            color: #333;
            font-weight: 700;
            margin-top: 10px;
        }
        
        .btn-google {
            background: linear-gradient(135deg, #4285f4, #34a853);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-google:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(66, 133, 244, 0.3);
            color: white;
        }
        
        .btn-google::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-google:hover::before {
            left: 100%;
        }
        
        .features {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #eee;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: #666;
        }
        
        .feature-item i {
            color: #667eea;
            margin-right: 15px;
            width: 20px;
        }
        
        .security-note {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: 0 10px 10px 0;
            margin-top: 20px;
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }
    </style>
</head>
<body>
    <!-- Floating Background Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <!-- Logo and Title -->
        <div class="logo">
            <i class="fas fa-robot"></i>
            <h2>Ultimate Trading Bot</h2>
            <p class="text-muted">Secure Login</p>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Google Sign-In Button -->
        <div class="d-grid">
            <a href="{{ url_for('google_auth') }}" class="btn btn-google btn-lg">
                <i class="fab fa-google me-2"></i>
                Sign in with Google
            </a>
        </div>

        <!-- Features -->
        <div class="features">
            <h6 class="text-center mb-3">Why Choose Our Platform?</h6>
            
            <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>Bank-level security with Google OAuth</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-chart-line"></i>
                <span>AI-powered trading algorithms</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-mobile-alt"></i>
                <span>Access from any device, anywhere</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-clock"></i>
                <span>24/7 automated trading</span>
            </div>
            
            <div class="feature-item">
                <i class="fas fa-globe"></i>
                <span>Multi-market support</span>
            </div>
        </div>

        <!-- Security Note -->
        <div class="security-note">
            <small>
                <i class="fas fa-lock me-2"></i>
                <strong>Secure & Private:</strong> We use Google's secure authentication. 
                Your trading credentials are encrypted and never shared.
            </small>
        </div>

        <!-- Footer Links -->
        <div class="text-center mt-4">
            <small class="text-muted">
                <a href="{{ url_for('about') }}" class="text-decoration-none me-3">About</a>
                <a href="{{ url_for('help_page') }}" class="text-decoration-none me-3">Help</a>
                <a href="#" class="text-decoration-none">Privacy</a>
            </small>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add loading state to Google sign-in button
        document.querySelector('.btn-google').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing in...';
            this.disabled = true;
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);
        
        // Add entrance animation to features
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature-item');
            features.forEach((feature, index) => {
                feature.style.opacity = '0';
                feature.style.transform = 'translateX(-20px)';
                
                setTimeout(() => {
                    feature.style.transition = 'all 0.5s ease';
                    feature.style.opacity = '1';
                    feature.style.transform = 'translateX(0)';
                }, 100 * index);
            });
        });
    </script>
</body>
</html>
