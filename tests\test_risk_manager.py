"""
Unit tests for Risk Manager
"""
import pytest
import sys
import os
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from risk_manager import RiskManager, Position
from config import trading_config

class TestRiskManager:
    """Test cases for Risk Manager"""
    
    def setup_method(self):
        """Setup for each test"""
        self.risk_manager = RiskManager()
    
    def test_position_size_calculation(self):
        """Test position size calculation"""
        # Test normal case
        position_size = self.risk_manager.calculate_position_size(
            entry_price=1000,
            stop_loss=980,
            risk_amount=2000
        )
        
        expected_size = int(2000 / 20)  # risk_amount / (entry_price - stop_loss)
        assert position_size == expected_size
        
        # Test zero risk case
        position_size = self.risk_manager.calculate_position_size(
            entry_price=1000,
            stop_loss=1000,  # Same as entry price
            risk_amount=2000
        )
        assert position_size == 0
        
        # Test insufficient capital
        self.risk_manager.available_capital = 1000
        position_size = self.risk_manager.calculate_position_size(
            entry_price=1000,
            stop_loss=980,
            risk_amount=50000  # Very high risk amount
        )
        assert position_size <= 1  # Should be limited by available capital
    
    def test_trade_validation(self):
        """Test trade validation logic"""
        # Valid trade
        is_valid, message = self.risk_manager.validate_trade(
            symbol="RELIANCE",
            side="BUY",
            quantity=10,
            entry_price=1000,
            stop_loss=980
        )
        assert is_valid == True
        
        # Invalid stop loss for BUY
        is_valid, message = self.risk_manager.validate_trade(
            symbol="RELIANCE",
            side="BUY",
            quantity=10,
            entry_price=1000,
            stop_loss=1020  # Stop loss above entry for BUY
        )
        assert is_valid == False
        assert "Invalid stop loss" in message
        
        # Invalid stop loss for SELL
        is_valid, message = self.risk_manager.validate_trade(
            symbol="RELIANCE",
            side="SELL",
            quantity=10,
            entry_price=1000,
            stop_loss=980  # Stop loss below entry for SELL
        )
        assert is_valid == False
        assert "Invalid stop loss" in message
        
        # Position too small
        is_valid, message = self.risk_manager.validate_trade(
            symbol="RELIANCE",
            side="BUY",
            quantity=1,
            entry_price=100,  # Very small position value
            stop_loss=95
        )
        assert is_valid == False
        assert "below minimum" in message
    
    def test_position_management(self):
        """Test adding and removing positions"""
        # Add position
        success = self.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=980,
            target=1040,
            order_id="TEST001"
        )
        assert success == True
        assert "RELIANCE" in self.risk_manager.positions
        
        # Try to add duplicate position
        success = self.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=5,
            entry_price=1010,
            side="BUY",
            stop_loss=990,
            target=1050,
            order_id="TEST002"
        )
        assert success == False  # Should fail due to existing position
        
        # Remove position
        realized_pnl = self.risk_manager.remove_position(
            symbol="RELIANCE",
            exit_price=1020,
            exit_reason="Target Hit"
        )
        assert realized_pnl == 200  # (1020 - 1000) * 10
        assert "RELIANCE" not in self.risk_manager.positions
    
    def test_stop_loss_detection(self):
        """Test stop loss detection"""
        # Add position
        self.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=980,
            target=1040,
            order_id="TEST001"
        )
        
        # Update price to trigger stop loss
        self.risk_manager.update_position_prices({"RELIANCE": 975})
        
        # Check stop loss detection
        stop_loss_hits = self.risk_manager.check_stop_losses()
        assert "RELIANCE" in stop_loss_hits
    
    def test_target_detection(self):
        """Test target detection"""
        # Add position
        self.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=980,
            target=1040,
            order_id="TEST001"
        )
        
        # Update price to trigger target
        self.risk_manager.update_position_prices({"RELIANCE": 1045})
        
        # Check target detection
        target_hits = self.risk_manager.check_targets()
        assert "RELIANCE" in target_hits
    
    def test_daily_loss_limit(self):
        """Test daily loss limit enforcement"""
        # Simulate large loss
        self.risk_manager.daily_pnl = -6000  # Exceeds 5000 limit
        
        # Try to validate new trade
        is_valid, message = self.risk_manager.validate_trade(
            symbol="TCS",
            side="BUY",
            quantity=10,
            entry_price=1000,
            stop_loss=980
        )
        assert is_valid == False
        assert "Daily loss limit" in message
        assert self.risk_manager.trading_enabled == False
    
    def test_max_positions_limit(self):
        """Test maximum positions limit"""
        # Add maximum allowed positions
        for i in range(trading_config.MAX_POSITIONS):
            success = self.risk_manager.add_position(
                symbol=f"STOCK{i}",
                quantity=10,
                entry_price=1000,
                side="BUY",
                stop_loss=980,
                target=1040,
                order_id=f"TEST{i:03d}"
            )
            assert success == True
        
        # Try to add one more position
        success = self.risk_manager.add_position(
            symbol="EXTRASTOCK",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=980,
            target=1040,
            order_id="TESTEXTRA"
        )
        assert success == False  # Should fail due to position limit
    
    def test_portfolio_metrics(self):
        """Test portfolio metrics calculation"""
        # Add some positions
        self.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=980,
            target=1040,
            order_id="TEST001"
        )
        
        # Update prices
        self.risk_manager.update_position_prices({"RELIANCE": 1020})
        
        # Get metrics
        metrics = self.risk_manager.get_portfolio_summary()
        
        assert metrics.current_positions == 1
        assert metrics.daily_pnl == 200  # Unrealized P&L
        assert metrics.available_capital < trading_config.INITIAL_CAPITAL
    
    def test_emergency_stop(self):
        """Test emergency stop functionality"""
        self.risk_manager.emergency_stop("Test emergency")
        
        assert self.risk_manager.trading_enabled == False
        assert len(self.risk_manager.risk_alerts) > 0
        assert "EMERGENCY STOP" in self.risk_manager.risk_alerts[0]

if __name__ == "__main__":
    pytest.main([__file__])
