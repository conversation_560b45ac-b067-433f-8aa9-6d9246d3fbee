#!/usr/bin/env python3
"""
ULTIMATE TRADING BOT - WEB INTERFACE
Professional web dashboard for controlling and monitoring the trading bot
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_socketio import SocketIO, emit
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import os
import threading
import time
from typing import Dict, List, Optional
import yfinance as yf
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-here')
socketio = SocketIO(app, cors_allowed_origins="*")

# ============================================================================
# TRADING BOT BACKEND
# ============================================================================

class TradingBotEngine:
    """Core trading bot engine for web interface"""
    
    def __init__(self):
        self.is_running = False
        self.current_market = "indian"
        self.trading_mode = "paper"  # paper or live
        self.capital = 100.0
        self.positions = {}
        self.trades = []
        self.performance = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'daily_pnl': 0.0
        }
        
        # Market configurations
        self.markets = {
            'indian': {
                'name': 'Indian Stock Market',
                'symbols': ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS'],
                'currency': 'INR',
                'suffix': '.NS'
            },
            'us': {
                'name': 'US Stock Market',
                'symbols': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'],
                'currency': 'USD',
                'suffix': ''
            },
            'european': {
                'name': 'European Stock Market',
                'symbols': ['ASML.AS', 'SAP.DE', 'NESN.SW', 'MC.PA'],
                'currency': 'EUR',
                'suffix': ''
            },
            'crypto': {
                'name': 'Cryptocurrency Market',
                'symbols': ['BTC', 'ETH', 'BNB', 'ADA', 'SOL'],
                'currency': 'USD',
                'suffix': '-USD'
            }
        }
        
        self.bot_thread = None
        self.last_update = datetime.now()
        
        logger.info("Trading Bot Engine initialized")
    
    def start_bot(self, market: str, mode: str) -> bool:
        """Start the trading bot"""
        try:
            if self.is_running:
                return False
            
            self.current_market = market
            self.trading_mode = mode
            self.is_running = True
            
            # Start bot in separate thread
            self.bot_thread = threading.Thread(target=self._run_bot_loop)
            self.bot_thread.daemon = True
            self.bot_thread.start()
            
            logger.info(f"Bot started: {market} market, {mode} mode")
            return True
            
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            return False
    
    def stop_bot(self) -> bool:
        """Stop the trading bot"""
        try:
            self.is_running = False
            logger.info("Bot stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping bot: {e}")
            return False
    
    def _run_bot_loop(self):
        """Main bot trading loop"""
        while self.is_running:
            try:
                # Simulate trading cycle
                self._trading_cycle()
                
                # Update last update time
                self.last_update = datetime.now()
                
                # Emit real-time updates to web interface
                socketio.emit('bot_update', {
                    'status': 'running',
                    'market': self.current_market,
                    'mode': self.trading_mode,
                    'performance': self.performance,
                    'positions': len(self.positions),
                    'last_update': self.last_update.strftime('%H:%M:%S')
                })
                
                # Wait before next cycle
                time.sleep(30)  # 30 seconds between cycles
                
            except Exception as e:
                logger.error(f"Bot loop error: {e}")
                time.sleep(10)
    
    def _trading_cycle(self):
        """Execute one trading cycle"""
        try:
            market_config = self.markets[self.current_market]
            
            for symbol in market_config['symbols']:
                # Skip if already have position
                if symbol in self.positions:
                    continue
                
                # Get market data
                data = self._get_market_data(symbol, market_config)
                if not data:
                    continue
                
                # Analyze symbol
                signal = self._analyze_symbol(symbol, data, market_config)
                if signal:
                    # Execute trade
                    success = self._execute_trade(signal)
                    if success:
                        # Emit trade notification
                        socketio.emit('new_trade', {
                            'symbol': signal['symbol'],
                            'action': signal['action'],
                            'price': signal['entry_price'],
                            'confidence': signal['confidence'],
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        })
                
                time.sleep(1)  # Rate limiting
                
        except Exception as e:
            logger.error(f"Trading cycle error: {e}")
    
    def _get_market_data(self, symbol: str, market_config: Dict) -> Optional[Dict]:
        """Get market data for symbol"""
        try:
            # Construct Yahoo Finance symbol
            yahoo_symbol = symbol + market_config['suffix']
            
            # Try to get real data
            ticker = yf.Ticker(yahoo_symbol)
            hist = ticker.history(period="1d", interval="5m")
            
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                prev_close = hist['Close'].iloc[0] if len(hist) > 1 else current_price
                change = current_price - prev_close
                change_pct = (change / prev_close) * 100 if prev_close > 0 else 0
                volume = hist['Volume'].iloc[-1]
                
                return {
                    'symbol': symbol,
                    'price': float(current_price),
                    'change': float(change),
                    'change_pct': float(change_pct),
                    'volume': int(volume),
                    'currency': market_config['currency']
                }
            else:
                # Fallback to simulated data
                return self._get_fallback_data(symbol, market_config)
                
        except Exception as e:
            logger.error(f"Data error for {symbol}: {e}")
            return self._get_fallback_data(symbol, market_config)
    
    def _get_fallback_data(self, symbol: str, market_config: Dict) -> Dict:
        """Generate fallback market data"""
        base_prices = {
            'RELIANCE': 2485, 'TCS': 3245, 'HDFCBANK': 1598,
            'AAPL': 180, 'GOOGL': 140, 'MSFT': 350,
            'ASML.AS': 650, 'SAP.DE': 120,
            'BTC': 45000, 'ETH': 3000, 'BNB': 300
        }
        
        base_price = base_prices.get(symbol, 1000)
        change_pct = np.random.uniform(-3, 3)
        current_price = base_price * (1 + change_pct / 100)
        
        return {
            'symbol': symbol,
            'price': current_price,
            'change': current_price - base_price,
            'change_pct': change_pct,
            'volume': np.random.randint(100000, 1000000),
            'currency': market_config['currency']
        }
    
    def _analyze_symbol(self, symbol: str, data: Dict, market_config: Dict) -> Optional[Dict]:
        """Analyze symbol and generate trading signal"""
        try:
            change_pct = data['change_pct']
            
            # Simple analysis for demo
            technical_score = 0
            if abs(change_pct) > 2:
                technical_score += np.sign(change_pct) * 1.0
            
            sentiment_score = np.random.uniform(-0.5, 0.5)
            momentum_score = np.sign(change_pct) * min(abs(change_pct) / 3, 1.0)
            
            # Calculate confidence
            confidence = (abs(technical_score) + abs(sentiment_score) + abs(momentum_score)) / 3
            confidence = min(1.0, confidence)
            
            # Check confidence threshold
            if confidence < 0.4:
                return None
            
            # Determine action
            combined_score = technical_score + sentiment_score + momentum_score
            
            if combined_score > 0.8:
                action = "STRONG_BUY"
            elif combined_score > 0.4:
                action = "BUY"
            elif combined_score < -0.8:
                action = "STRONG_SELL"
            elif combined_score < -0.4:
                action = "SELL"
            else:
                return None
            
            # Calculate position size
            position_size = self.capital * 0.15 * confidence
            
            # Risk management
            current_price = data['price']
            if "BUY" in action:
                stop_loss = current_price * 0.98
                target = current_price * 1.06
            else:
                stop_loss = current_price * 1.02
                target = current_price * 0.94
            
            return {
                'symbol': symbol,
                'action': action,
                'confidence': confidence,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'target': target,
                'position_size': position_size,
                'currency': data['currency'],
                'market_data': data
            }
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
            return None
    
    def _execute_trade(self, signal: Dict) -> bool:
        """Execute trading signal"""
        try:
            symbol = signal['symbol']
            
            # Add to positions
            self.positions[symbol] = {
                'signal': signal,
                'entry_time': datetime.now(),
                'status': 'OPEN'
            }
            
            # Add to trades history
            trade_record = {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol,
                'action': signal['action'],
                'entry_price': signal['entry_price'],
                'confidence': signal['confidence'],
                'position_size': signal['position_size'],
                'currency': signal['currency'],
                'mode': self.trading_mode
            }
            self.trades.append(trade_record)
            
            # Update performance
            self.performance['total_trades'] += 1
            
            # Simulate P&L for demo
            if np.random.random() > 0.4:  # 60% win rate
                pnl = signal['position_size'] * 0.05  # 5% profit
                self.performance['winning_trades'] += 1
            else:
                pnl = -signal['position_size'] * 0.02  # 2% loss
            
            self.performance['total_pnl'] += pnl
            self.performance['daily_pnl'] += pnl
            self.performance['win_rate'] = (self.performance['winning_trades'] / 
                                          self.performance['total_trades']) * 100
            
            logger.info(f"Trade executed: {symbol} {signal['action']} at {signal['currency']} {signal['entry_price']:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Trade execution error: {e}")
            return False
    
    def get_status(self) -> Dict:
        """Get current bot status"""
        return {
            'is_running': self.is_running,
            'current_market': self.current_market,
            'trading_mode': self.trading_mode,
            'capital': self.capital,
            'positions': len(self.positions),
            'performance': self.performance,
            'last_update': self.last_update.strftime('%Y-%m-%d %H:%M:%S') if self.last_update else None
        }
    
    def get_positions(self) -> List[Dict]:
        """Get current positions"""
        positions_list = []
        for symbol, position in self.positions.items():
            signal = position['signal']
            positions_list.append({
                'symbol': symbol,
                'action': signal['action'],
                'entry_price': signal['entry_price'],
                'current_price': signal['entry_price'] * (1 + np.random.uniform(-0.02, 0.02)),  # Simulate price movement
                'confidence': signal['confidence'],
                'position_size': signal['position_size'],
                'currency': signal['currency'],
                'entry_time': position['entry_time'].strftime('%H:%M:%S'),
                'status': position['status']
            })
        return positions_list
    
    def get_trades_history(self, limit: int = 20) -> List[Dict]:
        """Get recent trades history"""
        return self.trades[-limit:] if self.trades else []

# Initialize bot engine
bot_engine = TradingBotEngine()

# ============================================================================
# WEB ROUTES
# ============================================================================

@app.route('/')
def dashboard():
    """Main dashboard page"""
    status = bot_engine.get_status()
    return render_template('dashboard.html', 
                         status=status, 
                         markets=bot_engine.markets)

@app.route('/start_bot', methods=['POST'])
def start_bot():
    """Start the trading bot"""
    market = request.form.get('market', 'indian')
    mode = request.form.get('mode', 'paper')
    
    success = bot_engine.start_bot(market, mode)
    
    if success:
        flash(f'Bot started successfully! Trading {market} market in {mode} mode.', 'success')
    else:
        flash('Failed to start bot. Bot may already be running.', 'error')
    
    return redirect(url_for('dashboard'))

@app.route('/stop_bot', methods=['POST'])
def stop_bot():
    """Stop the trading bot"""
    success = bot_engine.stop_bot()
    
    if success:
        flash('Bot stopped successfully!', 'success')
    else:
        flash('Failed to stop bot.', 'error')
    
    return redirect(url_for('dashboard'))

@app.route('/api/status')
def api_status():
    """API endpoint for bot status"""
    return jsonify(bot_engine.get_status())

@app.route('/api/positions')
def api_positions():
    """API endpoint for current positions"""
    return jsonify(bot_engine.get_positions())

@app.route('/api/trades')
def api_trades():
    """API endpoint for trades history"""
    limit = request.args.get('limit', 20, type=int)
    return jsonify(bot_engine.get_trades_history(limit))

@app.route('/positions')
def positions():
    """Positions page"""
    positions = bot_engine.get_positions()
    return render_template('positions.html', positions=positions)

@app.route('/trades')
def trades():
    """Trades history page"""
    trades = bot_engine.get_trades_history(50)
    return render_template('trades.html', trades=trades)

@app.route('/settings')
def settings():
    """Settings page"""
    return render_template('settings.html', 
                         markets=bot_engine.markets,
                         current_settings=bot_engine.get_status())

# ============================================================================
# WEBSOCKET EVENTS
# ============================================================================

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'status': 'Connected to Trading Bot'})
    # Send initial status
    emit('bot_update', bot_engine.get_status())

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected')

@socketio.on('request_update')
def handle_request_update():
    """Handle request for status update"""
    emit('bot_update', bot_engine.get_status())
    emit('positions_update', bot_engine.get_positions())
    emit('trades_update', bot_engine.get_trades_history(10))

# ============================================================================
# MAIN APPLICATION
# ============================================================================

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    print("🌐 ULTIMATE TRADING BOT - WEB INTERFACE")
    print("=" * 50)
    print("🚀 Starting web server...")
    print("📱 Access your bot at: http://localhost:5000")
    print("🌍 Or from any device on your network at: http://YOUR_IP:5000")
    print("⏹️  Press Ctrl+C to stop")
    print()
    
    # Run the web application
    socketio.run(app, 
                host='0.0.0.0',  # Allow access from any device on network
                port=5000, 
                debug=False,
                allow_unsafe_werkzeug=True)
