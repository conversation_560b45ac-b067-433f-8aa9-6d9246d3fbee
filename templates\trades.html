{% extends "base.html" %}

{% block title %}Trade History - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-history"></i> Trade History
            <span class="badge bg-primary ms-2">{{ trades|length }} Trades</span>
        </h1>
    </div>
</div>

{% if trades %}
<!-- Trade Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">{{ trades|length }}</div>
            <div class="metric-label">Total Trades</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-success">
            {% set buy_trades = trades | selectattr('action', 'match', '.*BUY.*') | list %}
            <div class="metric-value">{{ buy_trades|length }}</div>
            <div class="metric-label">Buy Trades</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-danger">
            {% set sell_trades = trades | selectattr('action', 'match', '.*SELL.*') | list %}
            <div class="metric-value">{{ sell_trades|length }}</div>
            <div class="metric-label">Sell Trades</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-info">
            {% set avg_confidence = (trades | sum(attribute='confidence') / trades|length * 100) if trades else 0 %}
            <div class="metric-value">{{ "%.1f"|format(avg_confidence) }}%</div>
            <div class="metric-label">Avg Confidence</div>
        </div>
    </div>
</div>

<!-- Trades Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-table"></i> All Trades</span>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="exportTrades()">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="refreshTrades()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="tradesTable">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Symbol</th>
                                <th>Market</th>
                                <th>Action</th>
                                <th>Entry Price</th>
                                <th>Position Size</th>
                                <th>Confidence</th>
                                <th>Mode</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trade in trades|reverse %}
                            <tr class="trade-row">
                                <td>
                                    <small>{{ trade.timestamp[:19] | replace('T', ' ') }}</small>
                                </td>
                                <td>
                                    <strong>{{ trade.symbol }}</strong>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ trade.market.upper() }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{% if 'BUY' in trade.action %}success{% else %}danger{% endif %}">
                                        {{ trade.action }}
                                    </span>
                                </td>
                                <td>
                                    {{ trade.currency }} {{ "%.2f"|format(trade.entry_price) }}
                                </td>
                                <td>
                                    {{ trade.currency }} {{ "%.2f"|format(trade.position_size) }}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress me-2" style="width: 50px; height: 6px;">
                                            <div class="progress-bar bg-{% if trade.confidence > 0.7 %}success{% elif trade.confidence > 0.5 %}warning{% else %}danger{% endif %}" 
                                                 style="width: {{ trade.confidence * 100 }}%"></div>
                                        </div>
                                        <small>{{ "%.1f"|format(trade.confidence * 100) }}%</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{% if trade.mode == 'PAPER' %}info{% else %}warning{% endif %}">
                                        {{ trade.mode }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success">EXECUTED</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Trade Analysis -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> Trade Distribution
            </div>
            <div class="card-body">
                <canvas id="tradeDistributionChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Confidence Distribution
            </div>
            <div class="card-body">
                <canvas id="confidenceChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- No Trades -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-history fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Trade History</h4>
                <p class="text-muted">Start the trading bot to begin generating trade history.</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Go to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
{% if trades %}
<script>
    // Trade Distribution Chart
    const tradeCtx = document.getElementById('tradeDistributionChart').getContext('2d');
    
    // Calculate trade distribution
    const buyTrades = {{ (trades | selectattr('action', 'match', '.*BUY.*') | list) | length }};
    const sellTrades = {{ (trades | selectattr('action', 'match', '.*SELL.*') | list) | length }};
    
    const tradeDistributionChart = new Chart(tradeCtx, {
        type: 'doughnut',
        data: {
            labels: ['Buy Trades', 'Sell Trades'],
            datasets: [{
                data: [buyTrades, sellTrades],
                backgroundColor: ['#28a745', '#dc3545'],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Confidence Distribution Chart
    const confidenceCtx = document.getElementById('confidenceChart').getContext('2d');
    
    // Calculate confidence ranges
    const trades = {{ trades | tojson }};
    const confidenceRanges = {
        'High (70-100%)': 0,
        'Medium (50-70%)': 0,
        'Low (30-50%)': 0,
        'Very Low (0-30%)': 0
    };
    
    trades.forEach(trade => {
        const confidence = trade.confidence * 100;
        if (confidence >= 70) confidenceRanges['High (70-100%)']++;
        else if (confidence >= 50) confidenceRanges['Medium (50-70%)']++;
        else if (confidence >= 30) confidenceRanges['Low (30-50%)']++;
        else confidenceRanges['Very Low (0-30%)']++;
    });
    
    const confidenceChart = new Chart(confidenceCtx, {
        type: 'bar',
        data: {
            labels: Object.keys(confidenceRanges),
            datasets: [{
                label: 'Number of Trades',
                data: Object.values(confidenceRanges),
                backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Export trades function
    function exportTrades() {
        const csvContent = "data:text/csv;charset=utf-8," 
            + "Timestamp,Symbol,Market,Action,Entry Price,Position Size,Confidence,Mode\n"
            + trades.map(trade => 
                `${trade.timestamp},${trade.symbol},${trade.market},${trade.action},${trade.entry_price},${trade.position_size},${trade.confidence},${trade.mode}`
            ).join("\n");
        
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "trading_bot_history.csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    // Refresh trades function
    function refreshTrades() {
        location.reload();
    }
    
    // Real-time trade updates
    socket.on('new_trade', function(data) {
        // Add new trade to the table
        const tbody = document.querySelector('#tradesTable tbody');
        const newRow = document.createElement('tr');
        newRow.className = 'trade-row';
        newRow.innerHTML = `
            <td><small>${new Date().toISOString().slice(0, 19).replace('T', ' ')}</small></td>
            <td><strong>${data.symbol}</strong></td>
            <td><span class="badge bg-secondary">${data.market ? data.market.toUpperCase() : 'UNKNOWN'}</span></td>
            <td><span class="badge bg-${data.action.includes('BUY') ? 'success' : 'danger'}">${data.action}</span></td>
            <td>${data.price.toFixed(2)}</td>
            <td>-</td>
            <td>
                <div class="d-flex align-items-center">
                    <div class="progress me-2" style="width: 50px; height: 6px;">
                        <div class="progress-bar bg-${data.confidence > 0.7 ? 'success' : data.confidence > 0.5 ? 'warning' : 'danger'}" 
                             style="width: ${data.confidence * 100}%"></div>
                    </div>
                    <small>${(data.confidence * 100).toFixed(1)}%</small>
                </div>
            </td>
            <td><span class="badge bg-info">PAPER</span></td>
            <td><span class="badge bg-success">EXECUTED</span></td>
        `;
        
        // Add to top of table
        tbody.insertBefore(newRow, tbody.firstChild);
        
        // Highlight new row
        newRow.style.backgroundColor = '#e3f2fd';
        setTimeout(() => {
            newRow.style.backgroundColor = '';
        }, 3000);
    });
</script>
{% endif %}
{% endblock %}
