{% extends "base.html" %}

{% block title %}Analytics - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-bar"></i> Analytics & Reports
            <span class="badge bg-primary ms-2">Advanced</span>
        </h1>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value">75.2%</div>
            <div class="metric-label">Win Rate</div>
            <small class="text-light">↑ 5.2% from last week</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-success">
            <div class="metric-value">₹1,247</div>
            <div class="metric-label">Total P&L</div>
            <small class="text-light">↑ ₹247 this month</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-info">
            <div class="metric-value">1.85</div>
            <div class="metric-label">Sharpe Ratio</div>
            <small class="text-light">Excellent performance</small>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-warning">
            <div class="metric-value">-2.1%</div>
            <div class="metric-label">Max Drawdown</div>
            <small class="text-light">Low risk profile</small>
        </div>
    </div>
</div>

<!-- Performance Charts -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-chart-line"></i> Performance Over Time</span>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="updateChart('7d')">7D</button>
                    <button type="button" class="btn btn-outline-primary" onclick="updateChart('30d')">30D</button>
                    <button type="button" class="btn btn-outline-primary" onclick="updateChart('90d')">90D</button>
                    <button type="button" class="btn btn-outline-primary" onclick="updateChart('1y')">1Y</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-pie-chart"></i> Market Distribution
            </div>
            <div class="card-body">
                <canvas id="marketDistributionChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Trading Statistics -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Trading Statistics
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h4 class="text-primary">156</h4>
                            <small class="text-muted">Total Trades</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h4 class="text-success">117</h4>
                            <small class="text-muted">Winning Trades</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h4 class="text-danger">39</h4>
                            <small class="text-muted">Losing Trades</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center mb-3">
                            <h4 class="text-info">4.2</h4>
                            <small class="text-muted">Avg Daily Trades</small>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Average Win:</span>
                        <span class="text-success">₹18.50</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Average Loss:</span>
                        <span class="text-danger">₹-8.20</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Profit Factor:</span>
                        <span class="text-primary">2.26</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Best Trade:</span>
                        <span class="text-success">₹85.40</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Worst Trade:</span>
                        <span class="text-danger">₹-22.10</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock"></i> Time Analysis
            </div>
            <div class="card-body">
                <canvas id="timeAnalysisChart" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Market Performance -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-globe"></i> Market Performance Breakdown
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Market</th>
                                <th>Total Trades</th>
                                <th>Win Rate</th>
                                <th>Total P&L</th>
                                <th>Avg Trade</th>
                                <th>Best Trade</th>
                                <th>Status</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key, market in markets.items() %}
                            <tr>
                                <td>
                                    <strong>{{ market.name }}</strong>
                                    <br><small class="text-muted">{{ market.currency }}</small>
                                </td>
                                <td>{{ [45, 32, 28, 51, 0][loop.index0] }}</td>
                                <td>
                                    {% set win_rates = [78.2, 71.9, 82.1, 68.6, 0] %}
                                    <span class="text-{% if win_rates[loop.index0] > 75 %}success{% elif win_rates[loop.index0] > 65 %}warning{% else %}danger{% endif %}">
                                        {{ win_rates[loop.index0] }}%
                                    </span>
                                </td>
                                <td>
                                    {% set pnls = [485.20, 312.80, 298.50, 150.70, 0] %}
                                    <span class="text-{% if pnls[loop.index0] > 0 %}success{% else %}danger{% endif %}">
                                        {{ market.currency }} {{ pnls[loop.index0] }}
                                    </span>
                                </td>
                                <td>
                                    {% set avg_trades = [10.78, 9.78, 10.66, 2.95, 0] %}
                                    {{ market.currency }} {{ avg_trades[loop.index0] }}
                                </td>
                                <td>
                                    {% set best_trades = [85.40, 45.20, 52.30, 18.90, 0] %}
                                    <span class="text-success">{{ market.currency }} {{ best_trades[loop.index0] }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-{% if market.status == 'OPEN' %}success{% elif '24/7' in market.status %}info{% else %}secondary{% endif %}">
                                        {{ market.status }}
                                    </span>
                                </td>
                                <td>
                                    {% set performances = [92, 85, 88, 72, 0] %}
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-{% if performances[loop.index0] > 85 %}success{% elif performances[loop.index0] > 70 %}warning{% else %}danger{% endif %}" 
                                             style="width: {{ performances[loop.index0] }}%"></div>
                                    </div>
                                    <small>{{ performances[loop.index0] }}/100</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Risk Analysis -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Risk Analysis
            </div>
            <div class="card-body">
                <canvas id="riskAnalysisChart" height="250"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-area"></i> Confidence Distribution
            </div>
            <div class="card-body">
                <canvas id="confidenceDistributionChart" height="250"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Export and Reports -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-download"></i> Export & Reports
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Performance Reports</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportReport('daily')">
                                <i class="fas fa-file-pdf"></i> Daily Performance Report
                            </button>
                            <button class="btn btn-outline-success" onclick="exportReport('weekly')">
                                <i class="fas fa-file-excel"></i> Weekly Summary (Excel)
                            </button>
                            <button class="btn btn-outline-info" onclick="exportReport('monthly')">
                                <i class="fas fa-file-csv"></i> Monthly Analysis (CSV)
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Data Export</h6>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-secondary" onclick="exportData('trades')">
                                <i class="fas fa-database"></i> Export All Trades
                            </button>
                            <button class="btn btn-outline-warning" onclick="exportData('positions')">
                                <i class="fas fa-chart-line"></i> Export Positions History
                            </button>
                            <button class="btn btn-outline-dark" onclick="exportData('settings')">
                                <i class="fas fa-cog"></i> Export Bot Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Performance Chart
    const performanceCtx = document.getElementById('performanceChart').getContext('2d');
    const performanceChart = new Chart(performanceCtx, {
        type: 'line',
        data: {
            labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
            datasets: [{
                label: 'Cumulative P&L',
                data: {{ performance_data.daily_pnl }},
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Win Rate',
                data: {{ performance_data.win_rate_history }},
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'P&L (₹)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Win Rate (%)'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
    
    // Market Distribution Chart
    const marketCtx = document.getElementById('marketDistributionChart').getContext('2d');
    const marketChart = new Chart(marketCtx, {
        type: 'doughnut',
        data: {
            labels: ['Indian', 'US', 'European', 'Crypto', 'Forex'],
            datasets: [{
                data: [45, 32, 28, 51, 0],
                backgroundColor: ['#007bff', '#28a745', '#ffc107', '#17a2b8', '#6c757d'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Time Analysis Chart
    const timeCtx = document.getElementById('timeAnalysisChart').getContext('2d');
    const timeChart = new Chart(timeCtx, {
        type: 'bar',
        data: {
            labels: ['9-10', '10-11', '11-12', '12-13', '13-14', '14-15', '15-16'],
            datasets: [{
                label: 'Trades per Hour',
                data: [8, 12, 15, 10, 18, 14, 6],
                backgroundColor: 'rgba(0, 123, 255, 0.8)',
                borderColor: '#007bff',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Trades'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Trading Hours'
                    }
                }
            }
        }
    });
    
    // Risk Analysis Chart
    const riskCtx = document.getElementById('riskAnalysisChart').getContext('2d');
    const riskChart = new Chart(riskCtx, {
        type: 'radar',
        data: {
            labels: ['Volatility', 'Drawdown', 'Correlation', 'Liquidity', 'Concentration'],
            datasets: [{
                label: 'Risk Profile',
                data: [65, 25, 45, 85, 35],
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                borderColor: 'rgba(255, 99, 132, 1)',
                pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                pointBorderColor: '#fff',
                pointHoverBackgroundColor: '#fff',
                pointHoverBorderColor: 'rgba(255, 99, 132, 1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 100
                }
            }
        }
    });
    
    // Confidence Distribution Chart
    const confidenceCtx = document.getElementById('confidenceDistributionChart').getContext('2d');
    const confidenceChart = new Chart(confidenceCtx, {
        type: 'bar',
        data: {
            labels: ['30-40%', '40-50%', '50-60%', '60-70%', '70-80%', '80-90%', '90-100%'],
            datasets: [{
                label: 'Number of Trades',
                data: [2, 8, 15, 28, 35, 22, 8],
                backgroundColor: [
                    '#dc3545', '#fd7e14', '#ffc107', 
                    '#20c997', '#28a745', '#17a2b8', '#6f42c1'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Trades'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Confidence Range'
                    }
                }
            }
        }
    });
    
    // Chart update function
    function updateChart(period) {
        // Update active button
        document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        // Update chart data based on period
        let newData;
        switch(period) {
            case '7d':
                newData = {{ performance_data.daily_pnl }};
                break;
            case '30d':
                newData = [0, 5, 8, 12, 18, 25, 30, 28, 35, 40, 38, 45, 50, 48, 55, 60, 58, 65, 70, 68, 75, 80, 78, 85, 90, 88, 95, 100, 98, 105];
                break;
            case '90d':
                newData = [0, 10, 15, 25, 35, 50, 65, 80, 95, 110, 125, 140];
                break;
            case '1y':
                newData = [0, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500, 550];
                break;
        }
        
        performanceChart.data.datasets[0].data = newData;
        performanceChart.update();
    }
    
    // Export functions
    function exportReport(type) {
        alert(`Exporting ${type} report... (Feature will be implemented)`);
    }
    
    function exportData(type) {
        alert(`Exporting ${type} data... (Feature will be implemented)`);
    }
</script>
{% endblock %}
