#!/usr/bin/env python3
"""
Robust & Adaptive Decision Making Engine
Advanced trading logic with uncertainty handling
"""
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class MarketRegime(Enum):
    BULL_TRENDING = "bull_trending"
    BEAR_TRENDING = "bear_trending"
    SIDEWAYS_LOW_VOL = "sideways_low_vol"
    SIDEWAYS_HIGH_VOL = "sideways_high_vol"
    CRISIS = "crisis"
    RECOVERY = "recovery"

class VolatilityRegime(Enum):
    VERY_LOW = "very_low"    # <0.5%
    LOW = "low"              # 0.5-1%
    NORMAL = "normal"        # 1-2%
    HIGH = "high"            # 2-4%
    EXTREME = "extreme"      # >4%

@dataclass
class MarketContext:
    regime: MarketRegime
    volatility: VolatilityRegime
    trend_strength: float
    momentum: float
    uncertainty_level: float
    session_type: str  # "pre_market", "opening", "regular", "closing", "after_hours"
    
@dataclass
class RobustSignal:
    symbol: str
    signal_type: str
    base_confidence: float
    adjusted_confidence: float
    entry_price: float
    dynamic_stop_loss: float
    dynamic_target: float
    position_size_pct: float
    uncertainty_discount: float
    regime_adjustment: float
    reasoning: List[str]

class RobustDecisionEngine:
    """Advanced decision engine with uncertainty handling"""
    
    def __init__(self):
        self.price_history = {}  # Store real price history
        self.volatility_cache = {}
        self.regime_cache = {}
        self.uncertainty_factors = {}
        
    def update_price_history(self, symbol: str, price_data: Dict):
        """Update real price history for proper analysis"""
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append({
            'timestamp': datetime.now(),
            'price': price_data['price'],
            'change_pct': price_data['change_pct'],
            'volume': price_data.get('volume', 0)
        })
        
        # Keep only last 100 data points
        if len(self.price_history[symbol]) > 100:
            self.price_history[symbol] = self.price_history[symbol][-100:]
    
    def calculate_real_volatility(self, symbol: str, periods: int = 20) -> float:
        """Calculate real historical volatility"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < periods:
            return 0.02  # Default 2% volatility
        
        prices = [p['price'] for p in self.price_history[symbol][-periods:]]
        returns = [math.log(prices[i]/prices[i-1]) for i in range(1, len(prices))]
        
        if not returns:
            return 0.02
        
        volatility = np.std(returns) * math.sqrt(252)  # Annualized
        return max(0.005, min(0.5, volatility))  # Clamp between 0.5% and 50%
    
    def detect_market_regime(self, symbol: str) -> MarketContext:
        """Detect current market regime and context"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < 20:
            return MarketContext(
                regime=MarketRegime.SIDEWAYS_LOW_VOL,
                volatility=VolatilityRegime.NORMAL,
                trend_strength=0.0,
                momentum=0.0,
                uncertainty_level=0.5,
                session_type=self._get_session_type()
            )
        
        history = self.price_history[symbol]
        prices = [p['price'] for p in history[-20:]]
        changes = [p['change_pct'] for p in history[-10:]]
        
        # Calculate trend strength
        trend_strength = self._calculate_trend_strength(prices)
        
        # Calculate momentum
        momentum = np.mean(changes) if changes else 0.0
        
        # Calculate volatility
        volatility = self.calculate_real_volatility(symbol)
        vol_regime = self._classify_volatility(volatility)
        
        # Detect market regime
        regime = self._classify_market_regime(trend_strength, momentum, volatility)
        
        # Calculate uncertainty level
        uncertainty = self._calculate_uncertainty(symbol, volatility, changes)
        
        return MarketContext(
            regime=regime,
            volatility=vol_regime,
            trend_strength=trend_strength,
            momentum=momentum,
            uncertainty_level=uncertainty,
            session_type=self._get_session_type()
        )
    
    def _calculate_trend_strength(self, prices: List[float]) -> float:
        """Calculate trend strength using linear regression"""
        if len(prices) < 10:
            return 0.0
        
        x = np.arange(len(prices))
        y = np.array(prices)
        
        # Linear regression
        slope, intercept = np.polyfit(x, y, 1)
        
        # Calculate R-squared
        y_pred = slope * x + intercept
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        
        if ss_tot == 0:
            return 0.0
        
        r_squared = 1 - (ss_res / ss_tot)
        
        # Trend strength = R-squared * direction
        direction = 1 if slope > 0 else -1
        return r_squared * direction
    
    def _classify_volatility(self, volatility: float) -> VolatilityRegime:
        """Classify volatility regime"""
        if volatility < 0.005:
            return VolatilityRegime.VERY_LOW
        elif volatility < 0.01:
            return VolatilityRegime.LOW
        elif volatility < 0.02:
            return VolatilityRegime.NORMAL
        elif volatility < 0.04:
            return VolatilityRegime.HIGH
        else:
            return VolatilityRegime.EXTREME
    
    def _classify_market_regime(self, trend_strength: float, momentum: float, volatility: float) -> MarketRegime:
        """Classify market regime based on multiple factors"""
        abs_trend = abs(trend_strength)
        
        # Crisis detection
        if volatility > 0.04 and abs(momentum) > 3:
            return MarketRegime.CRISIS
        
        # Strong trending markets
        if abs_trend > 0.7:
            if trend_strength > 0:
                return MarketRegime.BULL_TRENDING
            else:
                return MarketRegime.BEAR_TRENDING
        
        # Sideways markets
        if abs_trend < 0.3:
            if volatility > 0.02:
                return MarketRegime.SIDEWAYS_HIGH_VOL
            else:
                return MarketRegime.SIDEWAYS_LOW_VOL
        
        # Recovery detection
        if trend_strength > 0.3 and momentum > 1 and volatility > 0.02:
            return MarketRegime.RECOVERY
        
        return MarketRegime.SIDEWAYS_LOW_VOL
    
    def _calculate_uncertainty(self, symbol: str, volatility: float, recent_changes: List[float]) -> float:
        """Calculate market uncertainty level"""
        uncertainty = 0.3  # Base uncertainty
        
        # Volatility contribution
        if volatility > 0.03:
            uncertainty += 0.2
        elif volatility < 0.01:
            uncertainty -= 0.1
        
        # Consistency of recent moves
        if recent_changes:
            change_std = np.std(recent_changes)
            if change_std > 2:
                uncertainty += 0.2
            elif change_std < 0.5:
                uncertainty -= 0.1
        
        # Time-based uncertainty
        session = self._get_session_type()
        if session in ["pre_market", "after_hours"]:
            uncertainty += 0.2
        elif session == "opening":
            uncertainty += 0.1
        
        return max(0.1, min(0.9, uncertainty))
    
    def _get_session_type(self) -> str:
        """Determine current trading session"""
        now = datetime.now()
        hour = now.hour
        
        if 4 <= hour < 9:
            return "pre_market"
        elif 9 <= hour < 10:
            return "opening"
        elif 10 <= hour < 15:
            return "regular"
        elif 15 <= hour < 16:
            return "closing"
        else:
            return "after_hours"
    
    def calculate_dynamic_thresholds(self, context: MarketContext) -> Dict[str, float]:
        """Calculate dynamic thresholds based on market context"""
        base_momentum_threshold = 1.0
        base_volatility_threshold = 2.0
        
        # Adjust for volatility regime
        vol_multiplier = {
            VolatilityRegime.VERY_LOW: 0.5,
            VolatilityRegime.LOW: 0.7,
            VolatilityRegime.NORMAL: 1.0,
            VolatilityRegime.HIGH: 1.5,
            VolatilityRegime.EXTREME: 2.0
        }[context.volatility]
        
        # Adjust for market regime
        regime_multiplier = {
            MarketRegime.BULL_TRENDING: 0.8,
            MarketRegime.BEAR_TRENDING: 0.8,
            MarketRegime.SIDEWAYS_LOW_VOL: 1.2,
            MarketRegime.SIDEWAYS_HIGH_VOL: 1.0,
            MarketRegime.CRISIS: 2.0,
            MarketRegime.RECOVERY: 0.9
        }[context.regime]
        
        # Adjust for session
        session_multiplier = {
            "pre_market": 1.5,
            "opening": 1.2,
            "regular": 1.0,
            "closing": 1.1,
            "after_hours": 1.5
        }[context.session_type]
        
        final_multiplier = vol_multiplier * regime_multiplier * session_multiplier
        
        return {
            'momentum_threshold': base_momentum_threshold * final_multiplier,
            'volatility_threshold': base_volatility_threshold * final_multiplier,
            'confidence_threshold': 0.75 + (context.uncertainty_level * 0.1)
        }
    
    def calculate_dynamic_risk_params(self, context: MarketContext, base_price: float) -> Dict[str, float]:
        """Calculate dynamic stop loss and target based on context"""
        base_stop_pct = 0.02  # 2%
        base_target_pct = 0.06  # 6%
        
        # Adjust for volatility
        vol_adjustment = {
            VolatilityRegime.VERY_LOW: 0.5,
            VolatilityRegime.LOW: 0.7,
            VolatilityRegime.NORMAL: 1.0,
            VolatilityRegime.HIGH: 1.5,
            VolatilityRegime.EXTREME: 2.5
        }[context.volatility]
        
        # Adjust for regime
        regime_adjustment = {
            MarketRegime.BULL_TRENDING: 0.8,
            MarketRegime.BEAR_TRENDING: 0.8,
            MarketRegime.SIDEWAYS_LOW_VOL: 1.2,
            MarketRegime.SIDEWAYS_HIGH_VOL: 1.0,
            MarketRegime.CRISIS: 2.0,
            MarketRegime.RECOVERY: 1.1
        }[context.regime]
        
        # Adjust for uncertainty
        uncertainty_adjustment = 1 + (context.uncertainty_level * 0.5)
        
        final_adjustment = vol_adjustment * regime_adjustment * uncertainty_adjustment
        
        dynamic_stop_pct = base_stop_pct * final_adjustment
        dynamic_target_pct = base_target_pct * final_adjustment
        
        # Ensure minimum risk-reward ratio of 1:2
        if dynamic_target_pct < dynamic_stop_pct * 2:
            dynamic_target_pct = dynamic_stop_pct * 2
        
        return {
            'stop_loss_pct': min(0.1, dynamic_stop_pct),  # Max 10% stop
            'target_pct': min(0.2, dynamic_target_pct),   # Max 20% target
            'position_size_pct': self._calculate_position_size(context)
        }
    
    def _calculate_position_size(self, context: MarketContext) -> float:
        """Calculate dynamic position size based on uncertainty"""
        base_size = 0.15  # 15%
        
        # Reduce size in uncertain conditions
        uncertainty_reduction = context.uncertainty_level * 0.5
        
        # Reduce size in extreme volatility
        if context.volatility == VolatilityRegime.EXTREME:
            uncertainty_reduction += 0.3
        elif context.volatility == VolatilityRegime.HIGH:
            uncertainty_reduction += 0.1
        
        # Reduce size in crisis
        if context.regime == MarketRegime.CRISIS:
            uncertainty_reduction += 0.4
        
        final_size = base_size * (1 - uncertainty_reduction)
        return max(0.05, min(0.25, final_size))  # Between 5% and 25%

    def analyze_with_robust_logic(self, symbol: str, price_data: Dict) -> Optional[RobustSignal]:
        """Advanced signal analysis with uncertainty handling"""
        # Update price history
        self.update_price_history(symbol, price_data)

        # Get market context
        context = self.detect_market_regime(symbol)

        # Get dynamic thresholds
        thresholds = self.calculate_dynamic_thresholds(context)

        # Initialize signal analysis
        signals = []
        confidence_factors = []
        reasoning = []

        current_price = price_data['price']
        change_pct = price_data['change_pct']

        # 1. ADAPTIVE MOMENTUM ANALYSIS
        momentum_threshold = thresholds['momentum_threshold']

        if abs(change_pct) > thresholds['volatility_threshold']:
            confidence_factors.append(('high_volatility', 0.1))
            reasoning.append(f"High volatility detected: {abs(change_pct):.2f}%")

        if change_pct > momentum_threshold:
            signals.append("BUY")
            strength = min(change_pct / momentum_threshold, 3.0)  # Cap at 3x
            confidence_factors.append(('bullish_momentum', 0.15 * strength))
            reasoning.append(f"Strong bullish momentum: {change_pct:.2f}% > {momentum_threshold:.2f}%")

        elif change_pct < -momentum_threshold:
            signals.append("SELL")
            strength = min(abs(change_pct) / momentum_threshold, 3.0)
            confidence_factors.append(('bearish_momentum', 0.15 * strength))
            reasoning.append(f"Strong bearish momentum: {change_pct:.2f}% < -{momentum_threshold:.2f}%")

        # 2. REAL RSI ANALYSIS (if enough history)
        if len(self.price_history[symbol]) >= 14:
            rsi = self._calculate_real_rsi(symbol)

            # Dynamic RSI thresholds based on volatility
            oversold_threshold = 30 - (context.volatility.value * 5)
            overbought_threshold = 70 + (context.volatility.value * 5)

            if rsi < oversold_threshold:
                signals.append("BUY")
                oversold_strength = (oversold_threshold - rsi) / oversold_threshold
                confidence_factors.append(('oversold_rsi', 0.2 * oversold_strength))
                reasoning.append(f"Oversold RSI: {rsi:.1f} < {oversold_threshold:.1f}")

            elif rsi > overbought_threshold:
                signals.append("SELL")
                overbought_strength = (rsi - overbought_threshold) / (100 - overbought_threshold)
                confidence_factors.append(('overbought_rsi', 0.2 * overbought_strength))
                reasoning.append(f"Overbought RSI: {rsi:.1f} > {overbought_threshold:.1f}")

        # 3. TREND ALIGNMENT ANALYSIS
        if abs(context.trend_strength) > 0.5:
            if context.trend_strength > 0 and "BUY" in signals:
                confidence_factors.append(('trend_alignment', 0.15))
                reasoning.append(f"Bullish trend alignment: {context.trend_strength:.2f}")
            elif context.trend_strength < 0 and "SELL" in signals:
                confidence_factors.append(('trend_alignment', 0.15))
                reasoning.append(f"Bearish trend alignment: {context.trend_strength:.2f}")

        # 4. MARKET REGIME ADJUSTMENT
        regime_bonus = self._get_regime_confidence_bonus(context.regime, signals)
        if regime_bonus != 0:
            confidence_factors.append(('regime_bonus', regime_bonus))
            reasoning.append(f"Market regime adjustment: {context.regime.value}")

        # 5. DETERMINE FINAL SIGNAL
        if not signals:
            return None

        buy_count = signals.count("BUY")
        sell_count = signals.count("SELL")

        if buy_count > sell_count:
            signal_type = "BUY"
        elif sell_count > buy_count:
            signal_type = "SELL"
        else:
            return None  # Conflicting signals

        # 6. CALCULATE CONFIDENCE
        base_confidence = 0.5
        total_confidence_boost = sum(factor[1] for factor in confidence_factors)
        raw_confidence = base_confidence + total_confidence_boost

        # 7. APPLY UNCERTAINTY DISCOUNT
        uncertainty_discount = context.uncertainty_level * 0.3
        adjusted_confidence = raw_confidence * (1 - uncertainty_discount)

        # 8. CHECK CONFIDENCE THRESHOLD
        if adjusted_confidence < thresholds['confidence_threshold']:
            reasoning.append(f"Confidence too low: {adjusted_confidence:.2f} < {thresholds['confidence_threshold']:.2f}")
            return None

        # 9. CALCULATE DYNAMIC RISK PARAMETERS
        risk_params = self.calculate_dynamic_risk_params(context, current_price)

        if signal_type == "BUY":
            stop_loss = current_price * (1 - risk_params['stop_loss_pct'])
            target = current_price * (1 + risk_params['target_pct'])
        else:
            stop_loss = current_price * (1 + risk_params['stop_loss_pct'])
            target = current_price * (1 - risk_params['target_pct'])

        return RobustSignal(
            symbol=symbol,
            signal_type=signal_type,
            base_confidence=raw_confidence,
            adjusted_confidence=adjusted_confidence,
            entry_price=current_price,
            dynamic_stop_loss=stop_loss,
            dynamic_target=target,
            position_size_pct=risk_params['position_size_pct'],
            uncertainty_discount=uncertainty_discount,
            regime_adjustment=regime_bonus,
            reasoning=reasoning
        )

    def _calculate_real_rsi(self, symbol: str, period: int = 14) -> float:
        """Calculate real RSI from price history"""
        if len(self.price_history[symbol]) < period + 1:
            return 50.0

        prices = [p['price'] for p in self.price_history[symbol][-(period+1):]]
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]

        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]

        avg_gain = sum(gains) / period
        avg_loss = sum(losses) / period

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def _get_regime_confidence_bonus(self, regime: MarketRegime, signals: List[str]) -> float:
        """Get confidence bonus based on market regime"""
        regime_bonuses = {
            MarketRegime.BULL_TRENDING: {"BUY": 0.1, "SELL": -0.05},
            MarketRegime.BEAR_TRENDING: {"BUY": -0.05, "SELL": 0.1},
            MarketRegime.SIDEWAYS_LOW_VOL: {"BUY": 0.0, "SELL": 0.0},
            MarketRegime.SIDEWAYS_HIGH_VOL: {"BUY": 0.05, "SELL": 0.05},
            MarketRegime.CRISIS: {"BUY": -0.2, "SELL": 0.05},
            MarketRegime.RECOVERY: {"BUY": 0.15, "SELL": -0.1}
        }

        if "BUY" in signals and "SELL" not in signals:
            return regime_bonuses[regime].get("BUY", 0.0)
        elif "SELL" in signals and "BUY" not in signals:
            return regime_bonuses[regime].get("SELL", 0.0)
        else:
            return 0.0
