"""
Backtesting framework for testing trading strategies
"""
import pytest
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from technical_analysis import TechnicalAnalyzer, TechnicalSignal
from risk_manager import RiskManager
from config import trading_config

class BacktestEngine:
    """Simple backtesting engine for strategy validation"""
    
    def __init__(self, initial_capital=100000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        
        self.analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
    
    def create_sample_data(self, symbol, days=30, start_price=1000):
        """Create sample OHLCV data for backtesting"""
        np.random.seed(42)  # For reproducible results
        
        # Generate minute-by-minute data for trading hours
        trading_minutes_per_day = 375  # 9:15 AM to 3:30 PM = 6h 15m = 375 minutes
        total_minutes = days * trading_minutes_per_day
        
        dates = []
        current_date = datetime(2024, 1, 1, 9, 15)  # Start at market open
        
        for day in range(days):
            for minute in range(trading_minutes_per_day):
                dates.append(current_date + timedelta(days=day, minutes=minute))
        
        # Generate price data with realistic patterns
        prices = [start_price]
        volatility = 0.02  # 2% daily volatility
        
        for i in range(1, total_minutes):
            # Add some trend and mean reversion
            trend = 0.0001 if i % 1000 < 500 else -0.0001  # Alternating trends
            noise = np.random.normal(0, volatility / np.sqrt(trading_minutes_per_day))
            
            price_change = trend + noise
            new_price = prices[-1] * (1 + price_change)
            prices.append(max(new_price, 1))  # Ensure positive prices
        
        # Create OHLCV data
        df_data = []
        for i, (date, price) in enumerate(zip(dates, prices)):
            # Simulate intraday high/low
            high_factor = 1 + abs(np.random.normal(0, 0.003))
            low_factor = 1 - abs(np.random.normal(0, 0.003))
            
            df_data.append({
                'timestamp': date,
                'open': price,
                'high': price * high_factor,
                'low': price * low_factor,
                'close': price,
                'volume': np.random.randint(50000, 200000)
            })
        
        df = pd.DataFrame(df_data)
        df.set_index('timestamp', inplace=True)
        df.attrs['symbol'] = symbol
        
        return df
    
    def run_backtest(self, symbol, strategy_name="all", days=30):
        """Run backtest for a specific symbol and strategy"""
        # Create sample data
        data = self.create_sample_data(symbol, days)
        
        # Reset state
        self.current_capital = self.initial_capital
        self.positions = {}
        self.trades = []
        self.equity_curve = []
        
        print(f"Running backtest for {symbol} - {days} days")
        print(f"Initial Capital: ₹{self.initial_capital:,.2f}")
        
        # Process each time period
        for i in range(50, len(data)):  # Start after enough data for indicators
            current_time = data.index[i]
            current_data = data.iloc[:i+1]  # Data up to current time
            current_price = data.iloc[i]['close']
            
            # Check for exit conditions first
            self._check_exits(current_price, current_time)
            
            # Generate new signals if no position
            if symbol not in self.positions:
                signals = self.analyzer.analyze_stock(current_data, symbol)
                
                if signals:
                    confluence_signal = self.analyzer.get_confluence_signal(signals)
                    
                    if confluence_signal and confluence_signal.confidence > 0.6:
                        self._execute_signal(confluence_signal, current_time)
            
            # Record equity
            portfolio_value = self._calculate_portfolio_value(current_price)
            self.equity_curve.append({
                'timestamp': current_time,
                'portfolio_value': portfolio_value,
                'cash': self.current_capital
            })
        
        # Close any remaining positions
        if symbol in self.positions:
            final_price = data.iloc[-1]['close']
            self._close_position(symbol, final_price, data.index[-1], "Backtest End")
        
        return self._generate_results()
    
    def _execute_signal(self, signal, timestamp):
        """Execute a trading signal"""
        symbol = signal.symbol
        
        # Calculate position size
        position_size = self.risk_manager.calculate_position_size(
            signal.entry_price, signal.stop_loss
        )
        
        if position_size <= 0:
            return
        
        # Check if we have enough capital
        position_value = position_size * signal.entry_price
        if position_value > self.current_capital:
            position_size = int(self.current_capital / signal.entry_price)
            position_value = position_size * signal.entry_price
        
        if position_size <= 0:
            return
        
        # Execute trade
        self.positions[symbol] = {
            'quantity': position_size,
            'entry_price': signal.entry_price,
            'entry_time': timestamp,
            'side': signal.signal_type,
            'stop_loss': signal.stop_loss,
            'target': signal.target,
            'strategy': signal.strategy
        }
        
        self.current_capital -= position_value
        
        print(f"ENTRY: {timestamp.strftime('%Y-%m-%d %H:%M')} {symbol} {signal.signal_type} "
              f"{position_size} @ ₹{signal.entry_price:.2f} Strategy: {signal.strategy}")
    
    def _check_exits(self, current_price, timestamp):
        """Check for exit conditions"""
        symbols_to_exit = []
        
        for symbol, position in self.positions.items():
            exit_reason = None
            
            # Check stop loss
            if position['side'] == 'BUY' and current_price <= position['stop_loss']:
                exit_reason = "Stop Loss"
            elif position['side'] == 'SELL' and current_price >= position['stop_loss']:
                exit_reason = "Stop Loss"
            
            # Check target
            elif position['side'] == 'BUY' and current_price >= position['target']:
                exit_reason = "Target Hit"
            elif position['side'] == 'SELL' and current_price <= position['target']:
                exit_reason = "Target Hit"
            
            # Check time-based exit (end of day)
            elif timestamp.hour >= 15 and timestamp.minute >= 20:
                exit_reason = "End of Day"
            
            if exit_reason:
                symbols_to_exit.append((symbol, exit_reason))
        
        # Execute exits
        for symbol, reason in symbols_to_exit:
            self._close_position(symbol, current_price, timestamp, reason)
    
    def _close_position(self, symbol, exit_price, timestamp, reason):
        """Close a position"""
        if symbol not in self.positions:
            return
        
        position = self.positions[symbol]
        
        # Calculate P&L
        if position['side'] == 'BUY':
            pnl = (exit_price - position['entry_price']) * position['quantity']
        else:  # SELL
            pnl = (position['entry_price'] - exit_price) * position['quantity']
        
        # Update capital
        exit_value = position['quantity'] * exit_price
        self.current_capital += exit_value
        
        # Record trade
        hold_duration = timestamp - position['entry_time']
        trade_record = {
            'symbol': symbol,
            'strategy': position['strategy'],
            'side': position['side'],
            'quantity': position['quantity'],
            'entry_price': position['entry_price'],
            'exit_price': exit_price,
            'entry_time': position['entry_time'],
            'exit_time': timestamp,
            'hold_duration': hold_duration.total_seconds() / 60,  # minutes
            'pnl': pnl,
            'pnl_pct': (pnl / (position['entry_price'] * position['quantity'])) * 100,
            'exit_reason': reason
        }
        
        self.trades.append(trade_record)
        
        print(f"EXIT:  {timestamp.strftime('%Y-%m-%d %H:%M')} {symbol} "
              f"₹{exit_price:.2f} P&L: ₹{pnl:.2f} ({trade_record['pnl_pct']:.1f}%) "
              f"Reason: {reason}")
        
        # Remove position
        del self.positions[symbol]
    
    def _calculate_portfolio_value(self, current_price):
        """Calculate current portfolio value"""
        portfolio_value = self.current_capital
        
        for symbol, position in self.positions.items():
            position_value = position['quantity'] * current_price
            portfolio_value += position_value
        
        return portfolio_value
    
    def _generate_results(self):
        """Generate backtest results"""
        if not self.trades:
            return {
                'total_trades': 0,
                'total_return': 0,
                'win_rate': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'profit_factor': 0,
                'max_drawdown': 0
            }
        
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        # Calculate metrics
        total_trades = len(trades_df)
        winning_trades = trades_df[trades_df['pnl'] > 0]
        losing_trades = trades_df[trades_df['pnl'] <= 0]
        
        total_pnl = trades_df['pnl'].sum()
        total_return = (total_pnl / self.initial_capital) * 100
        
        win_rate = len(winning_trades) / total_trades * 100 if total_trades > 0 else 0
        avg_win = winning_trades['pnl'].mean() if len(winning_trades) > 0 else 0
        avg_loss = abs(losing_trades['pnl'].mean()) if len(losing_trades) > 0 else 0
        
        gross_profit = winning_trades['pnl'].sum() if len(winning_trades) > 0 else 0
        gross_loss = abs(losing_trades['pnl'].sum()) if len(losing_trades) > 0 else 0
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Calculate max drawdown
        equity_df['peak'] = equity_df['portfolio_value'].expanding().max()
        equity_df['drawdown'] = (equity_df['portfolio_value'] - equity_df['peak']) / equity_df['peak']
        max_drawdown = equity_df['drawdown'].min() * 100
        
        results = {
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'total_pnl': total_pnl,
            'total_return': total_return,
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'max_drawdown': max_drawdown,
            'final_capital': self.current_capital + sum(pos['quantity'] * 1000 for pos in self.positions.values()),
            'trades_data': trades_df,
            'equity_curve': equity_df
        }
        
        return results

class TestBacktesting:
    """Test cases for backtesting framework"""
    
    def setup_method(self):
        """Setup for each test"""
        self.backtest_engine = BacktestEngine()
    
    def test_sample_data_generation(self):
        """Test sample data generation"""
        data = self.backtest_engine.create_sample_data("TESTSTOCK", days=5)
        
        assert len(data) == 5 * 375  # 5 days * 375 minutes per day
        assert all(col in data.columns for col in ['open', 'high', 'low', 'close', 'volume'])
        assert all(data['high'] >= data['close'])
        assert all(data['close'] >= data['low'])
        assert all(data['high'] >= data['open'])
        assert all(data['open'] >= data['low'])
    
    def test_backtest_execution(self):
        """Test complete backtest execution"""
        results = self.backtest_engine.run_backtest("RELIANCE", days=10)
        
        assert isinstance(results, dict)
        assert 'total_trades' in results
        assert 'total_return' in results
        assert 'win_rate' in results
        assert 'profit_factor' in results
        assert 'max_drawdown' in results
        
        # Check that results are reasonable
        assert results['total_trades'] >= 0
        assert -100 <= results['total_return'] <= 1000  # Reasonable return range
        assert 0 <= results['win_rate'] <= 100
        assert results['profit_factor'] >= 0
    
    def test_position_management(self):
        """Test position entry and exit"""
        from technical_analysis import TechnicalSignal
        
        # Create test signal
        signal = TechnicalSignal(
            symbol="TEST",
            signal_type="BUY",
            strength=0.8,
            strategy="test",
            indicators={},
            timestamp=pd.Timestamp.now(),
            entry_price=1000,
            stop_loss=950,
            target=1100,
            confidence=0.7
        )
        
        # Execute signal
        self.backtest_engine._execute_signal(signal, pd.Timestamp.now())
        
        # Check position was created
        assert "TEST" in self.backtest_engine.positions
        position = self.backtest_engine.positions["TEST"]
        assert position['entry_price'] == 1000
        assert position['stop_loss'] == 950
        assert position['target'] == 1100
        
        # Test exit
        self.backtest_engine._close_position("TEST", 1050, pd.Timestamp.now(), "Test Exit")
        
        # Check position was closed
        assert "TEST" not in self.backtest_engine.positions
        assert len(self.backtest_engine.trades) == 1
        
        trade = self.backtest_engine.trades[0]
        assert trade['exit_price'] == 1050
        assert trade['pnl'] > 0  # Should be profitable
    
    def test_stop_loss_execution(self):
        """Test stop loss execution"""
        # Add position
        self.backtest_engine.positions["TEST"] = {
            'quantity': 10,
            'entry_price': 1000,
            'entry_time': pd.Timestamp.now(),
            'side': 'BUY',
            'stop_loss': 950,
            'target': 1100,
            'strategy': 'test'
        }
        
        # Trigger stop loss
        self.backtest_engine._check_exits(940, pd.Timestamp.now())
        
        # Check position was closed
        assert "TEST" not in self.backtest_engine.positions
        assert len(self.backtest_engine.trades) == 1
        assert self.backtest_engine.trades[0]['exit_reason'] == "Stop Loss"
    
    def test_target_execution(self):
        """Test target execution"""
        # Add position
        self.backtest_engine.positions["TEST"] = {
            'quantity': 10,
            'entry_price': 1000,
            'entry_time': pd.Timestamp.now(),
            'side': 'BUY',
            'stop_loss': 950,
            'target': 1100,
            'strategy': 'test'
        }
        
        # Trigger target
        self.backtest_engine._check_exits(1110, pd.Timestamp.now())
        
        # Check position was closed
        assert "TEST" not in self.backtest_engine.positions
        assert len(self.backtest_engine.trades) == 1
        assert self.backtest_engine.trades[0]['exit_reason'] == "Target Hit"
    
    def test_multiple_symbols_backtest(self):
        """Test backtesting multiple symbols"""
        symbols = ["RELIANCE", "TCS", "HDFCBANK"]
        all_results = {}
        
        for symbol in symbols:
            results = self.backtest_engine.run_backtest(symbol, days=5)
            all_results[symbol] = results
        
        # Check all symbols were tested
        assert len(all_results) == 3
        
        for symbol, results in all_results.items():
            assert isinstance(results, dict)
            assert 'total_trades' in results

if __name__ == "__main__":
    # Run a sample backtest
    engine = BacktestEngine()
    results = engine.run_backtest("RELIANCE", days=20)
    
    print("\n" + "="*50)
    print("BACKTEST RESULTS")
    print("="*50)
    print(f"Total Trades: {results['total_trades']}")
    print(f"Winning Trades: {results['winning_trades']}")
    print(f"Losing Trades: {results['losing_trades']}")
    print(f"Win Rate: {results['win_rate']:.1f}%")
    print(f"Total Return: {results['total_return']:.2f}%")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
    print(f"Max Drawdown: {results['max_drawdown']:.2f}%")
    print(f"Average Win: ₹{results['avg_win']:.2f}")
    print(f"Average Loss: ₹{results['avg_loss']:.2f}")
    print("="*50)
    
    pytest.main([__file__])
