{% extends "base.html" %}

{% block title %}Server Error - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card text-center">
            <div class="card-body py-5">
                <i class="fas fa-exclamation-triangle fa-5x text-warning mb-4"></i>
                <h1 class="display-1 text-danger">500</h1>
                <h2 class="mb-3">Internal Server Error</h2>
                <p class="lead text-muted mb-4">
                    Something went wrong on our end. Our trading bot is working to fix this issue. 
                    Please try again in a few moments.
                </p>
                
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    <strong>Don't worry!</strong> Your trading positions and data are safe. 
                    This is just a temporary technical issue.
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                    <button class="btn btn-primary btn-lg" onclick="location.reload()">
                        <i class="fas fa-sync"></i> Try Again
                    </button>
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-home"></i> Go to Dashboard
                    </a>
                </div>
                
                <hr class="my-4">
                
                <div class="row">
                    <div class="col-12">
                        <h6>If the problem persists:</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-envelope"></i> Email: <EMAIL></li>
                            <li><i class="fas fa-phone"></i> Phone: +91-XXXX-XXXXXX</li>
                            <li><i class="fas fa-ticket-alt"></i> Submit a support ticket</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
