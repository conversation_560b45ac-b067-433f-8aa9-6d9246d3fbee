"""
Technical Analysis Module
Implements various technical indicators and signal generation
"""
import numpy as np
import pandas as pd
import talib
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

from config import trading_config, strategy_config

logger = logging.getLogger(__name__)

@dataclass
class TechnicalSignal:
    """Represents a technical analysis signal"""
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    strength: float   # Signal strength 0-1
    strategy: str     # Strategy name that generated signal
    indicators: Dict  # Supporting indicator values
    timestamp: pd.Timestamp
    entry_price: float
    stop_loss: float
    target: float
    confidence: float  # Overall confidence 0-1

class TechnicalAnalyzer:
    """Technical analysis engine for generating trading signals"""
    
    def __init__(self):
        self.indicators_cache = {}
        logger.info("Technical Analyzer initialized")
    
    def calculate_indicators(self, df: pd.DataFrame) -> Dict:
        """Calculate all technical indicators for given OHLCV data"""
        
        if len(df) < 50:  # Need minimum data for indicators
            logger.warning("Insufficient data for technical analysis")
            return {}
        
        indicators = {}
        
        # Price data
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values
        open_price = df['open'].values
        
        try:
            # Moving Averages
            indicators['ema_9'] = talib.EMA(close, timeperiod=strategy_config.EMA_FAST)
            indicators['ema_21'] = talib.EMA(close, timeperiod=strategy_config.EMA_SLOW)
            indicators['sma_20'] = talib.SMA(close, timeperiod=20)
            indicators['sma_50'] = talib.SMA(close, timeperiod=50)
            
            # RSI
            indicators['rsi'] = talib.RSI(close, timeperiod=trading_config.RSI_PERIOD)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(close, fastperiod=12, slowperiod=26, signalperiod=9)
            indicators['macd'] = macd
            indicators['macd_signal'] = macd_signal
            indicators['macd_histogram'] = macd_hist
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2)
            indicators['bb_upper'] = bb_upper
            indicators['bb_middle'] = bb_middle
            indicators['bb_lower'] = bb_lower
            
            # VWAP (Volume Weighted Average Price)
            indicators['vwap'] = self._calculate_vwap(df)
            
            # ATR (Average True Range)
            indicators['atr'] = talib.ATR(high, low, close, timeperiod=14)
            
            # Stochastic
            stoch_k, stoch_d = talib.STOCH(high, low, close, fastk_period=14, slowk_period=3, slowd_period=3)
            indicators['stoch_k'] = stoch_k
            indicators['stoch_d'] = stoch_d
            
            # Volume indicators
            indicators['volume_sma'] = talib.SMA(volume.astype(float), timeperiod=20)
            indicators['volume_ratio'] = volume / indicators['volume_sma']
            
            # Support and Resistance
            indicators['support'], indicators['resistance'] = self._calculate_support_resistance(df)
            
            # Trend strength
            indicators['trend_strength'] = self._calculate_trend_strength(close)
            
            logger.debug("Technical indicators calculated successfully")
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {str(e)}")
            return {}
        
        return indicators
    
    def _calculate_vwap(self, df: pd.DataFrame) -> np.ndarray:
        """Calculate Volume Weighted Average Price"""
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap.values
    
    def _calculate_support_resistance(self, df: pd.DataFrame, window: int = 20) -> Tuple[np.ndarray, np.ndarray]:
        """Calculate dynamic support and resistance levels"""
        high = df['high'].rolling(window=window)
        low = df['low'].rolling(window=window)
        
        resistance = high.max().values
        support = low.min().values
        
        return support, resistance
    
    def _calculate_trend_strength(self, close: np.ndarray, period: int = 20) -> np.ndarray:
        """Calculate trend strength indicator"""
        if len(close) < period:
            return np.zeros(len(close))
        
        trend_strength = np.zeros(len(close))
        
        for i in range(period, len(close)):
            price_changes = np.diff(close[i-period:i])
            positive_changes = price_changes[price_changes > 0]
            negative_changes = price_changes[price_changes < 0]
            
            if len(positive_changes) > 0 and len(negative_changes) > 0:
                avg_gain = np.mean(positive_changes)
                avg_loss = abs(np.mean(negative_changes))
                trend_strength[i] = avg_gain / (avg_gain + avg_loss)
            else:
                trend_strength[i] = 0.5
        
        return trend_strength
    
    def generate_orb_signal(self, df: pd.DataFrame, current_price: float) -> Optional[TechnicalSignal]:
        """Generate Opening Range Breakout signal"""
        
        if len(df) < strategy_config.ORB_PERIOD_MINUTES:
            return None
        
        # Get opening range (first 15 minutes)
        orb_data = df.head(strategy_config.ORB_PERIOD_MINUTES)
        orb_high = orb_data['high'].max()
        orb_low = orb_data['low'].min()
        orb_range = orb_high - orb_low
        
        # Check if range is within acceptable limits
        if orb_range < strategy_config.ORB_MIN_RANGE_POINTS or orb_range > strategy_config.ORB_MAX_RANGE_POINTS:
            return None
        
        # Check for breakout
        if current_price > orb_high:
            # Bullish breakout
            stop_loss = orb_low
            target = current_price + (orb_range * 2)  # 2:1 risk reward
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='BUY',
                strength=0.8,
                strategy='orb',
                indicators={'orb_high': orb_high, 'orb_low': orb_low, 'orb_range': orb_range},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.75
            )
        
        elif current_price < orb_low:
            # Bearish breakout
            stop_loss = orb_high
            target = current_price - (orb_range * 2)  # 2:1 risk reward
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='SELL',
                strength=0.8,
                strategy='orb',
                indicators={'orb_high': orb_high, 'orb_low': orb_low, 'orb_range': orb_range},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.75
            )
        
        return None
    
    def generate_vwap_signal(self, df: pd.DataFrame, indicators: Dict, current_price: float) -> Optional[TechnicalSignal]:
        """Generate VWAP-based signal"""
        
        if 'vwap' not in indicators or len(indicators['vwap']) == 0:
            return None
        
        current_vwap = indicators['vwap'][-1]
        volume_ratio = indicators['volume_ratio'][-1] if 'volume_ratio' in indicators else 1.0
        
        # Check volume confirmation
        if strategy_config.VWAP_VOLUME_CONFIRMATION and volume_ratio < trading_config.VOLUME_MULTIPLIER:
            return None
        
        price_deviation = abs(current_price - current_vwap) / current_vwap
        
        if price_deviation < strategy_config.VWAP_DEVIATION_PCT:
            return None  # Price too close to VWAP
        
        atr = indicators['atr'][-1] if 'atr' in indicators else current_price * 0.02
        
        if current_price > current_vwap * (1 + strategy_config.VWAP_DEVIATION_PCT):
            # Bullish signal
            stop_loss = current_price - (atr * 1.5)
            target = current_price + (atr * 3)
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='BUY',
                strength=0.7,
                strategy='vwap',
                indicators={'vwap': current_vwap, 'volume_ratio': volume_ratio},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.65
            )
        
        elif current_price < current_vwap * (1 - strategy_config.VWAP_DEVIATION_PCT):
            # Bearish signal
            stop_loss = current_price + (atr * 1.5)
            target = current_price - (atr * 3)
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='SELL',
                strength=0.7,
                strategy='vwap',
                indicators={'vwap': current_vwap, 'volume_ratio': volume_ratio},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.65
            )
        
        return None
    
    def generate_momentum_signal(self, df: pd.DataFrame, indicators: Dict, current_price: float) -> Optional[TechnicalSignal]:
        """Generate momentum-based signal"""
        
        if 'rsi' not in indicators or 'macd' not in indicators:
            return None
        
        rsi = indicators['rsi'][-1]
        macd = indicators['macd'][-1]
        macd_signal = indicators['macd_signal'][-1]
        ema_9 = indicators['ema_9'][-1]
        ema_21 = indicators['ema_21'][-1]
        
        # Check for bullish momentum
        bullish_conditions = [
            rsi > 50 and rsi < 70,  # RSI in bullish zone but not overbought
            macd > macd_signal,     # MACD above signal line
            ema_9 > ema_21,         # Fast EMA above slow EMA
            current_price > ema_9   # Price above fast EMA
        ]
        
        # Check for bearish momentum
        bearish_conditions = [
            rsi < 50 and rsi > 30,  # RSI in bearish zone but not oversold
            macd < macd_signal,     # MACD below signal line
            ema_9 < ema_21,         # Fast EMA below slow EMA
            current_price < ema_9   # Price below fast EMA
        ]
        
        atr = indicators['atr'][-1] if 'atr' in indicators else current_price * 0.02
        
        if sum(bullish_conditions) >= 3:
            # Strong bullish momentum
            stop_loss = current_price - (atr * 2)
            target = current_price + (atr * 4)
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='BUY',
                strength=0.8,
                strategy='momentum',
                indicators={'rsi': rsi, 'macd': macd, 'ema_9': ema_9, 'ema_21': ema_21},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.7
            )
        
        elif sum(bearish_conditions) >= 3:
            # Strong bearish momentum
            stop_loss = current_price + (atr * 2)
            target = current_price - (atr * 4)
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='SELL',
                strength=0.8,
                strategy='momentum',
                indicators={'rsi': rsi, 'macd': macd, 'ema_9': ema_9, 'ema_21': ema_21},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.7
            )
        
        return None
    
    def generate_mean_reversion_signal(self, df: pd.DataFrame, indicators: Dict, current_price: float) -> Optional[TechnicalSignal]:
        """Generate mean reversion signal"""
        
        if 'bb_upper' not in indicators or 'bb_lower' not in indicators or 'rsi' not in indicators:
            return None
        
        bb_upper = indicators['bb_upper'][-1]
        bb_lower = indicators['bb_lower'][-1]
        bb_middle = indicators['bb_middle'][-1]
        rsi = indicators['rsi'][-1]
        
        atr = indicators['atr'][-1] if 'atr' in indicators else current_price * 0.02
        
        # Oversold condition - potential buy
        if (current_price <= bb_lower and rsi <= trading_config.RSI_OVERSOLD):
            stop_loss = current_price - (atr * 1)
            target = bb_middle
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='BUY',
                strength=0.6,
                strategy='mean_reversion',
                indicators={'bb_upper': bb_upper, 'bb_lower': bb_lower, 'rsi': rsi},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.6
            )
        
        # Overbought condition - potential sell
        elif (current_price >= bb_upper and rsi >= trading_config.RSI_OVERBOUGHT):
            stop_loss = current_price + (atr * 1)
            target = bb_middle
            
            return TechnicalSignal(
                symbol=df.attrs.get('symbol', 'UNKNOWN'),
                signal_type='SELL',
                strength=0.6,
                strategy='mean_reversion',
                indicators={'bb_upper': bb_upper, 'bb_lower': bb_lower, 'rsi': rsi},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=current_price,
                stop_loss=stop_loss,
                target=target,
                confidence=0.6
            )
        
        return None
    
    def analyze_stock(self, df: pd.DataFrame, symbol: str) -> List[TechnicalSignal]:
        """Comprehensive technical analysis for a stock"""
        
        if len(df) < 50:
            logger.warning(f"Insufficient data for {symbol}")
            return []
        
        # Add symbol to dataframe attributes
        df.attrs['symbol'] = symbol
        
        # Calculate all indicators
        indicators = self.calculate_indicators(df)
        if not indicators:
            return []
        
        current_price = df['close'].iloc[-1]
        signals = []
        
        # Generate signals from different strategies
        orb_signal = self.generate_orb_signal(df, current_price)
        if orb_signal:
            signals.append(orb_signal)
        
        vwap_signal = self.generate_vwap_signal(df, indicators, current_price)
        if vwap_signal:
            signals.append(vwap_signal)
        
        momentum_signal = self.generate_momentum_signal(df, indicators, current_price)
        if momentum_signal:
            signals.append(momentum_signal)
        
        mean_reversion_signal = self.generate_mean_reversion_signal(df, indicators, current_price)
        if mean_reversion_signal:
            signals.append(mean_reversion_signal)
        
        return signals
    
    def get_confluence_signal(self, signals: List[TechnicalSignal]) -> Optional[TechnicalSignal]:
        """Combine multiple signals to get confluence signal"""
        
        if not signals:
            return None
        
        # Group signals by type
        buy_signals = [s for s in signals if s.signal_type == 'BUY']
        sell_signals = [s for s in signals if s.signal_type == 'SELL']
        
        # Check for confluence (at least 2 signals in same direction)
        if len(buy_signals) >= 2:
            # Combine buy signals
            combined_strength = np.mean([s.strength for s in buy_signals])
            combined_confidence = np.mean([s.confidence for s in buy_signals])
            
            # Use the signal with highest confidence for entry/exit levels
            best_signal = max(buy_signals, key=lambda x: x.confidence)
            
            return TechnicalSignal(
                symbol=best_signal.symbol,
                signal_type='BUY',
                strength=combined_strength,
                strategy='confluence',
                indicators={'contributing_strategies': [s.strategy for s in buy_signals]},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=best_signal.entry_price,
                stop_loss=best_signal.stop_loss,
                target=best_signal.target,
                confidence=combined_confidence
            )
        
        elif len(sell_signals) >= 2:
            # Combine sell signals
            combined_strength = np.mean([s.strength for s in sell_signals])
            combined_confidence = np.mean([s.confidence for s in sell_signals])
            
            # Use the signal with highest confidence for entry/exit levels
            best_signal = max(sell_signals, key=lambda x: x.confidence)
            
            return TechnicalSignal(
                symbol=best_signal.symbol,
                signal_type='SELL',
                strength=combined_strength,
                strategy='confluence',
                indicators={'contributing_strategies': [s.strategy for s in sell_signals]},
                timestamp=pd.Timestamp.now(tz=trading_config.TIMEZONE),
                entry_price=best_signal.entry_price,
                stop_loss=best_signal.stop_loss,
                target=best_signal.target,
                confidence=combined_confidence
            )
        
        return None
