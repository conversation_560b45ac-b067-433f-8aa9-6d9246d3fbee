#!/usr/bin/env python3
"""
Paper Trading Demo Script
Demonstrates the trading bot in paper trading mode with simulated market data
"""
import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading_bot import TradingBot
from technical_analysis import TechnicalSignal
from config import trading_config

class PaperTradingDemo:
    """Demo class for paper trading simulation"""
    
    def __init__(self):
        self.bot = TradingBot()
        self.bot.paper_trading = True
        self.demo_data = {}
        self.current_time = datetime.now()
        
    def generate_demo_data(self, symbols, days=1):
        """Generate realistic demo market data"""
        print("📊 Generating demo market data...")
        
        for symbol in symbols:
            # Base prices for different stocks
            base_prices = {
                'RELIANCE': 2500,
                'TCS': 3200,
                'HDFCBANK': 1600,
                'ICICIBANK': 900,
                'INFOSYS': 1400
            }
            
            base_price = base_prices.get(symbol, 1000)
            
            # Generate minute-by-minute data
            minutes_per_day = 375  # 9:15 AM to 3:30 PM
            total_minutes = days * minutes_per_day
            
            prices = []
            volumes = []
            timestamps = []
            
            current_price = base_price
            
            for i in range(total_minutes):
                # Add realistic price movement
                volatility = 0.02 / np.sqrt(minutes_per_day)  # Daily 2% volatility
                price_change = np.random.normal(0, volatility)
                
                # Add some trend and patterns
                if i % 100 < 20:  # Morning volatility
                    price_change *= 1.5
                elif i % 100 > 80:  # End of day
                    price_change *= 0.5
                
                current_price *= (1 + price_change)
                current_price = max(current_price, 1)  # Ensure positive
                
                prices.append(current_price)
                volumes.append(np.random.randint(50000, 200000))
                
                # Calculate timestamp
                day_offset = i // minutes_per_day
                minute_offset = i % minutes_per_day
                timestamp = datetime(2024, 1, 1, 9, 15) + timedelta(days=day_offset, minutes=minute_offset)
                timestamps.append(timestamp)
            
            self.demo_data[symbol] = {
                'prices': prices,
                'volumes': volumes,
                'timestamps': timestamps,
                'current_index': 0
            }
        
        print(f"✅ Generated data for {len(symbols)} symbols")
    
    def get_current_price(self, symbol):
        """Get current price for a symbol"""
        if symbol not in self.demo_data:
            return 1000.0
        
        data = self.demo_data[symbol]
        if data['current_index'] < len(data['prices']):
            return data['prices'][data['current_index']]
        return data['prices'][-1]
    
    def advance_time(self):
        """Advance time by one minute"""
        self.current_time += timedelta(minutes=1)
        
        for symbol in self.demo_data:
            self.demo_data[symbol]['current_index'] += 1
    
    def create_demo_signal(self, symbol, signal_type="BUY"):
        """Create a demo trading signal"""
        current_price = self.get_current_price(symbol)
        
        if signal_type == "BUY":
            stop_loss = current_price * 0.98
            target = current_price * 1.04
        else:  # SELL
            stop_loss = current_price * 1.02
            target = current_price * 0.96
        
        return TechnicalSignal(
            symbol=symbol,
            signal_type=signal_type,
            strength=0.8,
            strategy="demo",
            indicators={'demo': True},
            timestamp=pd.Timestamp(self.current_time),
            entry_price=current_price,
            stop_loss=stop_loss,
            target=target,
            confidence=0.75
        )
    
    async def run_demo(self, duration_minutes=60):
        """Run paper trading demo"""
        print("🚀 Starting Paper Trading Demo")
        print("=" * 50)
        print(f"Duration: {duration_minutes} minutes")
        print(f"Initial Capital: ₹{trading_config.INITIAL_CAPITAL:,.2f}")
        print(f"Paper Trading: {self.bot.paper_trading}")
        print()
        
        # Generate demo data
        symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        self.generate_demo_data(symbols)
        
        # Mock the bot's API methods
        self.bot.angel_api.get_ltp = self.get_current_price
        self.bot.is_logged_in = True
        
        # Demo variables
        signals_generated = 0
        trades_executed = 0
        
        print("📈 Starting trading simulation...")
        print()
        
        for minute in range(duration_minutes):
            # Advance time
            self.advance_time()
            
            # Update market data cache
            for symbol in symbols:
                current_price = self.get_current_price(symbol)
                if symbol not in self.bot.market_data_cache:
                    self.bot.market_data_cache[symbol] = []
                
                self.bot.market_data_cache[symbol].append({
                    'timestamp': self.current_time,
                    'price': current_price
                })
                
                # Keep only last 100 data points
                if len(self.bot.market_data_cache[symbol]) > 100:
                    self.bot.market_data_cache[symbol] = self.bot.market_data_cache[symbol][-100:]
            
            # Update position prices
            price_updates = {symbol: self.get_current_price(symbol) for symbol in symbols}
            self.bot.risk_manager.update_position_prices(price_updates)
            
            # Check exit conditions
            await self.bot._check_exit_conditions()
            
            # Generate random signals (simulate strategy signals)
            if minute % 15 == 0 and len(self.bot.risk_manager.positions) < trading_config.MAX_POSITIONS:
                # Generate a signal for a random symbol
                symbol = np.random.choice(symbols)
                if symbol not in self.bot.risk_manager.positions:
                    signal_type = np.random.choice(['BUY', 'SELL'])
                    signal = self.create_demo_signal(symbol, signal_type)
                    
                    print(f"⚡ Signal Generated: {signal.symbol} {signal.signal_type} @ ₹{signal.entry_price:.2f}")
                    signals_generated += 1
                    
                    # Process signal
                    await self.bot._process_signal(signal)
                    
                    if symbol in self.bot.risk_manager.positions:
                        trades_executed += 1
                        print(f"✅ Trade Executed: {signal.symbol} {signal.signal_type}")
            
            # Print status every 10 minutes
            if minute % 10 == 0:
                self.print_status(minute, duration_minutes)
            
            # Small delay for demo effect
            await asyncio.sleep(0.1)
        
        # Final status
        print("\n" + "=" * 50)
        print("📊 DEMO COMPLETED")
        print("=" * 50)
        
        # Close any remaining positions
        await self.bot.square_off_all_positions("Demo End")
        
        # Print final results
        self.print_final_results(signals_generated, trades_executed)
    
    def print_status(self, current_minute, total_minutes):
        """Print current status"""
        progress = (current_minute / total_minutes) * 100
        metrics = self.bot.risk_manager.get_portfolio_summary()
        
        print(f"⏰ Time: {self.current_time.strftime('%H:%M')} "
              f"Progress: {progress:.1f}% "
              f"Positions: {metrics.current_positions} "
              f"P&L: ₹{metrics.daily_pnl:.2f}")
        
        # Show current positions
        if self.bot.risk_manager.positions:
            for symbol, position in self.bot.risk_manager.positions.items():
                current_price = self.get_current_price(symbol)
                print(f"   📍 {symbol}: {position.side} {position.quantity} @ ₹{position.entry_price:.2f} "
                      f"Current: ₹{current_price:.2f} P&L: ₹{position.unrealized_pnl:.2f}")
    
    def print_final_results(self, signals_generated, trades_executed):
        """Print final demo results"""
        metrics = self.bot.risk_manager.get_portfolio_summary()
        
        print(f"Signals Generated: {signals_generated}")
        print(f"Trades Executed: {trades_executed}")
        print(f"Final Capital: ₹{metrics.available_capital:.2f}")
        print(f"Total P&L: ₹{metrics.daily_pnl:.2f}")
        print(f"Return: {(metrics.daily_pnl / trading_config.INITIAL_CAPITAL) * 100:.2f}%")
        print(f"Max Drawdown: {metrics.max_drawdown:.2f}%")
        
        if self.bot.risk_manager.daily_trades:
            winning_trades = [t for t in self.bot.risk_manager.daily_trades if t.get('realized_pnl', 0) > 0]
            total_trades = len(self.bot.risk_manager.daily_trades)
            win_rate = (len(winning_trades) / total_trades) * 100 if total_trades > 0 else 0
            
            print(f"Total Completed Trades: {total_trades}")
            print(f"Winning Trades: {len(winning_trades)}")
            print(f"Win Rate: {win_rate:.1f}%")
        
        print("\n💡 This was a paper trading demo with simulated data.")
        print("   Real trading results may vary significantly.")
        print("   Always test thoroughly before using real money!")

async def main():
    """Main demo function"""
    print("🎮 Angel One Trading Bot - Paper Trading Demo")
    print("=" * 60)
    print("This demo simulates the trading bot in paper trading mode")
    print("with realistic market data and trading scenarios.")
    print()
    
    try:
        # Get demo duration from user
        duration_input = input("Enter demo duration in minutes (default 30): ").strip()
        duration = int(duration_input) if duration_input else 30
        
        if duration < 1 or duration > 300:
            print("Duration must be between 1 and 300 minutes. Using default 30.")
            duration = 30
        
        # Create and run demo
        demo = PaperTradingDemo()
        await demo.run_demo(duration)
        
        print("\n🎉 Demo completed successfully!")
        print("\nTo run the actual bot:")
        print("1. Set up your Angel One credentials in .env")
        print("2. Run: python main.py")
        print("3. Monitor logs: tail -f logs/trading_bot.log")
        
    except KeyboardInterrupt:
        print("\n\n👋 Demo stopped by user.")
    except Exception as e:
        print(f"\n❌ Demo error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
