#!/usr/bin/env python3
"""
Quick Setup for ₹100 Paper Trading
Sets up the bot for maximum returns with ₹100 budget
"""
import os
import sys
from datetime import datetime

def create_env_file():
    """Create .env file optimized for ₹100 trading"""
    print("🔧 Setting up ₹100 Paper Trading Configuration...")
    
    # Get Angel One credentials
    print("\n📋 Enter your Angel One SmartAPI credentials:")
    print("(From your screenshot: API Key = DxfC3bez, Secret Key = 5262bcc2-f4fe-4025-8e48-761d634e782)")
    
    api_key = input("API Key [DxfC3bez]: ").strip() or "DxfC3bez"
    secret_key = input("Secret Key [5262bcc2-f4fe-4025-8e48-761d634e782]: ").strip() or "5262bcc2-f4fe-4025-8e48-761d634e782"
    client_id = input("Client ID (your trading account number): ").strip()
    password = input("Password (your Angel One login password): ").strip()
    totp_secret = input("TOTP Secret (from 2FA setup): ").strip()
    
    if not all([client_id, password, totp_secret]):
        print("❌ Missing required credentials!")
        print("You need: Client ID, Password, and TOTP Secret")
        return False
    
    # Create optimized .env file for ₹100 trading
    env_content = f"""# Angel One SmartAPI Credentials
ANGEL_API_KEY={api_key}
ANGEL_SECRET_KEY={secret_key}
ANGEL_CLIENT_ID={client_id}
ANGEL_PASSWORD={password}
ANGEL_TOTP_SECRET={totp_secret}

# ₹100 Paper Trading Configuration
PAPER_TRADING=True
INITIAL_CAPITAL=100
MAX_DAILY_LOSS=25
MAX_POSITIONS=1
RISK_PER_TRADE=0.15

# High Accuracy Settings
MIN_SIGNAL_CONFIDENCE=0.75
MIN_CONFLUENCE_SIGNALS=2
SIGNAL_REFRESH_SECONDS=30

# Performance Targets
DAILY_TARGET_PCT=20
WEEKLY_TARGET_PCT=100
MONTHLY_TARGET_PCT=500

# Logging
LOG_LEVEL=INFO
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    # Set secure permissions
    try:
        os.chmod('.env', 0o600)
        print("✅ .env file created with secure permissions")
    except:
        print("✅ .env file created")
    
    return True

def test_credentials():
    """Test Angel One credentials"""
    print("\n🧪 Testing Angel One credentials...")
    
    try:
        # Add src to path
        sys.path.append('src')
        
        from angel_api import AngelOneAPI
        
        api = AngelOneAPI()
        success = api.login()
        
        if success:
            print("✅ Login successful!")
            
            # Get profile
            profile = api.get_profile()
            if profile:
                print(f"✅ Welcome: {profile.get('name', 'Unknown')}")
            
            # Get funds
            funds = api.get_funds()
            if funds:
                cash = funds.get('availablecash', 0)
                print(f"✅ Available cash: ₹{cash}")
            
            # Test market data
            ltp = api.get_ltp('RELIANCE')
            if ltp:
                print(f"✅ Market data: RELIANCE = ₹{ltp:.2f}")
            
            api.logout()
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Login failed - check credentials")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def show_trading_plan():
    """Show the ₹100 trading plan"""
    print("\n💰 ₹100 PAPER TRADING PLAN")
    print("=" * 50)
    print("🎯 TARGETS:")
    print("   Daily Target: ₹20 (20% return)")
    print("   Weekly Target: ₹100 (100% return)")
    print("   Monthly Target: ₹500 (500% return)")
    print()
    print("🛡️ RISK MANAGEMENT:")
    print("   Risk per Trade: 15% (₹15 max loss)")
    print("   Daily Loss Limit: ₹25 (25% of capital)")
    print("   Maximum Positions: 1 at a time")
    print("   Stop Loss: 2% per trade")
    print("   Target: 6% per trade (1:3 risk-reward)")
    print()
    print("📈 STRATEGY:")
    print("   High accuracy signals only (75%+ confidence)")
    print("   Multiple strategy confluence required")
    print("   Focus on most liquid stocks (RELIANCE, TCS, HDFCBANK)")
    print("   Trade only during high volatility periods")
    print()
    print("⏰ TRADING HOURS:")
    print("   09:15-09:45 (Opening volatility)")
    print("   11:00-11:30 (Mid-morning moves)")
    print("   14:30-15:15 (Closing volatility)")
    print()
    print("🎲 EXPECTED PERFORMANCE:")
    print("   Win Rate Target: 70%+")
    print("   Average Win: ₹6 (6%)")
    print("   Average Loss: ₹2 (2%)")
    print("   Trades per Day: 3-5")

def main():
    """Main setup function"""
    print("💰 ₹100 Paper Trading Bot Setup")
    print("=" * 60)
    print("🚀 Maximum Returns with High Accuracy")
    print(f"Setup Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # Step 1: Create configuration
        if not create_env_file():
            return 1
        
        # Step 2: Test credentials
        if not test_credentials():
            print("\n❌ Credential test failed!")
            print("Please check your Angel One credentials and try again.")
            return 1
        
        # Step 3: Show trading plan
        show_trading_plan()
        
        # Step 4: Ready to start
        print("\n🎉 SETUP COMPLETE!")
        print("=" * 50)
        print("✅ Credentials configured and tested")
        print("✅ ₹100 paper trading optimized")
        print("✅ High accuracy strategies enabled")
        print("✅ Risk management configured")
        print()
        print("🚀 READY TO START TRADING!")
        print()
        print("Next steps:")
        print("1. Start trading: python small_budget_bot.py")
        print("2. Monitor logs: tail -f logs/trading_bot.log")
        print("3. Track performance in real-time")
        print()
        print("⚠️  IMPORTANT:")
        print("- This is PAPER TRADING (no real money)")
        print("- Monitor performance closely")
        print("- Adjust strategies based on results")
        print("- Start live trading only after consistent profits")
        print()
        
        # Ask if user wants to start immediately
        start_now = input("Start trading now? (y/N): ").strip().lower()
        if start_now == 'y':
            print("\n🚀 Starting ₹100 Paper Trading Bot...")
            os.system("python small_budget_bot.py")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
