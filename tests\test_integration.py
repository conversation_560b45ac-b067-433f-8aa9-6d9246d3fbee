"""
Integration tests for the complete trading bot system
"""
import pytest
import sys
import os
import asyncio
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from trading_bot import TradingBot
from angel_api import AngelOneAPI
from risk_manager import RiskManager
from technical_analysis import TechnicalAnalyzer
from config import trading_config

class TestTradingBotIntegration:
    """Integration tests for the complete trading system"""
    
    def setup_method(self):
        """Setup for each test"""
        self.bot = TradingBot()
        self.sample_market_data = self.create_sample_market_data()
    
    def create_sample_market_data(self):
        """Create sample market data for testing"""
        symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        market_data = {}
        
        for symbol in symbols:
            # Create price history
            base_price = np.random.uniform(500, 2000)
            prices = []
            current_time = datetime.now()
            
            for i in range(100):
                price_change = np.random.normal(0, 0.01)
                new_price = base_price * (1 + price_change)
                prices.append({
                    'timestamp': current_time - timedelta(minutes=100-i),
                    'price': max(new_price, 1)  # Ensure positive price
                })
                base_price = new_price
            
            market_data[symbol] = prices
        
        return market_data
    
    @patch('src.angel_api.AngelOneAPI.login')
    @patch('src.angel_api.AngelOneAPI.get_profile')
    @patch('src.angel_api.AngelOneAPI.get_funds')
    async def test_bot_initialization(self, mock_funds, mock_profile, mock_login):
        """Test bot initialization and login"""
        # Mock API responses
        mock_login.return_value = True
        mock_profile.return_value = {'name': 'Test User'}
        mock_funds.return_value = {'availablecash': 100000}
        
        # Test login
        success = await self.bot.login()
        assert success == True
        assert self.bot.is_logged_in == True
        
        mock_login.assert_called_once()
        mock_profile.assert_called_once()
        mock_funds.assert_called_once()
    
    @patch('src.angel_api.AngelOneAPI.get_ltp')
    async def test_market_data_update(self, mock_get_ltp):
        """Test market data update functionality"""
        # Mock LTP responses
        mock_get_ltp.side_effect = lambda symbol: {
            'RELIANCE': 2500.0,
            'TCS': 3200.0,
            'HDFCBANK': 1600.0
        }.get(symbol, 1000.0)
        
        # Update market data
        await self.bot._update_market_data()
        
        # Check that data was cached
        assert len(self.bot.market_data_cache) > 0
        for symbol in self.bot.trading_symbols[:3]:  # Check first 3 symbols
            if symbol in ['RELIANCE', 'TCS', 'HDFCBANK']:
                assert symbol in self.bot.market_data_cache
                assert len(self.bot.market_data_cache[symbol]) > 0
    
    @patch('src.angel_api.AngelOneAPI.get_ltp')
    async def test_position_price_update(self, mock_get_ltp):
        """Test position price updates"""
        # Add a test position
        self.bot.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=2500,
            side="BUY",
            stop_loss=2450,
            target=2600,
            order_id="TEST001"
        )
        
        # Mock new price
        mock_get_ltp.return_value = 2550.0
        
        # Update position prices
        await self.bot._update_position_prices()
        
        # Check that position was updated
        position = self.bot.risk_manager.positions["RELIANCE"]
        assert position.current_price == 2550.0
        assert position.unrealized_pnl == 500.0  # (2550 - 2500) * 10
    
    @patch('src.angel_api.AngelOneAPI.place_order')
    async def test_paper_trade_execution(self, mock_place_order):
        """Test paper trade execution"""
        from technical_analysis import TechnicalSignal
        
        # Create test signal
        signal = TechnicalSignal(
            symbol="RELIANCE",
            signal_type="BUY",
            strength=0.8,
            strategy="test",
            indicators={},
            timestamp=pd.Timestamp.now(),
            entry_price=2500,
            stop_loss=2450,
            target=2600,
            confidence=0.7
        )
        
        # Set paper trading mode
        self.bot.paper_trading = True
        
        # Execute trade
        await self.bot._process_signal(signal)
        
        # Check that position was added (paper trading)
        assert "RELIANCE" in self.bot.risk_manager.positions
        
        # Verify no real API call was made
        mock_place_order.assert_not_called()
    
    @patch('src.angel_api.AngelOneAPI.place_order')
    async def test_real_trade_execution(self, mock_place_order):
        """Test real trade execution"""
        from technical_analysis import TechnicalSignal
        
        # Mock successful order placement
        mock_place_order.return_value = "ORDER123"
        
        # Create test signal
        signal = TechnicalSignal(
            symbol="TCS",
            signal_type="BUY",
            strength=0.8,
            strategy="test",
            indicators={},
            timestamp=pd.Timestamp.now(),
            entry_price=3200,
            stop_loss=3150,
            target=3300,
            confidence=0.7
        )
        
        # Set live trading mode
        self.bot.paper_trading = False
        
        # Execute trade
        await self.bot._process_signal(signal)
        
        # Check that position was added
        assert "TCS" in self.bot.risk_manager.positions
        
        # Verify API call was made
        mock_place_order.assert_called_once()
    
    async def test_stop_loss_execution(self):
        """Test stop loss execution"""
        # Add position
        self.bot.risk_manager.add_position(
            symbol="HDFCBANK",
            quantity=20,
            entry_price=1600,
            side="BUY",
            stop_loss=1550,
            target=1700,
            order_id="TEST002"
        )
        
        # Update price to trigger stop loss
        self.bot.risk_manager.update_position_prices({"HDFCBANK": 1540})
        
        # Mock exit position
        with patch.object(self.bot, '_exit_position') as mock_exit:
            await self.bot._check_exit_conditions()
            mock_exit.assert_called_with("HDFCBANK", "Stop Loss Hit")
    
    async def test_target_execution(self):
        """Test target execution"""
        # Add position
        self.bot.risk_manager.add_position(
            symbol="HDFCBANK",
            quantity=20,
            entry_price=1600,
            side="BUY",
            stop_loss=1550,
            target=1700,
            order_id="TEST003"
        )
        
        # Update price to trigger target
        self.bot.risk_manager.update_position_prices({"HDFCBANK": 1710})
        
        # Mock exit position
        with patch.object(self.bot, '_exit_position') as mock_exit:
            await self.bot._check_exit_conditions()
            mock_exit.assert_called_with("HDFCBANK", "Target Hit")
    
    @patch('src.angel_api.AngelOneAPI.place_order')
    async def test_square_off_all_positions(self, mock_place_order):
        """Test square off all positions functionality"""
        # Add multiple positions
        positions = [
            ("RELIANCE", 10, 2500, "BUY"),
            ("TCS", 5, 3200, "BUY"),
            ("HDFCBANK", 15, 1600, "SELL")
        ]
        
        for symbol, qty, price, side in positions:
            self.bot.risk_manager.add_position(
                symbol=symbol,
                quantity=qty,
                entry_price=price,
                side=side,
                stop_loss=price * 0.98 if side == "BUY" else price * 1.02,
                target=price * 1.04 if side == "BUY" else price * 0.96,
                order_id=f"TEST_{symbol}"
            )
        
        # Mock order placement for exits
        mock_place_order.return_value = "EXIT_ORDER"
        
        # Set live trading mode
        self.bot.paper_trading = False
        
        # Square off all positions
        await self.bot.square_off_all_positions("Test square off")
        
        # Check that all positions are closed
        assert len(self.bot.risk_manager.positions) == 0
        
        # Verify exit orders were placed (if not paper trading)
        if not self.bot.paper_trading:
            assert mock_place_order.call_count == len(positions)
    
    def test_risk_limits_enforcement(self):
        """Test that risk limits are properly enforced"""
        # Test daily loss limit
        self.bot.risk_manager.daily_pnl = -6000  # Exceeds limit
        
        from technical_analysis import TechnicalSignal
        signal = TechnicalSignal(
            symbol="TEST",
            signal_type="BUY",
            strength=0.8,
            strategy="test",
            indicators={},
            timestamp=pd.Timestamp.now(),
            entry_price=1000,
            stop_loss=950,
            target=1100,
            confidence=0.7
        )
        
        # Should not be able to process signal due to daily loss limit
        # This would be tested in the actual _process_signal method
        is_valid, message = self.bot.risk_manager.validate_trade(
            "TEST", "BUY", 10, 1000, 950
        )
        assert is_valid == False
        assert "Daily loss limit" in message
    
    def test_position_limits_enforcement(self):
        """Test position limits enforcement"""
        # Fill up to maximum positions
        for i in range(trading_config.MAX_POSITIONS):
            success = self.bot.risk_manager.add_position(
                symbol=f"STOCK{i}",
                quantity=10,
                entry_price=1000,
                side="BUY",
                stop_loss=950,
                target=1100,
                order_id=f"TEST{i}"
            )
            assert success == True
        
        # Try to add one more
        success = self.bot.risk_manager.add_position(
            symbol="EXTRASTOCK",
            quantity=10,
            entry_price=1000,
            side="BUY",
            stop_loss=950,
            target=1100,
            order_id="TESTEXTRA"
        )
        assert success == False
    
    @patch('src.trading_bot.is_market_hours')
    @patch('src.trading_bot.is_trading_day')
    async def test_market_hours_check(self, mock_trading_day, mock_market_hours):
        """Test market hours and trading day checks"""
        # Test non-trading day
        mock_trading_day.return_value = False
        mock_market_hours.return_value = False
        
        # Bot should not start on non-trading day
        with patch.object(self.bot, 'login', return_value=True):
            await self.bot.start()
            # Should exit early without doing anything
        
        # Test market closed
        mock_trading_day.return_value = True
        mock_market_hours.return_value = False
        
        # Should wait for market to open
        # This would be tested in the main loop
    
    def test_performance_metrics_calculation(self):
        """Test performance metrics calculation"""
        # Add some positions with P&L
        self.bot.risk_manager.add_position(
            symbol="RELIANCE",
            quantity=10,
            entry_price=2500,
            side="BUY",
            stop_loss=2450,
            target=2600,
            order_id="TEST001"
        )
        
        # Update price to show profit
        self.bot.risk_manager.update_position_prices({"RELIANCE": 2550})
        
        # Get metrics
        metrics = self.bot.risk_manager.get_portfolio_summary()
        
        assert metrics.current_positions == 1
        assert metrics.daily_pnl == 500  # Unrealized profit
        assert metrics.available_capital < trading_config.INITIAL_CAPITAL
    
    async def test_error_handling(self):
        """Test error handling in various scenarios"""
        # Test API error handling
        with patch.object(self.bot.angel_api, 'get_ltp', side_effect=Exception("API Error")):
            # Should handle gracefully without crashing
            await self.bot._update_market_data()
            # Bot should continue running
        
        # Test invalid signal handling
        from technical_analysis import TechnicalSignal
        invalid_signal = TechnicalSignal(
            symbol="INVALID",
            signal_type="BUY",
            strength=0.8,
            strategy="test",
            indicators={},
            timestamp=pd.Timestamp.now(),
            entry_price=-100,  # Invalid negative price
            stop_loss=-50,
            target=-200,
            confidence=0.7
        )
        
        # Should handle invalid signal gracefully
        await self.bot._process_signal(invalid_signal)

if __name__ == "__main__":
    pytest.main([__file__])
