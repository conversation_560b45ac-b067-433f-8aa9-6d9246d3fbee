#!/usr/bin/env python3
"""
₹100 Paper Trading Launcher
Complete launcher for maximum returns trading system
"""
import os
import sys
import subprocess
import time
from datetime import datetime

def show_banner():
    """Show trading bot banner"""
    print("💰" * 30)
    print("💰  ₹100 PAPER TRADING BOT  💰")
    print("💰   MAXIMUM RETURNS SYSTEM   💰") 
    print("💰" * 30)
    print()
    print("🎯 Target: 20% daily return (₹20 profit)")
    print("🛡️ Risk: 15% per trade, 25% daily loss limit")
    print("📈 Strategy: High accuracy signals only")
    print("⏰ Trading: High volatility periods only")
    print()

def check_setup():
    """Check if system is properly setup"""
    print("🔧 Checking system setup...")
    
    issues = []
    
    # Check .env file
    if not os.path.exists('.env'):
        issues.append("❌ .env file missing - run setup first")
    else:
        print("✅ .env file found")
    
    # Check credentials
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        required_vars = ['ANGEL_API_KEY', 'ANGEL_CLIENT_ID', 'ANGEL_PASSWORD', 'ANGEL_TOTP_SECRET']
        for var in required_vars:
            if not os.getenv(var):
                issues.append(f"❌ {var} missing in .env")
        
        if not issues:
            print("✅ Angel One credentials configured")
    except Exception as e:
        issues.append(f"❌ Environment error: {e}")
    
    # Check dependencies
    try:
        import pandas, numpy, talib, requests, pyotp
        print("✅ Dependencies installed")
    except ImportError as e:
        issues.append(f"❌ Missing dependency: {e}")
    
    # Check logs directory
    if not os.path.exists('logs'):
        os.makedirs('logs')
        print("✅ Logs directory created")
    else:
        print("✅ Logs directory exists")
    
    return issues

def show_menu():
    """Show main menu"""
    print("\n🚀 LAUNCH OPTIONS")
    print("=" * 40)
    print("1. 🔧 Setup ₹100 Trading (First time)")
    print("2. 💰 Start Paper Trading")
    print("3. 📊 Performance Monitor")
    print("4. 🔧 Strategy Optimizer")
    print("5. 🧪 Test Credentials")
    print("6. 📈 Live Data Test")
    print("7. 📋 View Trading Plan")
    print("8. 🔍 Check System Status")
    print("9. ❌ Exit")
    print()

def run_setup():
    """Run initial setup"""
    print("🔧 Running ₹100 Trading Setup...")
    try:
        subprocess.run([sys.executable, "setup_100_trading.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Setup failed")
        return False
    except FileNotFoundError:
        print("❌ setup_100_trading.py not found")
        return False

def start_trading():
    """Start paper trading"""
    print("💰 Starting ₹100 Paper Trading Bot...")
    
    # Check if setup is complete
    issues = check_setup()
    if issues:
        print("❌ Setup issues found:")
        for issue in issues:
            print(f"   {issue}")
        print("\nRun setup first (Option 1)")
        return False
    
    try:
        print("🚀 Launching trading bot...")
        subprocess.run([sys.executable, "small_budget_bot.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Trading bot failed")
        return False
    except FileNotFoundError:
        print("❌ small_budget_bot.py not found")
        return False
    except KeyboardInterrupt:
        print("\n👋 Trading stopped by user")
        return True

def start_monitor():
    """Start performance monitor"""
    print("📊 Starting Performance Monitor...")
    try:
        subprocess.run([sys.executable, "performance_monitor.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Monitor failed")
        return False
    except FileNotFoundError:
        print("❌ performance_monitor.py not found")
        return False
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped by user")
        return True

def run_optimizer():
    """Run strategy optimizer"""
    print("🔧 Starting Strategy Optimizer...")
    try:
        subprocess.run([sys.executable, "strategy_optimizer.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Optimizer failed")
        return False
    except FileNotFoundError:
        print("❌ strategy_optimizer.py not found")
        return False

def test_credentials():
    """Test Angel One credentials"""
    print("🧪 Testing Angel One Credentials...")
    try:
        subprocess.run([sys.executable, "scripts/test_credentials.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Credential test failed")
        return False
    except FileNotFoundError:
        print("❌ test_credentials.py not found")
        return False

def live_data_test():
    """Run live data test"""
    print("📈 Running Live Data Test...")
    try:
        subprocess.run([sys.executable, "scripts/live_data_test.py"], check=True)
        return True
    except subprocess.CalledProcessError:
        print("❌ Live data test failed")
        return False
    except FileNotFoundError:
        print("❌ live_data_test.py not found")
        return False

def show_trading_plan():
    """Show detailed trading plan"""
    print("\n💰 ₹100 PAPER TRADING PLAN")
    print("=" * 50)
    print()
    print("🎯 FINANCIAL TARGETS:")
    print("   Starting Capital: ₹100")
    print("   Daily Target: ₹20 (20% return)")
    print("   Weekly Target: ₹100 (100% return)")
    print("   Monthly Target: ₹500 (500% return)")
    print()
    print("🛡️ RISK MANAGEMENT:")
    print("   Risk per Trade: 15% (₹15 max loss)")
    print("   Daily Loss Limit: ₹25 (25% of capital)")
    print("   Stop Loss: 2% per trade")
    print("   Target: 6% per trade (1:3 risk-reward)")
    print("   Maximum Positions: 1 at a time")
    print()
    print("📈 TRADING STRATEGY:")
    print("   Signal Confidence: 75%+ minimum")
    print("   Strategy Confluence: 2+ strategies must agree")
    print("   Target Stocks: RELIANCE, TCS, HDFCBANK")
    print("   Win Rate Target: 70%+")
    print()
    print("⏰ TRADING SCHEDULE:")
    print("   09:15-09:45: Opening volatility")
    print("   11:00-11:30: Mid-morning moves")
    print("   14:30-15:15: Closing volatility")
    print("   Auto square-off: 15:20")
    print()
    print("📊 EXPECTED PERFORMANCE:")
    print("   Average Win: ₹6 (6%)")
    print("   Average Loss: ₹2 (2%)")
    print("   Trades per Day: 3-5")
    print("   Profit Factor: 3.0+")
    print()

def check_system_status():
    """Check complete system status"""
    print("\n🔍 SYSTEM STATUS CHECK")
    print("=" * 40)
    
    # Check setup
    issues = check_setup()
    
    if not issues:
        print("✅ System fully configured")
        
        # Check if trading is active
        if os.path.exists('logs/trading_bot.log'):
            print("✅ Trading logs found")
            
            # Check recent activity
            try:
                with open('logs/trading_bot.log', 'r') as f:
                    lines = f.readlines()
                    if lines:
                        last_line = lines[-1]
                        print(f"✅ Last log: {last_line.strip()[:50]}...")
            except:
                pass
        else:
            print("⚠️  No trading logs - bot not started yet")
        
        # Check performance
        if os.path.exists('logs/trades.log'):
            print("✅ Trade logs found")
        else:
            print("⚠️  No trade logs - no trades executed yet")
        
        print("\n🚀 System ready for trading!")
    else:
        print("❌ System issues found:")
        for issue in issues:
            print(f"   {issue}")
        print("\nRun setup to fix issues")

def main():
    """Main launcher function"""
    show_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("Select option (1-9): ").strip()
            
            if choice == '1':
                run_setup()
            elif choice == '2':
                start_trading()
            elif choice == '3':
                start_monitor()
            elif choice == '4':
                run_optimizer()
            elif choice == '5':
                test_credentials()
            elif choice == '6':
                live_data_test()
            elif choice == '7':
                show_trading_plan()
            elif choice == '8':
                check_system_status()
            elif choice == '9':
                print("👋 Goodbye! Happy trading!")
                break
            else:
                print("❌ Invalid option. Please select 1-9.")
            
            # Pause before showing menu again
            if choice != '9':
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n\n👋 Launcher stopped by user")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            input("Press Enter to continue...")

if __name__ == "__main__":
    main()
