# 🔌 Real Angel One API Testing Guide

This guide focuses on testing your trading bot with **REAL Angel One SmartAPI data** - no mocks, no placeholders, just real market data and API calls.

## ⚡ **Why Real API Testing is Critical**

### **Problems with Mock Data:**
- ❌ **Unrealistic patterns** - Mock data doesn't reflect real market behavior
- ❌ **Missing edge cases** - Real markets have gaps, halts, volatility spikes
- ❌ **API differences** - Real API responses differ from mocked ones
- ❌ **Timing issues** - Real API has latency, rate limits, timeouts
- ❌ **False confidence** - Bo<PERSON> works with fake data but fails with real data

### **Benefits of Real API Testing:**
- ✅ **Real market conditions** - Test with actual price movements
- ✅ **API reliability** - Discover connection issues, rate limits
- ✅ **Data quality** - Handle missing data, bad ticks, market halts
- ✅ **Performance** - Real latency and throughput testing
- ✅ **True validation** - Confidence that bot actually works

---

## 🔧 **Setup for Real API Testing**

### **1. Angel One Account Requirements**
```
✅ Active Angel One trading account
✅ SmartAPI subscription activated
✅ API credentials generated:
   - API Key
   - Client ID  
   - Password
   - TOTP Secret (for 2FA)
✅ Account funded (for live testing)
✅ Trading permissions enabled
```

### **2. Configure Real Credentials**
```bash
# Create .env file with REAL credentials
cat > .env << EOF
# Real Angel One SmartAPI Credentials
ANGEL_API_KEY=your_real_api_key_here
ANGEL_CLIENT_ID=your_real_client_id_here  
ANGEL_PASSWORD=your_real_password_here
ANGEL_TOTP_SECRET=your_real_totp_secret_here

# Start with paper trading for safety
PAPER_TRADING=True
INITIAL_CAPITAL=100000
MAX_DAILY_LOSS=5000
EOF
```

### **3. Verify API Access**
```bash
# Test basic connectivity
python -c "
from src.angel_api import AngelOneAPI
api = AngelOneAPI()
success = api.login()
print(f'Login Success: {success}')
if success:
    profile = api.get_profile()
    print(f'User: {profile.get(\"name\", \"Unknown\")}')
    api.logout()
"
```

---

## 🧪 **Real API Testing Phases**

### **Phase 1: API Connectivity Testing**

#### **Test Basic API Functions**
```bash
# Run live API tests
python -m pytest tests/test_live_api.py -v

# Or run comprehensive live data test
python scripts/live_data_test.py
```

#### **Expected Results:**
```
✅ Login successful
✅ Profile fetch successful  
✅ Funds fetch successful
✅ LTP fetch for multiple symbols
✅ Historical data fetch
✅ Quote data fetch
✅ Order book access
✅ Positions access
```

#### **Common Issues & Solutions:**
```bash
# Issue: Login fails
# Solution: Check credentials, TOTP secret
python -c "
import pyotp
totp = pyotp.TOTP('YOUR_TOTP_SECRET')
print(f'Current TOTP: {totp.now()}')
"

# Issue: API rate limits
# Solution: Add delays between calls
time.sleep(1)  # 1 second between API calls

# Issue: Invalid symbol tokens
# Solution: Use correct NSE symbol tokens
# RELIANCE = '2885', TCS = '11536', etc.
```

### **Phase 2: Real Market Data Testing**

#### **Test Historical Data Quality**
```bash
# Test historical data for multiple symbols
python -c "
from src.angel_api import AngelOneAPI
api = AngelOneAPI()
api.login()

symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
for symbol in symbols:
    df = api.get_historical_data(symbol, 'ONE_MINUTE')
    if df is not None:
        print(f'{symbol}: {len(df)} records')
        print(f'  Latest: {df.index[-1]} = ₹{df[\"close\"].iloc[-1]:.2f}')
        print(f'  Range: ₹{df[\"low\"].min():.2f} - ₹{df[\"high\"].max():.2f}')
    else:
        print(f'{symbol}: No data')

api.logout()
"
```

#### **Test Real-Time Data**
```bash
# Test live price fetching
python -c "
from src.angel_api import AngelOneAPI
import time

api = AngelOneAPI()
api.login()

symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
for i in range(5):  # 5 iterations
    print(f'--- Iteration {i+1} ---')
    for symbol in symbols:
        ltp = api.get_ltp(symbol)
        print(f'{symbol}: ₹{ltp:.2f}')
    time.sleep(10)  # Wait 10 seconds

api.logout()
"
```

### **Phase 3: Technical Analysis with Real Data**

#### **Test Indicators with Live Data**
```bash
# Test technical analysis with real market data
python -c "
from src.angel_api import AngelOneAPI
from src.technical_analysis import TechnicalAnalyzer

api = AngelOneAPI()
analyzer = TechnicalAnalyzer()

api.login()

symbol = 'RELIANCE'
df = api.get_historical_data(symbol, 'ONE_MINUTE')

if df is not None and len(df) >= 50:
    indicators = analyzer.calculate_indicators(df)
    print(f'Indicators for {symbol}:')
    print(f'  RSI: {indicators[\"rsi\"][-1]:.2f}')
    print(f'  EMA 9: ₹{indicators[\"ema_9\"][-1]:.2f}')
    print(f'  EMA 21: ₹{indicators[\"ema_21\"][-1]:.2f}')
    print(f'  VWAP: ₹{indicators[\"vwap\"][-1]:.2f}')
    
    # Generate signals
    signals = analyzer.analyze_stock(df, symbol)
    print(f'  Signals: {len(signals)}')
    for signal in signals:
        print(f'    {signal.strategy}: {signal.signal_type} (Conf: {signal.confidence:.2f})')

api.logout()
"
```

### **Phase 4: Paper Trading with Real Data**

#### **Start Paper Trading Bot**
```bash
# Ensure paper trading mode
echo "PAPER_TRADING=True" >> .env

# Start the bot with real API
python main.py
```

#### **Monitor Real Paper Trading**
```bash
# Watch logs in real-time
tail -f logs/trading_bot.log

# Check for real signals
grep "SIGNAL_GENERATED" logs/trades.log

# Check for real trades
grep "TRADE_EXECUTED" logs/trades.log

# Monitor performance
grep "PERFORMANCE_METRICS" logs/trading_bot.log | tail -5
```

#### **Validate Paper Trading Results**
```bash
# Check daily performance
python -c "
import json
with open('logs/trades.log', 'r') as f:
    trades = [json.loads(line) for line in f if 'TRADE_EXECUTED' in line]
    
print(f'Total Trades: {len(trades)}')
if trades:
    symbols = set(trade['symbol'] for trade in trades)
    print(f'Symbols Traded: {symbols}')
"
```

### **Phase 5: Live Trading Validation**

#### **Pre-Live Checklist**
```bash
# 1. Verify paper trading success
grep "daily_pnl" logs/trading_bot.log | tail -10

# 2. Check risk management
grep "RISK_EVENT" logs/trading_bot.log

# 3. Verify no critical errors
grep "ERROR" logs/errors.log | tail -10

# 4. Test with minimal capital
echo "PAPER_TRADING=False" > .env
echo "INITIAL_CAPITAL=10000" >> .env
echo "MAX_DAILY_LOSS=500" >> .env
```

#### **Start Small Live Testing**
```bash
# Start with ₹10,000 capital
python main.py

# Monitor every trade closely
tail -f logs/trading_bot.log | grep -E "(TRADE_EXECUTED|TRADE_EXITED|RISK_EVENT)"
```

---

## 📊 **Real Data Quality Validation**

### **Data Consistency Checks**
```bash
# Test data consistency across API calls
python -c "
from src.angel_api import AngelOneAPI
import time

api = AngelOneAPI()
api.login()

symbol = 'RELIANCE'

# Get LTP
ltp = api.get_ltp(symbol)
print(f'LTP: ₹{ltp:.2f}')

# Get Quote
quote = api.get_quote(symbol)
quote_ltp = float(quote.get('ltp', 0))
print(f'Quote LTP: ₹{quote_ltp:.2f}')

# Check consistency
diff_pct = abs(ltp - quote_ltp) / ltp * 100
print(f'Difference: {diff_pct:.2f}%')

if diff_pct > 1:
    print('⚠️  Large price difference detected')
else:
    print('✅ Price data consistent')

api.logout()
"
```

### **Market Hours Validation**
```bash
# Test market hours detection
python -c "
from config import is_market_hours, is_trading_day
from datetime import datetime

print(f'Current Time: {datetime.now()}')
print(f'Is Trading Day: {is_trading_day()}')
print(f'Is Market Hours: {is_market_hours()}')

if is_trading_day() and is_market_hours():
    print('✅ Market is open - bot can trade')
else:
    print('⏰ Market is closed - bot will wait')
"
```

---

## 🚨 **Real API Testing Gotchas**

### **Common Real-World Issues**

#### **1. API Rate Limits**
```python
# Angel One has rate limits - add delays
import time
time.sleep(1)  # 1 second between API calls
```

#### **2. Market Data Gaps**
```python
# Handle missing data gracefully
df = api.get_historical_data(symbol)
if df is None or len(df) < 50:
    logger.warning(f"Insufficient data for {symbol}")
    return None
```

#### **3. Network Timeouts**
```python
# Add timeout handling
try:
    response = requests.post(url, data=data, timeout=30)
except requests.Timeout:
    logger.error("API request timeout")
    return None
```

#### **4. Invalid Symbol Tokens**
```python
# Validate symbol tokens
token = self._get_symbol_token(symbol)
if token == '1234':  # Default/invalid token
    logger.error(f"Invalid token for {symbol}")
    return None
```

#### **5. Market Holidays**
```python
# Check for market holidays
if not is_trading_day():
    logger.info("Market is closed today")
    return
```

---

## ✅ **Real API Testing Success Criteria**

### **Phase 1: API Connectivity**
- ✅ Login success rate > 95%
- ✅ All API endpoints accessible
- ✅ Data fetch success rate > 90%
- ✅ No authentication errors

### **Phase 2: Data Quality**
- ✅ Historical data completeness > 95%
- ✅ Real-time data consistency
- ✅ No missing OHLCV fields
- ✅ Reasonable price ranges

### **Phase 3: Technical Analysis**
- ✅ Indicators calculate correctly
- ✅ Signals generate appropriately
- ✅ No calculation errors
- ✅ Performance acceptable

### **Phase 4: Paper Trading**
- ✅ 2+ weeks successful operation
- ✅ Reasonable trade frequency
- ✅ Risk management working
- ✅ No system crashes

### **Phase 5: Live Trading**
- ✅ Small capital test successful
- ✅ No unexpected losses
- ✅ All safety mechanisms working
- ✅ Ready for full deployment

---

## 🎯 **Final Real API Validation**

Before going live with full capital:

```bash
# Run complete real API test suite
python scripts/run_tests.py

# Run live data validation
python scripts/live_data_test.py

# Verify 2+ weeks of paper trading
grep "daily_pnl" logs/trading_bot.log | wc -l  # Should be 14+ lines

# Check win rate
python -c "
import json
trades = []
with open('logs/trades.log', 'r') as f:
    for line in f:
        if 'TRADE_EXITED' in line:
            trades.append(json.loads(line))

if trades:
    profitable = [t for t in trades if t.get('realized_pnl', 0) > 0]
    win_rate = len(profitable) / len(trades) * 100
    print(f'Win Rate: {win_rate:.1f}%')
    
    if win_rate > 50:
        print('✅ Good win rate')
    else:
        print('❌ Low win rate - needs improvement')
"
```

**Only proceed to live trading when ALL real API tests pass! 🚀**

---

## 📞 **Real API Support**

### **Angel One API Issues:**
- Check [Angel One SmartAPI Documentation](https://smartapi.angelbroking.com/docs)
- Contact Angel One support for API-specific issues
- Verify account permissions and subscriptions

### **Bot Issues with Real Data:**
- Check logs for specific error messages
- Verify symbol tokens are correct
- Test individual components in isolation
- Ensure sufficient historical data available

**Remember: Real market data is unpredictable - your bot must handle all edge cases! 💪**
