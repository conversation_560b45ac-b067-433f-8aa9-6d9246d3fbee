<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Ultimate Trading Bot{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }
        
        .status-running {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-stopped {
            color: #dc3545;
            font-weight: bold;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 10px 25px;
            font-weight: bold;
            margin: 5px;
        }
        
        .btn-start {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
        }
        
        .btn-stop {
            background: linear-gradient(135deg, #dc3545, #c82333);
            border: none;
            color: white;
        }
        
        .trade-item {
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 10px 10px 0;
        }
        
        .trade-buy {
            border-left-color: #28a745;
        }
        
        .trade-sell {
            border-left-color: #dc3545;
        }
        
        .real-time-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            background-color: #28a745;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .sidebar {
            background: linear-gradient(180deg, #343a40, #495057);
            min-height: 100vh;
            padding-top: 20px;
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 15px 20px;
            border-radius: 0;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: #007bff;
            color: white;
        }
        
        .main-content {
            padding: 20px;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #007bff;
            color: white;
            border: none;
        }

        /* Dark Mode Styles */
        .dark-mode {
            background-color: #1a1a1a !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .navbar {
            background-color: #2d2d2d !important;
        }

        .dark-mode .sidebar {
            background: linear-gradient(180deg, #1e1e1e 0%, #2d2d2d 100%) !important;
        }

        .dark-mode .card {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
            border-color: #404040 !important;
        }

        .dark-mode .card-header {
            background: linear-gradient(135deg, #404040, #505050) !important;
            border-color: #505050 !important;
        }

        .dark-mode .table {
            background-color: #2d2d2d !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .table tbody tr:hover {
            background-color: #404040 !important;
        }

        .dark-mode .form-control,
        .dark-mode .form-select {
            background-color: #404040 !important;
            border-color: #505050 !important;
            color: #e0e0e0 !important;
        }

        .dark-mode .form-control:focus,
        .dark-mode .form-select:focus {
            background-color: #404040 !important;
            border-color: #007bff !important;
            color: #e0e0e0 !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        .dark-mode .dropdown-menu {
            background-color: #2d2d2d !important;
            border-color: #404040 !important;
        }

        .dark-mode .dropdown-item {
            color: #e0e0e0 !important;
        }

        .dark-mode .dropdown-item:hover {
            background-color: #404040 !important;
            color: #ffffff !important;
        }

        .dark-mode .alert {
            border-color: #505050 !important;
        }

        .dark-mode .alert-info {
            background-color: #1e3a5f !important;
            color: #b3d4fc !important;
        }

        .dark-mode .alert-success {
            background-color: #1e4d2b !important;
            color: #b3e5c7 !important;
        }

        .dark-mode .alert-warning {
            background-color: #5d4e1a !important;
            color: #ffeaa7 !important;
        }

        .dark-mode .alert-danger {
            background-color: #5d1a1a !important;
            color: #ffb3b3 !important;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-robot"></i> Ultimate Trading Bot
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if session.user %}
                <!-- User Menu -->
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle"></i> {{ session.user.name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                            <i class="fas fa-user"></i> Profile
                        </a></li>
                        <li><a class="dropdown-item" href="{{ url_for('settings') }}">
                            <i class="fas fa-cog"></i> Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleDarkMode()">
                            <i class="fas fa-moon" id="navDarkModeIcon"></i>
                            <span id="navDarkModeText">Dark Mode</span>
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a></li>
                    </ul>
                </div>
                {% endif %}

                <span class="navbar-text ms-3">
                    <span class="real-time-indicator"></span>
                    <span id="connection-status">Connected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a class="nav-link {% if request.endpoint == 'live_dashboard' %}active{% endif %}" href="{{ url_for('live_dashboard') }}">
                        <i class="fas fa-chart-candlestick"></i> Live Charts
                    </a>
                    <a class="nav-link {% if request.endpoint == 'positions' %}active{% endif %}" href="{{ url_for('positions') }}">
                        <i class="fas fa-chart-line"></i> Positions
                    </a>
                    <a class="nav-link {% if request.endpoint == 'trades' %}active{% endif %}" href="{{ url_for('trades') }}">
                        <i class="fas fa-history"></i> Trade History
                    </a>
                    <a class="nav-link {% if request.endpoint == 'analytics' %}active{% endif %}" href="{{ url_for('analytics') }}">
                        <i class="fas fa-chart-bar"></i> Analytics
                    </a>
                    <a class="nav-link {% if request.endpoint == 'settings' %}active{% endif %}" href="{{ url_for('settings') }}">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                    <hr class="my-3" style="border-color: #495057;">
                    <a class="nav-link {% if request.endpoint == 'help_page' %}active{% endif %}" href="{{ url_for('help_page') }}">
                        <i class="fas fa-question-circle"></i> Help
                    </a>
                    <a class="nav-link {% if request.endpoint == 'about' %}active{% endif %}" href="{{ url_for('about') }}">
                        <i class="fas fa-info-circle"></i> About
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 main-content">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Socket.IO Connection -->
    <script>
        const socket = io();
        
        socket.on('connect', function() {
            document.getElementById('connection-status').textContent = 'Connected';
            document.getElementById('connection-status').className = 'text-success';
        });
        
        socket.on('disconnect', function() {
            document.getElementById('connection-status').textContent = 'Disconnected';
            document.getElementById('connection-status').className = 'text-danger';
        });
        
        socket.on('bot_update', function(data) {
            updateBotStatus(data);
        });
        
        socket.on('new_trade', function(data) {
            showTradeNotification(data);
        });
        
        function updateBotStatus(data) {
            // Update status indicators
            const statusElement = document.getElementById('bot-status');
            if (statusElement) {
                statusElement.textContent = data.is_running ? 'RUNNING' : 'STOPPED';
                statusElement.className = data.is_running ? 'status-running' : 'status-stopped';
            }
            
            // Update performance metrics
            if (data.performance) {
                updateMetric('total-trades', data.performance.total_trades);
                updateMetric('win-rate', data.performance.win_rate.toFixed(1) + '%');
                updateMetric('total-pnl', data.performance.total_pnl.toFixed(2));
                updateMetric('positions-count', data.positions);
            }
            
            // Update last update time
            const lastUpdateElement = document.getElementById('last-update');
            if (lastUpdateElement && data.last_update) {
                lastUpdateElement.textContent = data.last_update;
            }
        }
        
        function updateMetric(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }
        
        function showTradeNotification(data) {
            // Create toast notification for new trades
            const toast = document.createElement('div');
            toast.className = 'toast position-fixed top-0 end-0 m-3';
            toast.innerHTML = `
                <div class="toast-header">
                    <strong class="me-auto">New Trade</strong>
                    <small>${data.timestamp}</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${data.symbol} ${data.action} at ${data.price.toFixed(2)}
                    <br>Confidence: ${(data.confidence * 100).toFixed(1)}%
                </div>
            `;
            
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toast);
            });
        }
        
        // Request updates every 30 seconds
        setInterval(function() {
            socket.emit('request_update');
        }, 30000);

        // Dark mode functionality
        function toggleDarkMode() {
            fetch('/toggle_dark_mode', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                updateDarkModeUI(data.dark_mode);
            })
            .catch(error => {
                console.error('Error toggling dark mode:', error);
            });
        }

        function updateDarkModeUI(isDarkMode) {
            const body = document.body;
            const navIcon = document.getElementById('navDarkModeIcon');
            const navText = document.getElementById('navDarkModeText');

            if (isDarkMode) {
                body.classList.add('dark-mode');
                if (navIcon) {
                    navIcon.className = 'fas fa-sun';
                    navText.textContent = 'Light Mode';
                }
            } else {
                body.classList.remove('dark-mode');
                if (navIcon) {
                    navIcon.className = 'fas fa-moon';
                    navText.textContent = 'Dark Mode';
                }
            }
        }

        // Initialize dark mode on page load
        document.addEventListener('DOMContentLoaded', function() {
            {% if session.user and session.user.dark_mode %}
                updateDarkModeUI(true);
            {% endif %}
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
