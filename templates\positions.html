{% extends "base.html" %}

{% block title %}Positions - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-line"></i> Current Positions
            <span class="badge bg-primary ms-2">{{ positions|length }} Open</span>
        </h1>
    </div>
</div>

{% if positions %}
<div class="row">
    {% for position in positions %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <strong>{{ position.symbol }}</strong>
                <span class="badge bg-{% if 'BUY' in position.action %}success{% else %}danger{% endif %}">
                    {{ position.action }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Entry Price</small>
                        <div class="fw-bold">{{ position.currency }} {{ "%.2f"|format(position.entry_price) }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Current Price</small>
                        <div class="fw-bold text-{% if position.current_price > position.entry_price %}success{% else %}danger{% endif %}">
                            {{ position.currency }} {{ "%.2f"|format(position.current_price) }}
                        </div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">Position Size</small>
                        <div class="fw-bold">{{ position.currency }} {{ "%.2f"|format(position.position_size) }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Confidence</small>
                        <div class="fw-bold">{{ "%.1f"|format(position.confidence * 100) }}%</div>
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-6">
                        <small class="text-muted">Entry Time</small>
                        <div class="fw-bold">{{ position.entry_time }}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Status</small>
                        <div class="fw-bold text-success">{{ position.status }}</div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-{% if position.confidence > 0.7 %}success{% elif position.confidence > 0.5 %}warning{% else %}danger{% endif %}" 
                             style="width: {{ position.confidence * 100 }}%"></div>
                    </div>
                    <small class="text-muted">Confidence Level</small>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <button class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-line"></i> View Chart
                    </button>
                    <button class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-times"></i> Close Position
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Positions Summary -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-table"></i> Positions Summary
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Action</th>
                                <th>Entry Price</th>
                                <th>Current Price</th>
                                <th>P&L</th>
                                <th>Position Size</th>
                                <th>Confidence</th>
                                <th>Entry Time</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for position in positions %}
                            <tr>
                                <td><strong>{{ position.symbol }}</strong></td>
                                <td>
                                    <span class="badge bg-{% if 'BUY' in position.action %}success{% else %}danger{% endif %}">
                                        {{ position.action }}
                                    </span>
                                </td>
                                <td>{{ position.currency }} {{ "%.2f"|format(position.entry_price) }}</td>
                                <td class="text-{% if position.current_price > position.entry_price %}success{% else %}danger{% endif %}">
                                    {{ position.currency }} {{ "%.2f"|format(position.current_price) }}
                                </td>
                                <td class="text-{% if position.current_price > position.entry_price %}success{% else %}danger{% endif %}">
                                    {% set pnl = (position.current_price - position.entry_price) * (position.position_size / position.entry_price) %}
                                    {{ position.currency }} {{ "%.2f"|format(pnl) }}
                                </td>
                                <td>{{ position.currency }} {{ "%.2f"|format(position.position_size) }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress me-2" style="width: 60px; height: 8px;">
                                            <div class="progress-bar bg-{% if position.confidence > 0.7 %}success{% elif position.confidence > 0.5 %}warning{% else %}danger{% endif %}" 
                                                 style="width: {{ position.confidence * 100 }}%"></div>
                                        </div>
                                        {{ "%.1f"|format(position.confidence * 100) }}%
                                    </div>
                                </td>
                                <td>{{ position.entry_time }}</td>
                                <td>
                                    <span class="badge bg-success">{{ position.status }}</span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- No Positions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No Open Positions</h4>
                <p class="text-muted">Start the trading bot to begin generating positions.</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Go to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    // Real-time position updates
    socket.on('positions_update', function(data) {
        // Update positions in real-time
        console.log('Positions updated:', data);
        // You can add real-time position updates here
    });
    
    // Auto-refresh positions every 30 seconds
    setInterval(function() {
        if (!document.hidden) {
            fetch('/api/positions')
                .then(response => response.json())
                .then(data => {
                    // Update position data
                    console.log('Positions refreshed:', data);
                })
                .catch(error => console.error('Error refreshing positions:', error));
        }
    }, 30000);
</script>
{% endblock %}
