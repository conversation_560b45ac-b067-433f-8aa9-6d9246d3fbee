#!/usr/bin/env python3
"""
ULTIMATE TRADING BOT - DEPLOYMENT TEST
Quick test to verify deployment is working correctly
"""
import asyncio
import sys
import os
from datetime import datetime

def test_imports():
    """Test if all required imports work"""
    print("🔍 Testing imports...")
    
    try:
        import numpy as np
        print("   ✅ numpy")
        
        import pandas as pd
        print("   ✅ pandas")
        
        import requests
        print("   ✅ requests")
        
        import yfinance as yf
        print("   ✅ yfinance")
        
        from dotenv import load_dotenv
        print("   ✅ python-dotenv")
        
        import pyotp
        print("   ✅ pyotp")
        
        print("✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Run: pip install -r requirements_ultimate.txt")
        return False

def test_environment():
    """Test environment configuration"""
    print("\n🔍 Testing environment configuration...")
    
    if not os.path.exists('.env'):
        print("⚠️  .env file not found")
        print("💡 Copy .env.example to .env and configure your credentials")
        return False
    
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required variables
    required_vars = [
        'ANGEL_API_KEY', 'ANGEL_CLIENT_ID', 
        'ANGEL_PASSWORD', 'ANGEL_TOTP_SECRET'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value or value == f'your_{var.lower()}_here':
            missing_vars.append(var)
        else:
            print(f"   ✅ {var}: {'*' * min(len(value), 8)}")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("💡 Configure these in your .env file")
        return False
    
    print("✅ Environment configuration looks good!")
    return True

async def test_bot_initialization():
    """Test bot initialization"""
    print("\n🔍 Testing bot initialization...")
    
    try:
        # Import the bot
        from deploy_ultimate_bot import UltimateTradingBot
        
        # Create bot instance (paper trading mode)
        bot = UltimateTradingBot(paper_trading=True)
        print("   ✅ Bot instance created")
        
        # Test initialization
        success = await bot.initialize()
        if success:
            print("   ✅ Bot initialized successfully")
        else:
            print("   ⚠️  Bot initialization had issues (may be normal in paper trading)")
        
        print("✅ Bot initialization test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Bot initialization error: {e}")
        return False

async def test_data_sources():
    """Test data source connectivity"""
    print("\n🔍 Testing data sources...")
    
    try:
        # Test Yahoo Finance
        import yfinance as yf
        ticker = yf.Ticker("RELIANCE.NS")
        hist = ticker.history(period="1d")
        
        if not hist.empty:
            print("   ✅ Yahoo Finance connectivity")
        else:
            print("   ⚠️  Yahoo Finance returned empty data")
        
        # Test fallback data generation
        import numpy as np
        test_data = np.random.uniform(2400, 2600)
        print(f"   ✅ Fallback data generation: ₹{test_data:.2f}")
        
        print("✅ Data sources test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Data sources error: {e}")
        return False

async def test_analysis_components():
    """Test analysis components"""
    print("\n🔍 Testing analysis components...")
    
    try:
        from deploy_ultimate_bot import UltimateTechnicalAnalyzer, ProductionSentimentAnalyzer
        
        # Test technical analyzer
        tech_analyzer = UltimateTechnicalAnalyzer()
        print("   ✅ Technical analyzer created")
        
        # Test sentiment analyzer
        sentiment_analyzer = ProductionSentimentAnalyzer()
        print("   ✅ Sentiment analyzer created")
        
        # Test sentiment analysis
        sentiment = await sentiment_analyzer.get_comprehensive_sentiment("RELIANCE")
        print(f"   ✅ Sentiment analysis: {sentiment['sentiment']:+.2f}")
        
        print("✅ Analysis components test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Analysis components error: {e}")
        return False

def test_file_structure():
    """Test file structure"""
    print("\n🔍 Testing file structure...")
    
    required_files = [
        'deploy_ultimate_bot.py',
        '.env.example',
        'requirements_ultimate.txt',
        'DEPLOYMENT_README.md'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            missing_files.append(file)
            print(f"   ❌ {file}")
    
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
        print("   ✅ logs/ directory created")
    else:
        print("   ✅ logs/ directory exists")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ File structure test completed!")
    return True

async def run_deployment_test():
    """Run complete deployment test"""
    print("🚀 ULTIMATE TRADING BOT - DEPLOYMENT TEST")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Environment", test_environment),
        ("Data Sources", test_data_sources),
        ("Analysis Components", test_analysis_components),
        ("Bot Initialization", test_bot_initialization),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"🧪 Running {test_name} test...")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed_tests += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test ERROR: {e}")
        
        print()
    
    # Final results
    print("🏁 DEPLOYMENT TEST RESULTS")
    print("=" * 60)
    print(f"📊 Tests Passed: {passed_tests}/{total_tests}")
    print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    print()
    
    if passed_tests == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Your Ultimate Trading Bot is ready for deployment!")
        print()
        print("🚀 Next Steps:")
        print("   1. Configure your .env file with real credentials")
        print("   2. Run: python deploy_ultimate_bot.py")
        print("   3. Select Paper Trading mode first")
        print("   4. Monitor performance and then move to live trading")
        print()
        print("💰 Happy Trading! 📈")
        
    elif passed_tests >= total_tests * 0.8:
        print("⚠️  MOSTLY READY!")
        print("✅ Most tests passed - minor issues may exist")
        print("💡 Check failed tests and fix any issues")
        print("🚀 Bot should work in paper trading mode")
        
    else:
        print("❌ DEPLOYMENT NOT READY")
        print("💡 Please fix the failed tests before deploying")
        print("📖 Check DEPLOYMENT_README.md for troubleshooting")
    
    return passed_tests == total_tests

def main():
    """Main test function"""
    try:
        result = asyncio.run(run_deployment_test())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
