#!/usr/bin/env python3
"""
Strategy Optimizer for ₹100 Paper Trading
Optimizes trading parameters for maximum returns with high accuracy
"""
import sys
import os
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import itertools

# Add src to path
sys.path.append('src')

from angel_api import AngelOneAPI
from technical_analysis import TechnicalAnalyzer
from config_small_budget import high_freq_stocks

class StrategyOptimizer:
    """Optimize trading strategies for maximum returns"""
    
    def __init__(self):
        self.api = AngelOneAPI()
        self.analyzer = TechnicalAnalyzer()
        self.optimization_results = []
        
    def optimize_parameters(self, symbol: str = "RELIANCE", days: int = 7):
        """Optimize trading parameters for maximum returns"""
        print(f"🔧 Optimizing strategy parameters for {symbol}")
        print("=" * 60)
        
        # Login to API
        if not self.api.login():
            print("❌ Failed to login to Angel One API")
            return None
        
        # Get historical data
        print(f"📊 Fetching {days} days of historical data...")
        df = self.api.get_historical_data(symbol, "ONE_MINUTE")
        
        if df is None or len(df) < 1000:
            print("❌ Insufficient historical data")
            return None
        
        # Use last N days
        df = df.tail(days * 375)  # 375 minutes per trading day
        print(f"✅ Using {len(df)} data points for optimization")
        
        # Parameter ranges to test
        parameter_ranges = {
            'rsi_period': [9, 14, 21],
            'rsi_oversold': [20, 25, 30],
            'rsi_overbought': [70, 75, 80],
            'ema_fast': [5, 9, 13],
            'ema_slow': [13, 21, 34],
            'stop_loss_pct': [0.015, 0.02, 0.025],
            'target_pct': [0.045, 0.06, 0.075],
            'min_confidence': [0.65, 0.75, 0.85]
        }
        
        print("🧪 Testing parameter combinations...")
        
        # Generate all combinations
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())
        combinations = list(itertools.product(*param_values))
        
        print(f"Total combinations to test: {len(combinations)}")
        
        best_params = None
        best_return = -float('inf')
        results = []
        
        for i, combination in enumerate(combinations[:50]):  # Test first 50 combinations
            params = dict(zip(param_names, combination))
            
            if i % 10 == 0:
                print(f"Progress: {i}/{min(50, len(combinations))} combinations tested")
            
            # Backtest with these parameters
            result = self._backtest_parameters(df, params, symbol)
            
            if result:
                results.append({**params, **result})
                
                if result['total_return'] > best_return:
                    best_return = result['total_return']
                    best_params = params
        
        # Sort results by return
        results.sort(key=lambda x: x['total_return'], reverse=True)
        
        print("\n🏆 OPTIMIZATION RESULTS")
        print("=" * 60)
        
        # Show top 5 results
        for i, result in enumerate(results[:5]):
            print(f"\nRank {i+1}:")
            print(f"  Total Return: {result['total_return']:.2f}%")
            print(f"  Win Rate: {result['win_rate']:.1f}%")
            print(f"  Profit Factor: {result['profit_factor']:.2f}")
            print(f"  Max Drawdown: {result['max_drawdown']:.2f}%")
            print(f"  Total Trades: {result['total_trades']}")
            
            # Show key parameters
            print(f"  Parameters:")
            print(f"    RSI Period: {result['rsi_period']}")
            print(f"    RSI Levels: {result['rsi_oversold']}/{result['rsi_overbought']}")
            print(f"    EMA: {result['ema_fast']}/{result['ema_slow']}")
            print(f"    Stop/Target: {result['stop_loss_pct']:.1%}/{result['target_pct']:.1%}")
            print(f"    Min Confidence: {result['min_confidence']:.1%}")
        
        self.api.logout()
        
        return {
            'best_params': best_params,
            'best_return': best_return,
            'all_results': results
        }
    
    def _backtest_parameters(self, df: pd.DataFrame, params: Dict, symbol: str) -> Dict:
        """Backtest specific parameters"""
        try:
            # Create analyzer with custom parameters
            analyzer = TechnicalAnalyzer()
            
            # Override default parameters
            analyzer.rsi_period = params['rsi_period']
            analyzer.rsi_oversold = params['rsi_oversold']
            analyzer.rsi_overbought = params['rsi_overbought']
            analyzer.ema_fast = params['ema_fast']
            analyzer.ema_slow = params['ema_slow']
            
            # Simulate trading
            capital = 100.0
            position = None
            trades = []
            
            for i in range(100, len(df)):  # Start after enough data for indicators
                current_data = df.iloc[:i+1]
                current_price = df.iloc[i]['close']
                
                # Check exit conditions
                if position:
                    # Check stop loss
                    if position['side'] == 'BUY' and current_price <= position['stop_loss']:
                        pnl = (current_price - position['entry_price']) * position['quantity']
                        capital += pnl
                        trades.append(pnl)
                        position = None
                    # Check target
                    elif position['side'] == 'BUY' and current_price >= position['target']:
                        pnl = (current_price - position['entry_price']) * position['quantity']
                        capital += pnl
                        trades.append(pnl)
                        position = None
                    continue
                
                # Look for new signals
                if not position:
                    signals = analyzer.analyze_stock(current_data, symbol)
                    
                    if signals:
                        # Filter by confidence
                        high_conf_signals = [s for s in signals if s.confidence >= params['min_confidence']]
                        
                        if len(high_conf_signals) >= 2:  # Require confluence
                            signal = high_conf_signals[0]  # Take first signal
                            
                            # Calculate position size (15% of capital)
                            position_value = capital * 0.15
                            quantity = position_value / signal.entry_price
                            
                            # Set stop loss and target based on parameters
                            if signal.signal_type == 'BUY':
                                stop_loss = signal.entry_price * (1 - params['stop_loss_pct'])
                                target = signal.entry_price * (1 + params['target_pct'])
                            else:
                                stop_loss = signal.entry_price * (1 + params['stop_loss_pct'])
                                target = signal.entry_price * (1 - params['target_pct'])
                            
                            position = {
                                'side': signal.signal_type,
                                'entry_price': signal.entry_price,
                                'quantity': quantity,
                                'stop_loss': stop_loss,
                                'target': target
                            }
            
            # Close any remaining position
            if position:
                final_price = df.iloc[-1]['close']
                pnl = (final_price - position['entry_price']) * position['quantity']
                capital += pnl
                trades.append(pnl)
            
            # Calculate metrics
            if not trades:
                return None
            
            total_return = ((capital - 100) / 100) * 100
            winning_trades = [t for t in trades if t > 0]
            losing_trades = [t for t in trades if t <= 0]
            
            win_rate = (len(winning_trades) / len(trades)) * 100 if trades else 0
            
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = abs(np.mean(losing_trades)) if losing_trades else 0
            
            profit_factor = avg_win / avg_loss if avg_loss > 0 else 0
            
            # Calculate max drawdown
            running_capital = 100
            peak_capital = 100
            max_drawdown = 0
            
            for trade in trades:
                running_capital += trade
                if running_capital > peak_capital:
                    peak_capital = running_capital
                
                drawdown = ((peak_capital - running_capital) / peak_capital) * 100
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            return {
                'total_return': total_return,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'max_drawdown': max_drawdown,
                'total_trades': len(trades),
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'final_capital': capital
            }
            
        except Exception as e:
            return None
    
    def generate_optimized_config(self, best_params: Dict, symbol: str):
        """Generate optimized configuration file"""
        config_content = f'''"""
Optimized Configuration for {symbol} - Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Optimized for maximum returns with ₹100 capital
"""

# Optimized Technical Analysis Parameters
OPTIMIZED_RSI_PERIOD = {best_params['rsi_period']}
OPTIMIZED_RSI_OVERSOLD = {best_params['rsi_oversold']}
OPTIMIZED_RSI_OVERBOUGHT = {best_params['rsi_overbought']}

OPTIMIZED_EMA_FAST = {best_params['ema_fast']}
OPTIMIZED_EMA_SLOW = {best_params['ema_slow']}

# Optimized Risk Management
OPTIMIZED_STOP_LOSS_PCT = {best_params['stop_loss_pct']}
OPTIMIZED_TARGET_PCT = {best_params['target_pct']}
OPTIMIZED_MIN_CONFIDENCE = {best_params['min_confidence']}

# Trading Configuration
OPTIMIZED_SYMBOL = "{symbol}"
OPTIMIZED_CAPITAL = 100.0
OPTIMIZED_RISK_PER_TRADE = 0.15

def get_optimized_settings():
    """Get optimized settings for trading bot"""
    return {{
        'rsi_period': OPTIMIZED_RSI_PERIOD,
        'rsi_oversold': OPTIMIZED_RSI_OVERSOLD,
        'rsi_overbought': OPTIMIZED_RSI_OVERBOUGHT,
        'ema_fast': OPTIMIZED_EMA_FAST,
        'ema_slow': OPTIMIZED_EMA_SLOW,
        'stop_loss_pct': OPTIMIZED_STOP_LOSS_PCT,
        'target_pct': OPTIMIZED_TARGET_PCT,
        'min_confidence': OPTIMIZED_MIN_CONFIDENCE,
        'symbol': OPTIMIZED_SYMBOL,
        'capital': OPTIMIZED_CAPITAL,
        'risk_per_trade': OPTIMIZED_RISK_PER_TRADE
    }}
'''
        
        with open(f'optimized_config_{symbol.lower()}.py', 'w') as f:
            f.write(config_content)
        
        print(f"✅ Optimized configuration saved to: optimized_config_{symbol.lower()}.py")

def main():
    """Main optimization function"""
    print("🔧 Strategy Optimizer for ₹100 Paper Trading")
    print("=" * 60)
    print("Optimizing parameters for maximum returns with high accuracy")
    print()
    
    # Check credentials
    try:
        from config import env_config
        creds = env_config.get_angel_credentials()
        
        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            print("❌ Angel One credentials not configured!")
            print("Run: python setup_100_trading.py")
            return 1
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return 1
    
    # Get symbol to optimize
    print("📈 Available symbols for optimization:")
    for i, symbol in enumerate(high_freq_stocks.HIGH_ACCURACY_STOCKS, 1):
        print(f"  {i}. {symbol}")
    
    try:
        choice = input("\nSelect symbol (1-5) or press Enter for RELIANCE: ").strip()
        
        if choice:
            symbol_index = int(choice) - 1
            symbol = high_freq_stocks.HIGH_ACCURACY_STOCKS[symbol_index]
        else:
            symbol = "RELIANCE"
        
        days = input("Days of data to use (default 7): ").strip()
        days = int(days) if days else 7
        
        print(f"\n🎯 Optimizing for {symbol} using {days} days of data...")
        
        # Run optimization
        optimizer = StrategyOptimizer()
        results = optimizer.optimize_parameters(symbol, days)
        
        if results:
            print(f"\n🎉 OPTIMIZATION COMPLETE!")
            print(f"Best return: {results['best_return']:.2f}%")
            
            # Generate config file
            optimizer.generate_optimized_config(results['best_params'], symbol)
            
            print(f"\n🚀 Next steps:")
            print(f"1. Use optimized parameters in your trading bot")
            print(f"2. Test with paper trading first")
            print(f"3. Monitor performance closely")
            
        return 0
        
    except KeyboardInterrupt:
        print("\n👋 Optimization cancelled")
        return 0
    except Exception as e:
        print(f"❌ Optimization error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
