#!/usr/bin/env python3
"""
ULTIMATE TRADING BOT - WINDOWS COMPATIBLE VERSION
Fixed for Windows console encoding and paper trading
"""
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json
import logging
import os
import sys
from dotenv import load_dotenv
import yfinance as yf
import time

# Load environment variables
load_dotenv()

# Configure Windows-compatible logging (no emojis)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/bot_windows.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class TradingAction:
    STRONG_BUY = 2
    BUY = 1
    HOLD = 0
    SELL = -1
    STRONG_SELL = -2

class WindowsTradingBot:
    """Windows-compatible trading bot"""
    
    def __init__(self, paper_trading=True):
        self.paper_trading = paper_trading
        self.capital = 100.0
        self.positions = {}
        self.trades = []
        
        # Trading parameters
        self.confidence_threshold = 0.42
        self.max_position_size = 0.15
        
        # Watchlist
        self.watchlist = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS']
        
        logger.info("ULTIMATE TRADING BOT - WINDOWS VERSION")
        logger.info(f"Mode: {'PAPER TRADING' if paper_trading else 'LIVE TRADING'}")
        logger.info(f"Capital: Rs.{self.capital:.2f}")
        logger.info(f"Watchlist: {len(self.watchlist)} stocks")
        
    async def get_live_data(self, symbol: str) -> Optional[Dict]:
        """Get live market data"""
        try:
            logger.info(f"Getting live data for {symbol}")
            
            # Try Yahoo Finance first
            ticker = yf.Ticker(f"{symbol}.NS")
            info = ticker.info
            hist = ticker.history(period="1d", interval="5m")
            
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                prev_close = info.get('previousClose', current_price)
                change = current_price - prev_close
                change_pct = (change / prev_close) * 100 if prev_close > 0 else 0
                volume = hist['Volume'].iloc[-1]
                
                live_data = {
                    'symbol': symbol,
                    'ltp': float(current_price),
                    'change': float(change),
                    'change_pct': float(change_pct),
                    'volume': int(volume),
                    'timestamp': datetime.now(),
                    'data_source': 'Yahoo_Finance_Real'
                }
                
                logger.info(f"Live data for {symbol}: Rs.{current_price:.2f} ({change_pct:+.2f}%)")
                return live_data
            else:
                # Fallback to simulated data
                return self.get_fallback_data(symbol)
                
        except Exception as e:
            logger.error(f"Live data error for {symbol}: {e}")
            return self.get_fallback_data(symbol)
    
    def get_fallback_data(self, symbol: str) -> Dict:
        """Fallback simulated data"""
        base_prices = {
            'RELIANCE': 2485.50,
            'TCS': 3245.75,
            'HDFCBANK': 1598.25,
            'ICICIBANK': 945.30,
            'INFOSYS': 1456.80
        }
        
        base_price = base_prices.get(symbol, 1000)
        change_pct = np.random.uniform(-3, 3)
        current_price = base_price * (1 + change_pct / 100)
        
        return {
            'symbol': symbol,
            'ltp': current_price,
            'change': current_price - base_price,
            'change_pct': change_pct,
            'volume': np.random.randint(500000, 2000000),
            'timestamp': datetime.now(),
            'data_source': 'Simulated_Fallback'
        }
    
    async def get_historical_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Get historical data"""
        try:
            logger.info(f"Getting historical data for {symbol}")
            
            ticker = yf.Ticker(f"{symbol}.NS")
            hist = ticker.history(period="5d", interval="5m")
            
            if not hist.empty:
                df = hist.copy()
                df.columns = [col.lower() for col in df.columns]
                df = df.dropna()
                
                logger.info(f"Historical data for {symbol}: {len(df)} bars")
                return df
            else:
                return self.generate_synthetic_data()
                
        except Exception as e:
            logger.error(f"Historical data error for {symbol}: {e}")
            return self.generate_synthetic_data()
    
    def generate_synthetic_data(self) -> pd.DataFrame:
        """Generate synthetic historical data"""
        dates = pd.date_range(end=datetime.now(), periods=100, freq='5T')
        base_price = 1000
        prices = []
        
        for i in range(100):
            change = np.random.normal(0, 0.01)
            base_price *= (1 + change)
            prices.append(base_price)
        
        df = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': [np.random.randint(10000, 100000) for _ in range(100)]
        }, index=dates)
        
        return df
    
    def calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """Calculate RSI"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gains = np.mean(gains[-period:])
        avg_losses = np.mean(losses[-period:])
        
        if avg_losses == 0:
            return 100.0
        
        rs = avg_gains / avg_losses
        rsi = 100 - (100 / (1 + rs))
        
        return float(rsi)
    
    def analyze_stock(self, symbol: str, live_data: Dict, historical_data: pd.DataFrame) -> Optional[Dict]:
        """Analyze stock and generate signal"""
        try:
            logger.info(f"Analyzing {symbol}")
            
            if len(historical_data) < 20:
                logger.warning(f"Insufficient data for {symbol}")
                return None
            
            current_price = live_data['ltp']
            change_pct = live_data['change_pct']
            
            # Update historical data with current price
            df = historical_data.copy()
            df.iloc[-1, df.columns.get_loc('close')] = current_price
            
            close = df['close'].values
            volume = df['volume'].values
            
            # Technical analysis
            rsi = self.calculate_rsi(close)
            ma_20 = np.mean(close[-20:])
            volume_ratio = live_data['volume'] / np.mean(volume[-20:]) if np.mean(volume[-20:]) > 0 else 1
            
            # Calculate scores
            technical_score = 0.0
            
            # RSI component
            if rsi < 30:
                technical_score += 1.5
            elif rsi < 35:
                technical_score += 1.0
            elif rsi > 70:
                technical_score -= 1.5
            elif rsi > 65:
                technical_score -= 1.0
            
            # Moving average component
            if current_price > ma_20:
                technical_score += 0.8
            else:
                technical_score -= 0.8
            
            # Volume component
            if volume_ratio > 1.5:
                technical_score += 0.6
            elif volume_ratio < 0.7:
                technical_score -= 0.3
            
            # Price momentum
            if abs(change_pct) > 2.0:
                technical_score += np.sign(change_pct) * 0.8
            elif abs(change_pct) > 1.0:
                technical_score += np.sign(change_pct) * 0.4
            
            # Sentiment score (simulated)
            sentiment_score = np.random.uniform(-0.5, 0.5)
            
            # Momentum score
            momentum_score = 0.0
            if abs(change_pct) > 2.0:
                momentum_score += np.sign(change_pct) * 0.6
            elif abs(change_pct) > 1.0:
                momentum_score += np.sign(change_pct) * 0.3
            
            # Calculate confidence
            technical_conf = min(1.0, abs(technical_score) / 2.5)
            sentiment_conf = min(1.0, abs(sentiment_score))
            momentum_conf = min(1.0, abs(momentum_score))
            
            # Adaptive weights (simplified)
            weights = {'technical': 0.5, 'sentiment': 0.3, 'momentum': 0.2}
            
            raw_confidence = (
                technical_conf * weights['technical'] +
                sentiment_conf * weights['sentiment'] +
                momentum_conf * weights['momentum']
            )
            
            # Confidence boost for strong signals
            boost = 0.0
            strong_signals = sum([technical_conf > 0.6, sentiment_conf > 0.5, momentum_conf > 0.5])
            if strong_signals >= 2:
                boost = 0.15
            elif strong_signals == 1:
                boost = 0.05
            
            final_confidence = min(1.0, raw_confidence + boost)
            
            # Check confidence threshold
            if final_confidence < self.confidence_threshold:
                logger.info(f"{symbol}: Confidence too low ({final_confidence:.2f} < {self.confidence_threshold:.2f})")
                return None
            
            # Determine action
            combined_score = technical_score + sentiment_score + momentum_score
            
            if combined_score > 1.0:
                action = TradingAction.STRONG_BUY
                action_name = "STRONG_BUY"
            elif combined_score > 0.5:
                action = TradingAction.BUY
                action_name = "BUY"
            elif combined_score < -1.0:
                action = TradingAction.STRONG_SELL
                action_name = "STRONG_SELL"
            elif combined_score < -0.5:
                action = TradingAction.SELL
                action_name = "SELL"
            else:
                logger.info(f"{symbol}: No clear direction ({combined_score:.2f})")
                return None
            
            # Calculate position size and risk metrics
            position_size = self.capital * self.max_position_size * final_confidence
            
            if action > 0:  # BUY
                stop_loss = current_price * 0.98
                target = current_price * 1.06
            else:  # SELL
                stop_loss = current_price * 1.02
                target = current_price * 0.94
            
            signal = {
                'symbol': symbol,
                'action': action,
                'action_name': action_name,
                'confidence': final_confidence,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'target': target,
                'position_size': position_size,
                'technical_score': technical_score,
                'sentiment_score': sentiment_score,
                'momentum_score': momentum_score,
                'combined_score': combined_score,
                'rsi': rsi,
                'ma_20': ma_20,
                'volume_ratio': volume_ratio,
                'timestamp': datetime.now()
            }
            
            logger.info(f"SIGNAL GENERATED: {symbol} {action_name} (confidence: {final_confidence:.1%})")
            logger.info(f"  Entry: Rs.{current_price:.2f}, Stop: Rs.{stop_loss:.2f}, Target: Rs.{target:.2f}")
            logger.info(f"  Technical: {technical_score:.2f}, Sentiment: {sentiment_score:.2f}, Momentum: {momentum_score:.2f}")
            
            return signal
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
            return None
    
    async def execute_signal(self, signal: Dict) -> bool:
        """Execute trading signal"""
        try:
            symbol = signal['symbol']
            action_name = signal['action_name']
            
            if self.paper_trading:
                # Paper trading execution
                self.positions[symbol] = {
                    'signal': signal,
                    'entry_time': datetime.now(),
                    'status': 'OPEN'
                }
                
                # Record trade
                trade_record = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'action': action_name,
                    'entry_price': signal['entry_price'],
                    'confidence': signal['confidence'],
                    'position_size': signal['position_size'],
                    'mode': 'PAPER'
                }
                self.trades.append(trade_record)
                
                logger.info(f"PAPER TRADE EXECUTED: {symbol} {action_name}")
                logger.info(f"  Position Size: Rs.{signal['position_size']:.2f}")
                
                return True
            else:
                # Live trading would go here
                logger.info(f"LIVE TRADING: {symbol} {action_name}")
                return True
                
        except Exception as e:
            logger.error(f"Execution error for {signal['symbol']}: {e}")
            return False
    
    async def run_trading_cycle(self):
        """Run one trading cycle"""
        logger.info("Starting trading cycle")
        
        signals_generated = 0
        
        for symbol in self.watchlist:
            try:
                # Skip if already have position
                if symbol in self.positions:
                    continue
                
                # Get live data
                live_data = await self.get_live_data(symbol)
                if not live_data:
                    continue
                
                # Get historical data
                historical_data = await self.get_historical_data(symbol)
                if historical_data is None:
                    continue
                
                # Analyze stock
                signal = self.analyze_stock(symbol, live_data, historical_data)
                if not signal:
                    continue
                
                # Execute signal
                success = await self.execute_signal(signal)
                if success:
                    signals_generated += 1
                
                # Rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error processing {symbol}: {e}")
                continue
        
        logger.info(f"Trading cycle complete: {signals_generated} signals generated")
        
        # Save state
        self.save_state()
        
        return signals_generated
    
    def save_state(self):
        """Save bot state"""
        try:
            state = {
                'positions': self.positions,
                'trades': self.trades[-50:],  # Keep last 50 trades
                'capital': self.capital,
                'timestamp': datetime.now().isoformat()
            }
            
            with open('logs/bot_state_windows.json', 'w') as f:
                json.dump(state, f, indent=2, default=str)
                
            logger.info("Bot state saved")
            
        except Exception as e:
            logger.error(f"State save error: {e}")
    
    async def start_trading(self, cycles: int = 3):
        """Start trading for specified cycles"""
        logger.info(f"Starting trading for {cycles} cycles")
        
        for cycle in range(cycles):
            logger.info(f"CYCLE {cycle + 1}/{cycles}")
            
            signals = await self.run_trading_cycle()
            
            logger.info(f"Cycle {cycle + 1} complete: {signals} signals")
            
            if cycle < cycles - 1:
                logger.info("Waiting 30 seconds before next cycle...")
                await asyncio.sleep(30)
        
        logger.info("Trading session complete")
        
        # Show summary
        self.show_summary()
    
    def show_summary(self):
        """Show trading summary"""
        logger.info("TRADING SUMMARY")
        logger.info("=" * 40)
        logger.info(f"Total Trades: {len(self.trades)}")
        logger.info(f"Open Positions: {len(self.positions)}")
        
        if self.trades:
            logger.info("Recent Trades:")
            for trade in self.trades[-5:]:
                logger.info(f"  {trade['timestamp']}: {trade['symbol']} {trade['action']} (confidence: {trade['confidence']:.1%})")
        
        if self.positions:
            logger.info("Open Positions:")
            for symbol, pos in self.positions.items():
                logger.info(f"  {symbol}: {pos['signal']['action_name']} at Rs.{pos['signal']['entry_price']:.2f}")

async def main():
    """Main function"""
    print("ULTIMATE TRADING BOT - WINDOWS VERSION")
    print("=" * 50)
    
    # Create bot in paper trading mode
    bot = WindowsTradingBot(paper_trading=True)
    
    try:
        # Run trading for 3 cycles
        await bot.start_trading(cycles=3)
        
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"\nBot error: {e}")
        logger.error(f"Bot error: {e}")
    finally:
        bot.save_state()
        print("Bot state saved")

if __name__ == "__main__":
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Run the bot
    asyncio.run(main())
