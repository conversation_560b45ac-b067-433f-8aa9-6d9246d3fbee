"""
Angel One SmartAPI Integration
Handles all API interactions with Angel One broker
"""
import json
import time
import hashlib
import pyotp
import requests
import websocket
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

try:
    import pandas as pd
except ImportError:
    print("pandas not installed. Install with: pip install pandas")
    raise

from config import angel_config, trading_config, env_config

logger = logging.getLogger(__name__)

class AngelOneAPI:
    """Angel One SmartAPI client for trading operations"""
    
    def __init__(self):
        self.api_key = None
        self.client_id = None
        self.password = None
        self.totp_secret = None
        self.auth_token = None
        self.refresh_token = None
        self.feed_token = None
        self.session = requests.Session()
        self.ws = None
        self.is_connected = False
        
        # Load credentials
        self._load_credentials()
        
    def _load_credentials(self):
        """Load Angel One credentials from environment"""
        creds = env_config.get_angel_credentials()
        self.api_key = creds['api_key']
        self.client_id = creds['client_id']
        self.password = creds['password']
        self.totp_secret = creds['totp_secret']
        
        if not all([self.api_key, self.client_id, self.password]):
            raise ValueError("Missing Angel One credentials in environment variables")
    
    def generate_totp(self) -> str:
        """Generate TOTP for 2FA authentication"""
        if not self.totp_secret:
            raise ValueError("TOTP secret not configured")
        
        totp = pyotp.TOTP(self.totp_secret)
        return totp.now()
    
    def login(self) -> bool:
        """Login to Angel One and get auth tokens"""
        try:
            totp = self.generate_totp()
            
            login_data = {
                "clientcode": self.client_id,
                "password": self.password,
                "totp": totp
            }
            
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "X-UserType": "USER",
                "X-SourceID": "WEB",
                "X-ClientLocalIP": "***********",
                "X-ClientPublicIP": "**************",
                "X-MACAddress": "fe80::216:3eff:fe00:1362",
                "X-PrivateKey": self.api_key
            }
            
            response = self.session.post(
                angel_config.LOGIN_URL,
                data=json.dumps(login_data),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    self.auth_token = result['data']['jwtToken']
                    self.refresh_token = result['data']['refreshToken']
                    self.feed_token = result['data']['feedToken']
                    self.is_connected = True
                    
                    logger.info("Successfully logged in to Angel One")
                    return True
                else:
                    logger.error(f"Login failed: {result.get('message')}")
                    return False
            else:
                logger.error(f"Login request failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
    
    def logout(self) -> bool:
        """Logout from Angel One"""
        try:
            headers = self._get_headers()
            logout_data = {"clientcode": self.client_id}
            
            response = self.session.post(
                angel_config.LOGOUT_URL,
                data=json.dumps(logout_data),
                headers=headers
            )
            
            if response.status_code == 200:
                self.auth_token = None
                self.refresh_token = None
                self.feed_token = None
                self.is_connected = False
                logger.info("Successfully logged out from Angel One")
                return True
            else:
                logger.error(f"Logout failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Logout error: {str(e)}")
            return False
    
    def _get_headers(self) -> Dict[str, str]:
        """Get standard headers for API requests"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "X-UserType": "USER",
            "X-SourceID": "WEB",
            "X-ClientLocalIP": "***********",
            "X-ClientPublicIP": "**************",
            "X-MACAddress": "fe80::216:3eff:fe00:1362",
            "X-PrivateKey": self.api_key
        }
    
    def get_profile(self) -> Optional[Dict]:
        """Get user profile information"""
        try:
            headers = self._get_headers()
            response = self.session.get(angel_config.PROFILE_URL, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Profile fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Profile request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Profile fetch error: {str(e)}")
            return None
    
    def get_funds(self) -> Optional[Dict]:
        """Get available funds and margins"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/user/v1/getRMS"
            headers = self._get_headers()
            
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Funds fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Funds request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Funds fetch error: {str(e)}")
            return None
    
    def place_order(self, symbol: str, transaction_type: str, quantity: int, 
                   order_type: str = "MARKET", price: float = 0, 
                   trigger_price: float = 0, product: str = "MIS") -> Optional[str]:
        """Place a trading order"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/placeOrder"
            
            order_data = {
                "variety": "NORMAL",
                "tradingsymbol": symbol,
                "symboltoken": self._get_symbol_token(symbol),
                "transactiontype": transaction_type,
                "exchange": angel_config.EXCHANGE_NSE,
                "ordertype": order_type,
                "producttype": product,
                "duration": angel_config.DURATION_DAY,
                "price": str(price) if price > 0 else "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(quantity),
                "triggerprice": str(trigger_price) if trigger_price > 0 else "0"
            }
            
            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(order_data),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    order_id = result['data']['orderid']
                    logger.info(f"Order placed successfully: {order_id}")
                    return order_id
                else:
                    logger.error(f"Order placement failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Order request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Order placement error: {str(e)}")
            return None
    
    def modify_order(self, order_id: str, quantity: int = None, 
                    price: float = None, trigger_price: float = None) -> bool:
        """Modify an existing order"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/modifyOrder"
            
            modify_data = {
                "variety": "NORMAL",
                "orderid": order_id,
                "ordertype": "LIMIT",
                "producttype": "MIS",
                "duration": "DAY",
                "price": str(price) if price else "0",
                "quantity": str(quantity) if quantity else "0",
                "triggerprice": str(trigger_price) if trigger_price else "0"
            }
            
            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(modify_data),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    logger.info(f"Order modified successfully: {order_id}")
                    return True
                else:
                    logger.error(f"Order modification failed: {result.get('message')}")
                    return False
            else:
                logger.error(f"Modify request failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Order modification error: {str(e)}")
            return False
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/cancelOrder"
            
            cancel_data = {
                "variety": "NORMAL",
                "orderid": order_id
            }
            
            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(cancel_data),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    logger.info(f"Order cancelled successfully: {order_id}")
                    return True
                else:
                    logger.error(f"Order cancellation failed: {result.get('message')}")
                    return False
            else:
                logger.error(f"Cancel request failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Order cancellation error: {str(e)}")
            return False
    
    def get_order_book(self) -> Optional[List[Dict]]:
        """Get order book (all orders)"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/getOrderBook"
            headers = self._get_headers()
            
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Order book fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Order book request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Order book fetch error: {str(e)}")
            return None
    
    def get_positions(self) -> Optional[List[Dict]]:
        """Get current positions"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/getPosition"
            headers = self._get_headers()
            
            response = self.session.get(url, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Positions fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Positions request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Positions fetch error: {str(e)}")
            return None
    
    def _get_symbol_token(self, symbol: str) -> str:
        """Get symbol token for the given symbol"""
        # NSE symbol tokens for major stocks
        symbol_tokens = {
            'RELIANCE': '2885',
            'TCS': '11536',
            'HDFCBANK': '1333',
            'ICICIBANK': '4963',
            'INFOSYS': '1594',
            'ITC': '424',
            'SBIN': '3045',
            'BHARTIARTL': '10604',
            'LT': '11483',
            'HCLTECH': '7229',
            'MARUTI': '10999',
            'SUNPHARMA': '3351',
            'TITAN': '3506',
            'ULTRACEMCO': '11532',
            'ONGC': '2475',
            'NESTLEIND': '17963',
            'KOTAKBANK': '1922',
            'ASIANPAINT': '236',
            'WIPRO': '3787',
            'M&M': '519',
            'AXISBANK': '5900',
            'INDUSINDBK': '5258',
            'PNB': '10666',
            'BANKBARODA': '4668',
            'FEDERALBNK': '1023',
            'IDFCFIRSTB': '11184',
            'AUBANK': '21238',
            'BANDHANBNK': '2263',
            'NTPC': '11630',
            'POWERGRID': '14977',
            'COALINDIA': '20374'
        }

        return symbol_tokens.get(symbol, '1234')  # Default token if symbol not found
    
    def get_ltp(self, symbol: str) -> Optional[float]:
        """Get Last Traded Price for a symbol"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/getLTP"
            
            ltp_data = {
                "exchange": angel_config.EXCHANGE_NSE,
                "tradingsymbol": symbol,
                "symboltoken": self._get_symbol_token(symbol)
            }
            
            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(ltp_data),
                headers=headers
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return float(result['data']['ltp'])
                else:
                    logger.error(f"LTP fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"LTP request failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"LTP fetch error: {str(e)}")
            return None

    def get_historical_data(self, symbol: str, interval: str = "ONE_MINUTE",
                           from_date: str = None, to_date: str = None) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data for a symbol"""
        try:
            from datetime import datetime, timedelta

            # Default date range if not provided
            if not to_date:
                to_date = datetime.now().strftime("%Y-%m-%d %H:%M")
            if not from_date:
                from_dt = datetime.now() - timedelta(days=30)
                from_date = from_dt.strftime("%Y-%m-%d %H:%M")

            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/historical/v1/getCandleData"

            historical_data = {
                "exchange": angel_config.EXCHANGE_NSE,
                "symboltoken": self._get_symbol_token(symbol),
                "interval": interval,
                "fromdate": from_date,
                "todate": to_date
            }

            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(historical_data),
                headers=headers
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('status') and result.get('data'):
                    # Convert to DataFrame
                    data = result['data']
                    df = pd.DataFrame(data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])

                    # Convert timestamp to datetime
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df.set_index('timestamp', inplace=True)

                    # Convert price columns to float
                    price_columns = ['open', 'high', 'low', 'close']
                    for col in price_columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                    df['volume'] = pd.to_numeric(df['volume'], errors='coerce')

                    logger.info(f"Historical data fetched for {symbol}: {len(df)} records")
                    return df
                else:
                    logger.error(f"Historical data fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Historical data request failed: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Historical data fetch error: {str(e)}")
            return None

    def get_quote(self, symbol: str) -> Optional[Dict]:
        """Get detailed quote for a symbol"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/market/v1/quote/"

            quote_data = {
                "exchange": angel_config.EXCHANGE_NSE,
                "symboltoken": self._get_symbol_token(symbol),
                "mode": "FULL"
            }

            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(quote_data),
                headers=headers
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Quote fetch failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Quote request failed: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Quote fetch error: {str(e)}")
            return None

    def search_scrips(self, search_text: str) -> Optional[List[Dict]]:
        """Search for scrips/symbols"""
        try:
            url = f"{angel_config.BASE_URL}/rest/secure/angelbroking/order/v1/searchScrip"

            search_data = {
                "exchange": angel_config.EXCHANGE_NSE,
                "searchscrip": search_text
            }

            headers = self._get_headers()
            response = self.session.post(
                url,
                data=json.dumps(search_data),
                headers=headers
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('status'):
                    return result['data']
                else:
                    logger.error(f"Scrip search failed: {result.get('message')}")
                    return None
            else:
                logger.error(f"Scrip search request failed: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Scrip search error: {str(e)}")
            return None
