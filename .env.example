# Ultimate Trading Bot - Environment Configuration
# Copy this file to .env and fill in your actual values

# Flask Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
FLASK_ENV=development

# Google OAuth Configuration
# Get these from Google Cloud Console: https://console.cloud.google.com/
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Angel One SmartAPI Credentials
# Get these from https://smartapi.angelbroking.com/
ANGEL_API_KEY=your_api_key_here
ANGEL_CLIENT_ID=your_client_id_here
ANGEL_PASSWORD=your_password_here
ANGEL_TOTP_SECRET=your_totp_secret_here

# Trading Configuration
PAPER_TRADING=True
TRADING_CAPITAL=100
MAX_POSITION_SIZE=0.15
CONFIDENCE_THRESHOLD=0.42
MAX_DAILY_LOSS=50

# Sentiment Analysis APIs
NEWS_API_KEY=your_news_api_key_here
ALPHA_VANTAGE_KEY=your_alpha_vantage_key_here

# Database Configuration
DATABASE_URL=sqlite:///trading_bot.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log

# Email Alerts (Optional)
EMAIL_SMTP_SERVER=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_TO=<EMAIL>

# Telegram Alerts (Optional)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Risk Management
MAX_POSITIONS=4
MAX_RISK_PER_TRADE=2000
STOP_LOSS_PERCENTAGE=1.5
TARGET_PERCENTAGE=3.0
