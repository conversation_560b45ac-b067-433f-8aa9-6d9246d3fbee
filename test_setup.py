#!/usr/bin/env python3
"""
Test script to verify Angel One Trading Bot setup
"""
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append('src')

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import pandas as pd
        print("✓ pandas")
    except ImportError as e:
        print(f"✗ pandas: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ numpy")
    except ImportError as e:
        print(f"✗ numpy: {e}")
        return False
    
    try:
        import talib
        print("✓ talib")
    except ImportError as e:
        print(f"✗ talib: {e}")
        print("  Install TA-Lib: https://github.com/mrjbq7/ta-lib")
        return False
    
    try:
        import requests
        print("✓ requests")
    except ImportError as e:
        print(f"✗ requests: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✓ python-dotenv")
    except ImportError as e:
        print(f"✗ python-dotenv: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import trading_config, angel_config, stock_universe
        print("✓ Configuration loaded")
        
        print(f"  - Initial Capital: ₹{trading_config.INITIAL_CAPITAL:,.2f}")
        print(f"  - Max Risk Per Trade: {trading_config.MAX_RISK_PER_TRADE:.1%}")
        print(f"  - Max Positions: {trading_config.MAX_POSITIONS}")
        print(f"  - Trading Symbols: {len(stock_universe.HIGH_VOLUME_STOCKS)} stocks")
        
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_environment():
    """Test environment variables"""
    print("\nTesting environment...")
    
    if not os.path.exists('.env'):
        print("✗ .env file not found")
        print("  Run: python scripts/setup_bot.py")
        return False
    
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['ANGEL_API_KEY', 'ANGEL_CLIENT_ID', 'ANGEL_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if os.getenv(var):
            print(f"✓ {var}")
        else:
            print(f"✗ {var} - missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"  Missing variables: {', '.join(missing_vars)}")
        return False
    
    return True

def test_modules():
    """Test bot modules"""
    print("\nTesting bot modules...")
    
    try:
        from angel_api import AngelOneAPI
        print("✓ Angel One API module")
    except Exception as e:
        print(f"✗ Angel One API module: {e}")
        return False
    
    try:
        from risk_manager import RiskManager
        print("✓ Risk Manager module")
    except Exception as e:
        print(f"✗ Risk Manager module: {e}")
        return False
    
    try:
        from technical_analysis import TechnicalAnalyzer
        print("✓ Technical Analysis module")
    except Exception as e:
        print(f"✗ Technical Analysis module: {e}")
        return False
    
    try:
        from trading_bot import TradingBot
        print("✓ Trading Bot module")
    except Exception as e:
        print(f"✗ Trading Bot module: {e}")
        return False
    
    return True

def test_directories():
    """Test required directories"""
    print("\nTesting directories...")
    
    required_dirs = ['logs', 'src']
    
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"✓ {directory}/")
        else:
            print(f"✗ {directory}/ - missing")
            Path(directory).mkdir(exist_ok=True)
            print(f"  Created {directory}/")
    
    return True

def test_technical_analysis():
    """Test technical analysis with sample data"""
    print("\nTesting technical analysis...")
    
    try:
        import pandas as pd
        import numpy as np
        from technical_analysis import TechnicalAnalyzer
        
        # Create sample OHLCV data
        dates = pd.date_range('2024-01-01', periods=100, freq='1min')
        np.random.seed(42)
        
        # Generate realistic price data
        base_price = 1000
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        df = pd.DataFrame({
            'open': prices,
            'high': [p * 1.005 for p in prices],
            'low': [p * 0.995 for p in prices],
            'close': prices,
            'volume': np.random.randint(10000, 100000, 100)
        }, index=dates)
        
        analyzer = TechnicalAnalyzer()
        indicators = analyzer.calculate_indicators(df)
        
        if indicators:
            print("✓ Technical indicators calculated")
            print(f"  - Indicators: {list(indicators.keys())}")
            return True
        else:
            print("✗ Technical indicators failed")
            return False
            
    except Exception as e:
        print(f"✗ Technical analysis test failed: {e}")
        return False

def test_risk_manager():
    """Test risk manager"""
    print("\nTesting risk manager...")
    
    try:
        from risk_manager import RiskManager
        
        rm = RiskManager()
        
        # Test position size calculation
        position_size = rm.calculate_position_size(
            entry_price=1000,
            stop_loss=980,
            risk_amount=2000
        )
        
        if position_size > 0:
            print(f"✓ Position size calculation: {position_size} shares")
        else:
            print("✗ Position size calculation failed")
            return False
        
        # Test trade validation
        is_valid, message = rm.validate_trade(
            symbol="RELIANCE",
            side="BUY",
            quantity=position_size,
            entry_price=1000,
            stop_loss=980
        )
        
        if is_valid:
            print("✓ Trade validation passed")
        else:
            print(f"✗ Trade validation failed: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Risk manager test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Angel One Trading Bot - Setup Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Environment", test_environment),
        ("Modules", test_modules),
        ("Directories", test_directories),
        ("Technical Analysis", test_technical_analysis),
        ("Risk Manager", test_risk_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your bot is ready to run.")
        print("\nNext steps:")
        print("1. Run: python main.py")
        print("2. Monitor: tail -f logs/trading_bot.log")
    else:
        print("❌ Some tests failed. Please fix the issues before running the bot.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
