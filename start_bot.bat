@echo off
REM ULTIMATE TRADING BOT - WINDOWS STARTUP SCRIPT
REM Double-click this file to start the bot control dashboard

echo 🤖 ULTIMATE TRADING BOT - WINDOWS STARTUP
echo ==========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo 💡 Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if bot files exist
if not exist "deploy_ultimate_bot.py" (
    echo ❌ Bot files not found in current directory
    echo 💡 Make sure you're in the correct folder
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found
    echo 💡 Copy .env.example to .env and configure your credentials
    echo.
    echo Would you like to create .env from template? (Y/N)
    set /p create_env=
    if /i "%create_env%"=="Y" (
        copy ".env.example" ".env"
        echo ✅ .env file created from template
        echo 💡 Please edit .env file with your Angel One credentials
        echo.
    )
)

REM Create logs directory
if not exist "logs" mkdir logs

echo 🚀 Starting Bot Control Dashboard...
echo.
echo 📋 AVAILABLE OPTIONS:
echo    1. Paper Trading (Safe testing)
echo    2. Live Trading (Real money)
echo    3. View Performance
echo    4. View Logs
echo    5. Configuration
echo.

REM Start the control dashboard
python bot_control_dashboard.py

echo.
echo 👋 Bot Control Dashboard closed
pause
