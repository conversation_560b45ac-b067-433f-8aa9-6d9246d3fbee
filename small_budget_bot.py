#!/usr/bin/env python3
"""
₹100 Paper Trading Bot - Maximum Returns with High Accuracy
Optimized for small budget aggressive trading
"""
import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
import time

# Add src to path
sys.path.append('src')

from config_small_budget import (
    small_budget_config, high_accuracy_strategy, high_freq_stocks, 
    performance_config, is_high_volatility_time, PerformanceTracker
)
from angel_api import AngelOneAPI
from technical_analysis import TechnicalAnalyzer, TechnicalSignal
from risk_manager import RiskManager
from logger_config import setup_logging

# Setup logging
logger = setup_logging()

class SmallBudgetTradingBot:
    """Specialized trading bot for ₹100 paper trading"""
    
    def __init__(self):
        self.angel_api = AngelOneAPI()
        self.analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
        self.performance_tracker = PerformanceTracker()
        
        # Override risk manager settings for small budget
        self.risk_manager.initial_capital = small_budget_config.INITIAL_CAPITAL
        self.risk_manager.available_capital = small_budget_config.INITIAL_CAPITAL
        self.risk_manager.max_daily_loss = small_budget_config.MAX_DAILY_LOSS * small_budget_config.INITIAL_CAPITAL
        self.risk_manager.max_positions = small_budget_config.MAX_POSITIONS
        
        # Trading state
        self.is_logged_in = False
        self.paper_trading = True  # Always paper trading for ₹100
        self.trading_active = False
        self.market_data_cache = {}
        
        # Performance tracking
        self.daily_trades = []
        self.total_pnl = 0.0
        self.trade_count = 0
        
        # High accuracy settings
        self.min_signal_confidence = high_accuracy_strategy.MIN_SIGNAL_CONFIDENCE
        self.min_confluence_signals = high_accuracy_strategy.MIN_CONFLUENCE_SIGNALS
        
        logger.info("₹100 Paper Trading Bot initialized")
        logger.info(f"Target: {performance_config.DAILY_TARGET_PCT:.0f}% daily return (₹{performance_config.DAILY_TARGET_PCT})")
    
    async def login(self):
        """Login to Angel One API"""
        try:
            success = self.angel_api.login()
            if success:
                self.is_logged_in = True
                logger.info("✅ Logged in to Angel One API")
                
                # Get profile
                profile = self.angel_api.get_profile()
                if profile:
                    logger.info(f"Trading as: {profile.get('name', 'Unknown')}")
                
                return True
            else:
                logger.error("❌ Failed to login to Angel One API")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
    
    async def start_trading(self):
        """Start the trading bot"""
        logger.info("🚀 Starting ₹100 Paper Trading Bot")
        logger.info("=" * 60)
        logger.info(f"Initial Capital: ₹{small_budget_config.INITIAL_CAPITAL}")
        logger.info(f"Daily Target: ₹{performance_config.DAILY_TARGET_PCT} ({performance_config.DAILY_TARGET_PCT:.0f}%)")
        logger.info(f"Max Daily Loss: ₹{small_budget_config.MAX_DAILY_LOSS * small_budget_config.INITIAL_CAPITAL}")
        logger.info(f"Risk per Trade: {small_budget_config.MAX_RISK_PER_TRADE:.0%}")
        logger.info(f"Target Stocks: {high_freq_stocks.HIGH_ACCURACY_STOCKS}")
        
        # Login first
        if not await self.login():
            logger.error("Cannot start trading without API login")
            return
        
        self.trading_active = True
        logger.info("🎯 Trading activated - hunting for high-accuracy signals...")
        
        try:
            while self.trading_active:
                # Check if should stop trading
                should_stop, reason = self.performance_tracker.should_stop_trading()
                if should_stop:
                    logger.info(f"🛑 Stopping trading: {reason}")
                    break
                
                # Only trade during high volatility periods for maximum returns
                if not is_high_volatility_time():
                    logger.debug("⏰ Waiting for high volatility period...")
                    await asyncio.sleep(60)  # Check every minute
                    continue
                
                # Main trading cycle
                await self._trading_cycle()
                
                # Wait before next cycle
                await asyncio.sleep(performance_config.SIGNAL_REFRESH_SECONDS)
                
        except KeyboardInterrupt:
            logger.info("👋 Trading stopped by user")
        except Exception as e:
            logger.error(f"Trading error: {str(e)}")
        finally:
            await self._cleanup()
    
    async def _trading_cycle(self):
        """Main trading cycle"""
        try:
            # Update market data
            await self._update_market_data()
            
            # Update position prices
            await self._update_position_prices()
            
            # Check exit conditions
            await self._check_exit_conditions()
            
            # Look for new signals if no position
            if len(self.risk_manager.positions) == 0:
                await self._scan_for_high_accuracy_signals()
            
            # Log performance every 10 cycles
            if self.trade_count % 10 == 0:
                await self._log_performance()
                
        except Exception as e:
            logger.error(f"Trading cycle error: {str(e)}")
    
    async def _update_market_data(self):
        """Update market data for target stocks"""
        try:
            for symbol in high_freq_stocks.HIGH_ACCURACY_STOCKS:
                # Get current price
                ltp = self.angel_api.get_ltp(symbol)
                if ltp:
                    if symbol not in self.market_data_cache:
                        self.market_data_cache[symbol] = []
                    
                    self.market_data_cache[symbol].append({
                        'timestamp': datetime.now(),
                        'price': ltp
                    })
                    
                    # Keep only last 100 data points
                    if len(self.market_data_cache[symbol]) > 100:
                        self.market_data_cache[symbol] = self.market_data_cache[symbol][-100:]
                        
        except Exception as e:
            logger.error(f"Market data update error: {str(e)}")
    
    async def _update_position_prices(self):
        """Update current position prices"""
        try:
            if not self.risk_manager.positions:
                return
            
            price_updates = {}
            for symbol in self.risk_manager.positions.keys():
                ltp = self.angel_api.get_ltp(symbol)
                if ltp:
                    price_updates[symbol] = ltp
            
            if price_updates:
                self.risk_manager.update_position_prices(price_updates)
                
        except Exception as e:
            logger.error(f"Position price update error: {str(e)}")
    
    async def _check_exit_conditions(self):
        """Check for exit conditions"""
        try:
            # Check stop losses
            stop_loss_hits = self.risk_manager.check_stop_losses()
            for symbol in stop_loss_hits:
                await self._exit_position(symbol, "Stop Loss Hit")
            
            # Check targets
            target_hits = self.risk_manager.check_targets()
            for symbol in target_hits:
                await self._exit_position(symbol, "Target Hit")
            
            # Check time-based exits (near market close)
            now = datetime.now(small_budget_config.TIMEZONE)
            if now.strftime("%H:%M") >= "15:10":  # 10 minutes before close
                for symbol in list(self.risk_manager.positions.keys()):
                    await self._exit_position(symbol, "End of Day")
                    
        except Exception as e:
            logger.error(f"Exit condition check error: {str(e)}")
    
    async def _scan_for_high_accuracy_signals(self):
        """Scan for high accuracy trading signals"""
        try:
            for symbol in high_freq_stocks.HIGH_ACCURACY_STOCKS:
                # Get historical data
                df = self.angel_api.get_historical_data(symbol, "ONE_MINUTE")
                if df is None or len(df) < 50:
                    continue
                
                # Generate signals
                signals = self.analyzer.analyze_stock(df, symbol)
                if not signals:
                    continue
                
                # Filter for high confidence signals
                high_conf_signals = [s for s in signals if s.confidence >= self.min_signal_confidence]
                
                if len(high_conf_signals) >= self.min_confluence_signals:
                    # Get confluence signal
                    confluence_signal = self.analyzer.get_confluence_signal(high_conf_signals)
                    
                    if confluence_signal and confluence_signal.confidence >= self.min_signal_confidence:
                        logger.info(f"🎯 HIGH ACCURACY SIGNAL: {symbol} {confluence_signal.signal_type}")
                        logger.info(f"   Confidence: {confluence_signal.confidence:.1%}")
                        logger.info(f"   Strategies: {len(high_conf_signals)}")
                        
                        await self._process_signal(confluence_signal)
                        break  # Only one position at a time
                        
        except Exception as e:
            logger.error(f"Signal scanning error: {str(e)}")
    
    async def _process_signal(self, signal: TechnicalSignal):
        """Process a high accuracy trading signal"""
        try:
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                signal.entry_price, signal.stop_loss
            )
            
            if position_size <= 0:
                logger.warning(f"Position size 0 for {signal.symbol} - skipping")
                return
            
            # Validate trade
            is_valid, message = self.risk_manager.validate_trade(
                signal.symbol, signal.signal_type, position_size,
                signal.entry_price, signal.stop_loss
            )
            
            if not is_valid:
                logger.warning(f"Trade validation failed: {message}")
                return
            
            # Execute trade (paper trading)
            success = self.risk_manager.add_position(
                symbol=signal.symbol,
                quantity=position_size,
                entry_price=signal.entry_price,
                side=signal.signal_type,
                stop_loss=signal.stop_loss,
                target=signal.target,
                order_id=f"PAPER_{signal.symbol}_{int(time.time())}"
            )
            
            if success:
                position_value = position_size * signal.entry_price
                logger.info(f"✅ TRADE EXECUTED: {signal.symbol}")
                logger.info(f"   Side: {signal.signal_type}")
                logger.info(f"   Quantity: {position_size}")
                logger.info(f"   Entry: ₹{signal.entry_price:.2f}")
                logger.info(f"   Stop Loss: ₹{signal.stop_loss:.2f}")
                logger.info(f"   Target: ₹{signal.target:.2f}")
                logger.info(f"   Position Value: ₹{position_value:.2f}")
                logger.info(f"   Risk: ₹{abs(signal.entry_price - signal.stop_loss) * position_size:.2f}")
                
                self.trade_count += 1
                
        except Exception as e:
            logger.error(f"Signal processing error: {str(e)}")
    
    async def _exit_position(self, symbol: str, reason: str):
        """Exit a position"""
        try:
            if symbol not in self.risk_manager.positions:
                return
            
            position = self.risk_manager.positions[symbol]
            current_price = self.angel_api.get_ltp(symbol)
            
            if not current_price:
                logger.error(f"Could not get exit price for {symbol}")
                return
            
            # Calculate P&L
            realized_pnl = self.risk_manager.remove_position(symbol, current_price, reason)
            
            if realized_pnl is not None:
                self.total_pnl += realized_pnl
                self.performance_tracker.current_capital += realized_pnl
                
                # Update performance tracker
                if realized_pnl > 0:
                    self.performance_tracker.winning_trades += 1
                self.performance_tracker.total_trades += 1
                
                logger.info(f"🔄 POSITION EXITED: {symbol}")
                logger.info(f"   Reason: {reason}")
                logger.info(f"   Exit Price: ₹{current_price:.2f}")
                logger.info(f"   P&L: ₹{realized_pnl:.2f}")
                logger.info(f"   Total P&L: ₹{self.total_pnl:.2f}")
                logger.info(f"   Current Capital: ₹{self.performance_tracker.current_capital:.2f}")
                
                returns = self.performance_tracker.calculate_returns()
                logger.info(f"   Returns: {returns:.1f}%")
                
        except Exception as e:
            logger.error(f"Position exit error: {str(e)}")
    
    async def _log_performance(self):
        """Log current performance"""
        try:
            returns = self.performance_tracker.calculate_returns()
            win_rate = self.performance_tracker.calculate_win_rate()
            
            logger.info("📊 PERFORMANCE UPDATE")
            logger.info(f"   Capital: ₹{self.performance_tracker.current_capital:.2f}")
            logger.info(f"   Returns: {returns:.1f}%")
            logger.info(f"   P&L: ₹{self.total_pnl:.2f}")
            logger.info(f"   Trades: {self.performance_tracker.total_trades}")
            logger.info(f"   Win Rate: {win_rate:.1f}%")
            logger.info(f"   Target: ₹{performance_config.DAILY_TARGET_PCT} ({performance_config.DAILY_TARGET_PCT:.0f}%)")
            
        except Exception as e:
            logger.error(f"Performance logging error: {str(e)}")
    
    async def _cleanup(self):
        """Cleanup and final reporting"""
        try:
            # Close any remaining positions
            for symbol in list(self.risk_manager.positions.keys()):
                await self._exit_position(symbol, "Bot Shutdown")
            
            # Final performance report
            returns = self.performance_tracker.calculate_returns()
            win_rate = self.performance_tracker.calculate_win_rate()
            
            logger.info("🏁 FINAL PERFORMANCE REPORT")
            logger.info("=" * 50)
            logger.info(f"Starting Capital: ₹{small_budget_config.INITIAL_CAPITAL}")
            logger.info(f"Final Capital: ₹{self.performance_tracker.current_capital:.2f}")
            logger.info(f"Total Returns: {returns:.1f}%")
            logger.info(f"Total P&L: ₹{self.total_pnl:.2f}")
            logger.info(f"Total Trades: {self.performance_tracker.total_trades}")
            logger.info(f"Winning Trades: {self.performance_tracker.winning_trades}")
            logger.info(f"Win Rate: {win_rate:.1f}%")
            
            if returns >= performance_config.DAILY_TARGET_PCT:
                logger.info("🎉 DAILY TARGET ACHIEVED!")
            elif returns > 0:
                logger.info("✅ Positive returns achieved")
            else:
                logger.info("📉 Negative returns - review strategy")
            
            # Logout
            if self.is_logged_in:
                self.angel_api.logout()
                logger.info("Logged out from Angel One API")
                
        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")

async def main():
    """Main function"""
    print("💰 ₹100 Paper Trading Bot - Maximum Returns")
    print("=" * 60)
    print("🎯 Target: 20% daily return (₹20 profit)")
    print("🛡️ Risk: 15% per trade, 25% daily loss limit")
    print("📈 Strategy: High accuracy signals only")
    print("⏰ Trading: High volatility periods only")
    print()
    
    # Check if credentials are configured
    try:
        from config import env_config
        creds = env_config.get_angel_credentials()
        
        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            print("❌ Angel One credentials not configured!")
            print("Run: python scripts/setup_bot.py")
            return 1
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return 1
    
    # Create and start bot
    bot = SmallBudgetTradingBot()
    
    try:
        await bot.start_trading()
        return 0
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Bot error: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
