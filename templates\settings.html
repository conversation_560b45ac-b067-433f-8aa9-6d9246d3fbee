{% extends "base.html" %}

{% block title %}Settings - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-cog"></i> Bot Settings
        </h1>
    </div>
</div>

<div class="row">
    <!-- Trading Settings -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> Trading Configuration
            </div>
            <div class="card-body">
                <form id="tradingSettings">
                    <div class="mb-3">
                        <label for="capital" class="form-label">Trading Capital</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="capital" value="{{ current_settings.capital }}" min="100" max="1000000">
                        </div>
                        <small class="form-text text-muted">Amount available for trading</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxPositions" class="form-label">Maximum Positions</label>
                        <input type="number" class="form-control" id="maxPositions" value="5" min="1" max="20">
                        <small class="form-text text-muted">Maximum number of open positions</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="positionSize" class="form-label">Position Size (%)</label>
                        <input type="range" class="form-range" id="positionSize" min="5" max="50" value="15">
                        <div class="d-flex justify-content-between">
                            <small>5%</small>
                            <small id="positionSizeValue">15%</small>
                            <small>50%</small>
                        </div>
                        <small class="form-text text-muted">Percentage of capital per trade</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confidenceThreshold" class="form-label">Confidence Threshold</label>
                        <input type="range" class="form-range" id="confidenceThreshold" min="20" max="80" value="42">
                        <div class="d-flex justify-content-between">
                            <small>20%</small>
                            <small id="confidenceThresholdValue">42%</small>
                            <small>80%</small>
                        </div>
                        <small class="form-text text-muted">Minimum confidence required for trades</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="tradingInterval" class="form-label">Trading Interval (seconds)</label>
                        <select class="form-select" id="tradingInterval">
                            <option value="30" selected>30 seconds</option>
                            <option value="60">1 minute</option>
                            <option value="300">5 minutes</option>
                            <option value="600">10 minutes</option>
                        </select>
                        <small class="form-text text-muted">How often to check for new trades</small>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Trading Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Risk Management -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Risk Management
            </div>
            <div class="card-body">
                <form id="riskSettings">
                    <div class="mb-3">
                        <label for="stopLoss" class="form-label">Stop Loss (%)</label>
                        <input type="range" class="form-range" id="stopLoss" min="1" max="10" value="2">
                        <div class="d-flex justify-content-between">
                            <small>1%</small>
                            <small id="stopLossValue">2%</small>
                            <small>10%</small>
                        </div>
                        <small class="form-text text-muted">Maximum loss per trade</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="takeProfit" class="form-label">Take Profit (%)</label>
                        <input type="range" class="form-range" id="takeProfit" min="2" max="20" value="6">
                        <div class="d-flex justify-content-between">
                            <small>2%</small>
                            <small id="takeProfitValue">6%</small>
                            <small>20%</small>
                        </div>
                        <small class="form-text text-muted">Target profit per trade</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dailyLossLimit" class="form-label">Daily Loss Limit</label>
                        <div class="input-group">
                            <span class="input-group-text">₹</span>
                            <input type="number" class="form-control" id="dailyLossLimit" value="50" min="10" max="1000">
                        </div>
                        <small class="form-text text-muted">Maximum loss per day</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoStopLoss" checked>
                            <label class="form-check-label" for="autoStopLoss">
                                Auto Stop Loss
                            </label>
                        </div>
                        <small class="form-text text-muted">Automatically set stop loss on all trades</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="trailingStop">
                            <label class="form-check-label" for="trailingStop">
                                Trailing Stop Loss
                            </label>
                        </div>
                        <small class="form-text text-muted">Move stop loss with profitable trades</small>
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-shield-alt"></i> Save Risk Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Market Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-globe"></i> Market Configuration
            </div>
            <div class="card-body">
                <div class="row">
                    {% for key, market in markets.items() %}
                    <div class="col-md-6 col-lg-3 mb-3">
                        <div class="card border">
                            <div class="card-body">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" id="market_{{ key }}" 
                                           {% if key == current_settings.current_market %}checked{% endif %}>
                                    <label class="form-check-label" for="market_{{ key }}">
                                        <strong>{{ market.name }}</strong>
                                    </label>
                                </div>
                                
                                <small class="text-muted d-block">Currency: {{ market.currency }}</small>
                                <small class="text-muted d-block">Symbols: {{ market.symbols|length }}</small>
                                
                                <div class="mt-2">
                                    <label class="form-label" style="font-size: 0.8rem;">Confidence Threshold:</label>
                                    <input type="range" class="form-range form-range-sm" 
                                           id="threshold_{{ key }}" min="20" max="80" value="42">
                                    <small id="threshold_{{ key }}_value">42%</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-success" onclick="saveMarketSettings()">
                        <i class="fas fa-save"></i> Save Market Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- API Configuration -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-key"></i> API Configuration
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Security Notice:</strong> API keys are stored securely and never displayed in full.
                </div>
                
                <form id="apiSettings">
                    <div class="mb-3">
                        <label for="angelApiKey" class="form-label">Angel One API Key</label>
                        <input type="password" class="form-control" id="angelApiKey" placeholder="••••••••••••••••">
                        <small class="form-text text-muted">Your Angel One SmartAPI key</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="angelClientId" class="form-label">Angel One Client ID</label>
                        <input type="text" class="form-control" id="angelClientId" placeholder="Enter client ID">
                    </div>
                    
                    <div class="mb-3">
                        <label for="newsApiKey" class="form-label">News API Key (Optional)</label>
                        <input type="password" class="form-control" id="newsApiKey" placeholder="••••••••••••••••">
                        <small class="form-text text-muted">For enhanced sentiment analysis</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableLiveTrading">
                            <label class="form-check-label" for="enableLiveTrading">
                                Enable Live Trading
                            </label>
                        </div>
                        <small class="form-text text-danger">⚠️ Only enable after thorough testing</small>
                    </div>
                    
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-key"></i> Update API Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-server"></i> System Status
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Bot Status:</span>
                        <span class="{% if current_settings.is_running %}status-running{% else %}status-stopped{% endif %}">
                            {% if current_settings.is_running %}RUNNING{% else %}STOPPED{% endif %}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Current Market:</span>
                        <span class="text-primary">{{ markets[current_settings.current_market].name }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Trading Mode:</span>
                        <span class="badge bg-{% if current_settings.trading_mode == 'paper' %}success{% else %}warning{% endif %}">
                            {{ current_settings.trading_mode.upper() }}
                        </span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Open Positions:</span>
                        <span class="text-info">{{ current_settings.positions }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Total Trades:</span>
                        <span class="text-success">{{ current_settings.performance.total_trades }}</span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Win Rate:</span>
                        <span class="text-primary">{{ "%.1f"|format(current_settings.performance.win_rate) }}%</span>
                    </div>
                </div>
                
                <hr>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info" onclick="exportSettings()">
                        <i class="fas fa-download"></i> Export Settings
                    </button>
                    <button class="btn btn-outline-secondary" onclick="resetSettings()">
                        <i class="fas fa-undo"></i> Reset to Defaults
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Update range slider values
    document.getElementById('positionSize').addEventListener('input', function() {
        document.getElementById('positionSizeValue').textContent = this.value + '%';
    });
    
    document.getElementById('confidenceThreshold').addEventListener('input', function() {
        document.getElementById('confidenceThresholdValue').textContent = this.value + '%';
    });
    
    document.getElementById('stopLoss').addEventListener('input', function() {
        document.getElementById('stopLossValue').textContent = this.value + '%';
    });
    
    document.getElementById('takeProfit').addEventListener('input', function() {
        document.getElementById('takeProfitValue').textContent = this.value + '%';
    });
    
    // Market threshold sliders
    {% for key, market in markets.items() %}
    document.getElementById('threshold_{{ key }}').addEventListener('input', function() {
        document.getElementById('threshold_{{ key }}_value').textContent = this.value + '%';
    });
    {% endfor %}
    
    // Form submissions
    document.getElementById('tradingSettings').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const settings = {
            capital: document.getElementById('capital').value,
            maxPositions: document.getElementById('maxPositions').value,
            positionSize: document.getElementById('positionSize').value,
            confidenceThreshold: document.getElementById('confidenceThreshold').value,
            tradingInterval: document.getElementById('tradingInterval').value
        };
        
        // Save settings (you can implement API call here)
        console.log('Trading settings:', settings);
        
        // Show success message
        showAlert('Trading settings saved successfully!', 'success');
    });
    
    document.getElementById('riskSettings').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const settings = {
            stopLoss: document.getElementById('stopLoss').value,
            takeProfit: document.getElementById('takeProfit').value,
            dailyLossLimit: document.getElementById('dailyLossLimit').value,
            autoStopLoss: document.getElementById('autoStopLoss').checked,
            trailingStop: document.getElementById('trailingStop').checked
        };
        
        console.log('Risk settings:', settings);
        showAlert('Risk management settings saved successfully!', 'warning');
    });
    
    document.getElementById('apiSettings').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const settings = {
            angelApiKey: document.getElementById('angelApiKey').value,
            angelClientId: document.getElementById('angelClientId').value,
            newsApiKey: document.getElementById('newsApiKey').value,
            enableLiveTrading: document.getElementById('enableLiveTrading').checked
        };
        
        console.log('API settings updated');
        showAlert('API settings updated successfully!', 'danger');
    });
    
    function saveMarketSettings() {
        const marketSettings = {};
        
        {% for key, market in markets.items() %}
        marketSettings['{{ key }}'] = {
            enabled: document.getElementById('market_{{ key }}').checked,
            threshold: document.getElementById('threshold_{{ key }}').value
        };
        {% endfor %}
        
        console.log('Market settings:', marketSettings);
        showAlert('Market settings saved successfully!', 'success');
    }
    
    function exportSettings() {
        const allSettings = {
            trading: {
                capital: document.getElementById('capital').value,
                maxPositions: document.getElementById('maxPositions').value,
                positionSize: document.getElementById('positionSize').value,
                confidenceThreshold: document.getElementById('confidenceThreshold').value,
                tradingInterval: document.getElementById('tradingInterval').value
            },
            risk: {
                stopLoss: document.getElementById('stopLoss').value,
                takeProfit: document.getElementById('takeProfit').value,
                dailyLossLimit: document.getElementById('dailyLossLimit').value,
                autoStopLoss: document.getElementById('autoStopLoss').checked,
                trailingStop: document.getElementById('trailingStop').checked
            }
        };
        
        const dataStr = JSON.stringify(allSettings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'trading_bot_settings.json';
        link.click();
        
        showAlert('Settings exported successfully!', 'info');
    }
    
    function resetSettings() {
        if (confirm('Are you sure you want to reset all settings to defaults?')) {
            // Reset all form values to defaults
            document.getElementById('capital').value = 100;
            document.getElementById('maxPositions').value = 5;
            document.getElementById('positionSize').value = 15;
            document.getElementById('confidenceThreshold').value = 42;
            document.getElementById('stopLoss').value = 2;
            document.getElementById('takeProfit').value = 6;
            document.getElementById('dailyLossLimit').value = 50;
            
            // Update display values
            document.getElementById('positionSizeValue').textContent = '15%';
            document.getElementById('confidenceThresholdValue').textContent = '42%';
            document.getElementById('stopLossValue').textContent = '2%';
            document.getElementById('takeProfitValue').textContent = '6%';
            
            showAlert('Settings reset to defaults!', 'secondary');
        }
    }
    
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of main content
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alertDiv, mainContent.firstChild);
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
</script>
{% endblock %}
