#!/usr/bin/env python3
"""
Real Data Paper Trading Bot
Uses REAL Angel One SmartAPI data for testing with paper trading
"""
import sys
import os
import asyncio
import logging
from datetime import datetime, timedelta
import time
import json

# Add src to path
sys.path.append('src')

from dotenv import load_dotenv
load_dotenv()

# Import our modules
from angel_api import AngelOneAPI
from technical_analysis import TechnicalAnalyzer, TechnicalSignal
from risk_manager import RiskManager
from config_small_budget import small_budget_config, high_freq_stocks, performance_config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/real_data_paper_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealDataPaperTradingBot:
    """Paper trading bot using real Angel One SmartAPI data"""
    
    def __init__(self):
        self.angel_api = AngelOneAPI()
        self.analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
        
        # Configure for ₹100 paper trading
        self.risk_manager.initial_capital = 100.0
        self.risk_manager.available_capital = 100.0
        self.risk_manager.max_daily_loss = 25.0
        self.risk_manager.max_positions = 1
        
        # Trading state
        self.is_logged_in = False
        self.paper_trading = True  # Always paper trading
        self.testing_mode = True   # Testing mode enabled
        self.trading_active = False
        
        # Performance tracking
        self.starting_capital = 100.0
        self.current_capital = 100.0
        self.daily_trades = []
        self.total_pnl = 0.0
        
        # Real data cache
        self.market_data_cache = {}
        self.last_price_update = {}
        
        logger.info("🧪 Real Data Paper Trading Bot initialized")
        logger.info(f"💰 Starting Capital: ₹{self.starting_capital}")
        logger.info(f"🎯 Daily Target: ₹{performance_config.DAILY_TARGET_PCT}")
        logger.info("📊 Mode: Paper Trading with REAL SmartAPI Data")
    
    async def login(self):
        """Login to Angel One API for real data"""
        try:
            logger.info("🔌 Connecting to Angel One SmartAPI...")
            success = self.angel_api.login()
            
            if success:
                self.is_logged_in = True
                logger.info("✅ Connected to Angel One SmartAPI")
                
                # Get real profile
                profile = self.angel_api.get_profile()
                if profile:
                    logger.info(f"👤 Trading as: {profile.get('name', 'Unknown')}")
                    logger.info(f"📱 Client ID: {profile.get('clientcode', 'Unknown')}")
                
                # Get real funds (for reference only)
                funds = self.angel_api.get_funds()
                if funds:
                    real_cash = funds.get('availablecash', 0)
                    logger.info(f"💳 Real Account Balance: ₹{real_cash}")
                    logger.info("⚠️  NOTE: Using paper trading - no real money will be used")
                
                return True
            else:
                logger.error("❌ Failed to connect to Angel One SmartAPI")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
    
    async def start_testing(self):
        """Start paper trading with real data"""
        logger.info("🚀 Starting Real Data Paper Trading")
        logger.info("=" * 60)
        logger.info("📊 Data Source: REAL Angel One SmartAPI")
        logger.info("💰 Trading Mode: PAPER TRADING (No real money)")
        logger.info("🧪 Purpose: Testing and validation")
        logger.info(f"🎯 Target: {performance_config.DAILY_TARGET_PCT:.0f}% daily return")
        logger.info(f"🛡️ Risk: {small_budget_config.MAX_RISK_PER_TRADE:.0%} per trade")
        
        # Login first
        if not await self.login():
            logger.error("Cannot start without API connection")
            return
        
        # Test real data fetching
        await self._test_real_data_access()
        
        self.trading_active = True
        logger.info("🎯 Paper trading activated with real market data")
        
        try:
            while self.trading_active:
                # Check if should stop
                if await self._should_stop_trading():
                    break
                
                # Main trading cycle with real data
                await self._real_data_trading_cycle()
                
                # Wait before next cycle
                await asyncio.sleep(30)  # 30 second cycles
                
        except KeyboardInterrupt:
            logger.info("👋 Trading stopped by user")
        except Exception as e:
            logger.error(f"Trading error: {str(e)}")
        finally:
            await self._cleanup()
    
    async def _test_real_data_access(self):
        """Test access to real market data"""
        logger.info("🧪 Testing real market data access...")
        
        test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        
        for symbol in test_symbols:
            try:
                # Test LTP (Last Traded Price)
                ltp = self.angel_api.get_ltp(symbol)
                if ltp:
                    logger.info(f"✅ {symbol} Real LTP: ₹{ltp:.2f}")
                else:
                    logger.warning(f"⚠️  Could not fetch LTP for {symbol}")
                
                # Test historical data
                df = self.angel_api.get_historical_data(symbol, "ONE_MINUTE")
                if df is not None and len(df) > 0:
                    latest_price = df['close'].iloc[-1]
                    data_time = df.index[-1]
                    logger.info(f"✅ {symbol} Historical Data: ₹{latest_price:.2f} at {data_time}")
                else:
                    logger.warning(f"⚠️  Could not fetch historical data for {symbol}")
                
                # Small delay between API calls
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"❌ Error testing {symbol}: {str(e)}")
        
        logger.info("✅ Real data access test completed")
    
    async def _real_data_trading_cycle(self):
        """Main trading cycle using real market data"""
        try:
            # Update real market data
            await self._update_real_market_data()
            
            # Update position prices with real data
            await self._update_position_prices_real()
            
            # Check exit conditions with real prices
            await self._check_exit_conditions_real()
            
            # Look for new signals if no position
            if len(self.risk_manager.positions) == 0:
                await self._scan_for_real_signals()
            
            # Log performance periodically
            await self._log_real_performance()
                
        except Exception as e:
            logger.error(f"Trading cycle error: {str(e)}")
    
    async def _update_real_market_data(self):
        """Update market data using real Angel One API"""
        try:
            current_time = datetime.now()
            
            for symbol in high_freq_stocks.HIGH_ACCURACY_STOCKS:
                # Get real LTP
                ltp = self.angel_api.get_ltp(symbol)
                
                if ltp:
                    # Cache real price data
                    if symbol not in self.market_data_cache:
                        self.market_data_cache[symbol] = []
                    
                    self.market_data_cache[symbol].append({
                        'timestamp': current_time,
                        'price': ltp,
                        'source': 'real_api'
                    })
                    
                    # Keep only last 100 data points
                    if len(self.market_data_cache[symbol]) > 100:
                        self.market_data_cache[symbol] = self.market_data_cache[symbol][-100:]
                    
                    self.last_price_update[symbol] = current_time
                    
                    logger.debug(f"📊 {symbol}: ₹{ltp:.2f} (Real)")
                
                # Rate limiting
                await asyncio.sleep(0.5)
                
        except Exception as e:
            logger.error(f"Real market data update error: {str(e)}")
    
    async def _update_position_prices_real(self):
        """Update position prices with real market data"""
        try:
            if not self.risk_manager.positions:
                return
            
            price_updates = {}
            for symbol in self.risk_manager.positions.keys():
                real_ltp = self.angel_api.get_ltp(symbol)
                if real_ltp:
                    price_updates[symbol] = real_ltp
                    logger.debug(f"📈 {symbol} position updated: ₹{real_ltp:.2f}")
            
            if price_updates:
                self.risk_manager.update_position_prices(price_updates)
                
        except Exception as e:
            logger.error(f"Position price update error: {str(e)}")
    
    async def _check_exit_conditions_real(self):
        """Check exit conditions using real prices"""
        try:
            # Check stop losses with real prices
            stop_loss_hits = self.risk_manager.check_stop_losses()
            for symbol in stop_loss_hits:
                await self._exit_position_real(symbol, "Stop Loss Hit")
            
            # Check targets with real prices
            target_hits = self.risk_manager.check_targets()
            for symbol in target_hits:
                await self._exit_position_real(symbol, "Target Hit")
            
            # Time-based exit (market close)
            now = datetime.now()
            if now.strftime("%H:%M") >= "15:20":
                for symbol in list(self.risk_manager.positions.keys()):
                    await self._exit_position_real(symbol, "Market Close")
                    
        except Exception as e:
            logger.error(f"Exit condition check error: {str(e)}")
    
    async def _scan_for_real_signals(self):
        """Scan for trading signals using real market data"""
        try:
            for symbol in high_freq_stocks.HIGH_ACCURACY_STOCKS:
                # Get real historical data for analysis
                df = self.angel_api.get_historical_data(symbol, "ONE_MINUTE")
                
                if df is None or len(df) < 50:
                    continue
                
                logger.debug(f"🔍 Analyzing {symbol} with {len(df)} real data points")
                
                # Generate signals using real data
                signals = self.analyzer.analyze_stock(df, symbol)
                
                if not signals:
                    continue
                
                # Filter for high confidence signals
                high_conf_signals = [s for s in signals if s.confidence >= 0.75]
                
                if len(high_conf_signals) >= 2:  # Require confluence
                    confluence_signal = self.analyzer.get_confluence_signal(high_conf_signals)
                    
                    if confluence_signal and confluence_signal.confidence >= 0.75:
                        logger.info(f"🎯 REAL DATA SIGNAL: {symbol} {confluence_signal.signal_type}")
                        logger.info(f"   Confidence: {confluence_signal.confidence:.1%}")
                        logger.info(f"   Entry: ₹{confluence_signal.entry_price:.2f}")
                        logger.info(f"   Strategies: {len(high_conf_signals)}")
                        logger.info(f"   Data Source: Real Angel One API")
                        
                        await self._process_real_signal(confluence_signal)
                        break  # Only one position at a time
                
                # Rate limiting
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Signal scanning error: {str(e)}")
    
    async def _process_real_signal(self, signal: TechnicalSignal):
        """Process signal with real data (paper trading)"""
        try:
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                signal.entry_price, signal.stop_loss
            )
            
            if position_size <= 0:
                logger.warning(f"Position size 0 for {signal.symbol} - skipping")
                return
            
            # Validate trade
            is_valid, message = self.risk_manager.validate_trade(
                signal.symbol, signal.signal_type, position_size,
                signal.entry_price, signal.stop_loss
            )
            
            if not is_valid:
                logger.warning(f"Trade validation failed: {message}")
                return
            
            # Execute PAPER trade with real prices
            success = self.risk_manager.add_position(
                symbol=signal.symbol,
                quantity=position_size,
                entry_price=signal.entry_price,
                side=signal.signal_type,
                stop_loss=signal.stop_loss,
                target=signal.target,
                order_id=f"PAPER_{signal.symbol}_{int(time.time())}"
            )
            
            if success:
                position_value = position_size * signal.entry_price
                
                logger.info(f"✅ PAPER TRADE EXECUTED (Real Data): {signal.symbol}")
                logger.info(f"   Side: {signal.signal_type}")
                logger.info(f"   Quantity: {position_size}")
                logger.info(f"   Entry: ₹{signal.entry_price:.2f} (Real Price)")
                logger.info(f"   Stop Loss: ₹{signal.stop_loss:.2f}")
                logger.info(f"   Target: ₹{signal.target:.2f}")
                logger.info(f"   Position Value: ₹{position_value:.2f}")
                logger.info(f"   🧪 Mode: PAPER TRADING (No real money used)")
                
                # Log trade for analysis
                trade_log = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': signal.symbol,
                    'side': signal.signal_type,
                    'quantity': position_size,
                    'entry_price': signal.entry_price,
                    'stop_loss': signal.stop_loss,
                    'target': signal.target,
                    'confidence': signal.confidence,
                    'data_source': 'real_angel_one_api',
                    'mode': 'paper_trading'
                }
                
                with open('logs/real_data_trades.log', 'a') as f:
                    f.write(json.dumps(trade_log) + '\n')
                
        except Exception as e:
            logger.error(f"Signal processing error: {str(e)}")
    
    async def _exit_position_real(self, symbol: str, reason: str):
        """Exit position using real market price"""
        try:
            if symbol not in self.risk_manager.positions:
                return
            
            # Get real exit price
            real_exit_price = self.angel_api.get_ltp(symbol)
            
            if not real_exit_price:
                logger.error(f"Could not get real exit price for {symbol}")
                return
            
            position = self.risk_manager.positions[symbol]
            
            # Calculate P&L with real price
            realized_pnl = self.risk_manager.remove_position(symbol, real_exit_price, reason)
            
            if realized_pnl is not None:
                self.total_pnl += realized_pnl
                self.current_capital += realized_pnl
                
                logger.info(f"🔄 PAPER POSITION EXITED (Real Data): {symbol}")
                logger.info(f"   Reason: {reason}")
                logger.info(f"   Exit Price: ₹{real_exit_price:.2f} (Real Price)")
                logger.info(f"   P&L: ₹{realized_pnl:.2f}")
                logger.info(f"   Total P&L: ₹{self.total_pnl:.2f}")
                logger.info(f"   Current Capital: ₹{self.current_capital:.2f}")
                
                returns = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
                logger.info(f"   Returns: {returns:.1f}%")
                
                # Log exit for analysis
                exit_log = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'exit_reason': reason,
                    'exit_price': real_exit_price,
                    'realized_pnl': realized_pnl,
                    'total_pnl': self.total_pnl,
                    'current_capital': self.current_capital,
                    'returns_pct': returns,
                    'data_source': 'real_angel_one_api',
                    'mode': 'paper_trading'
                }
                
                with open('logs/real_data_exits.log', 'a') as f:
                    f.write(json.dumps(exit_log) + '\n')
                
        except Exception as e:
            logger.error(f"Position exit error: {str(e)}")
    
    async def _should_stop_trading(self):
        """Check if should stop trading"""
        returns = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        
        # Stop if daily target reached
        if returns >= performance_config.DAILY_TARGET_PCT:
            logger.info(f"🎉 Daily target reached: {returns:.1f}%")
            return True
        
        # Stop if daily loss limit reached
        if returns <= -25:  # 25% loss limit
            logger.info(f"🛑 Daily loss limit reached: {returns:.1f}%")
            return True
        
        return False
    
    async def _log_real_performance(self):
        """Log performance with real data"""
        try:
            returns = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
            
            logger.info("📊 REAL DATA PERFORMANCE UPDATE")
            logger.info(f"   Capital: ₹{self.current_capital:.2f}")
            logger.info(f"   Returns: {returns:.1f}%")
            logger.info(f"   P&L: ₹{self.total_pnl:.2f}")
            logger.info(f"   Target: {performance_config.DAILY_TARGET_PCT:.0f}%")
            logger.info(f"   Data: Real Angel One SmartAPI")
            logger.info(f"   Mode: Paper Trading")
            
        except Exception as e:
            logger.error(f"Performance logging error: {str(e)}")
    
    async def _cleanup(self):
        """Cleanup and final reporting"""
        try:
            # Close any remaining positions
            for symbol in list(self.risk_manager.positions.keys()):
                await self._exit_position_real(symbol, "Bot Shutdown")
            
            # Final performance report
            returns = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
            
            logger.info("🏁 REAL DATA PAPER TRADING COMPLETE")
            logger.info("=" * 60)
            logger.info(f"📊 Data Source: Real Angel One SmartAPI")
            logger.info(f"💰 Starting Capital: ₹{self.starting_capital}")
            logger.info(f"💰 Final Capital: ₹{self.current_capital:.2f}")
            logger.info(f"📈 Total Returns: {returns:.1f}%")
            logger.info(f"💵 Total P&L: ₹{self.total_pnl:.2f}")
            logger.info(f"🧪 Mode: Paper Trading (No real money used)")
            
            if returns >= performance_config.DAILY_TARGET_PCT:
                logger.info("🎉 DAILY TARGET ACHIEVED with real data!")
            elif returns > 0:
                logger.info("✅ Positive returns with real market data")
            else:
                logger.info("📉 Negative returns - strategy needs optimization")
            
            # Logout
            if self.is_logged_in:
                self.angel_api.logout()
                logger.info("Disconnected from Angel One SmartAPI")
                
        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")

async def main():
    """Main function for real data paper trading"""
    print("🧪 Real Data Paper Trading Bot")
    print("=" * 60)
    print("📊 Data Source: REAL Angel One SmartAPI")
    print("💰 Trading Mode: PAPER TRADING (No real money)")
    print("🎯 Purpose: Testing with live market data")
    print("💵 Capital: ₹100 (simulated)")
    print()
    
    # Check credentials
    try:
        api_key = os.getenv('ANGEL_API_KEY')
        client_id = os.getenv('ANGEL_CLIENT_ID')
        password = os.getenv('ANGEL_PASSWORD')
        totp_secret = os.getenv('ANGEL_TOTP_SECRET')
        
        if not all([api_key, client_id, password, totp_secret]):
            print("❌ Angel One credentials not configured!")
            print("Edit .env file with your credentials:")
            print("- ANGEL_CLIENT_ID=your_client_id")
            print("- ANGEL_PASSWORD=your_password")
            print("- ANGEL_TOTP_SECRET=your_totp_secret")
            return 1
            
        print("✅ Credentials configured")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return 1
    
    # Create and start bot
    bot = RealDataPaperTradingBot()
    
    try:
        await bot.start_testing()
        return 0
    except KeyboardInterrupt:
        print("\n👋 Real data paper trading stopped by user")
        return 0
    except Exception as e:
        print(f"❌ Bot error: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(asyncio.run(main()))
