#!/usr/bin/env python3
"""
Real-time Performance Monitor for ₹100 Paper Trading
Live dashboard and analytics for maximum returns tracking
"""
import os
import sys
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List
import threading

# Add src to path
sys.path.append('src')

class PerformanceMonitor:
    """Real-time performance monitoring for ₹100 trading"""
    
    def __init__(self):
        self.starting_capital = 100.0
        self.current_capital = 100.0
        self.daily_target = 20.0  # ₹20 daily target
        self.trades = []
        self.hourly_pnl = {}
        self.running = False
        
    def start_monitoring(self):
        """Start real-time monitoring"""
        self.running = True
        print("📊 Starting Real-time Performance Monitor")
        print("=" * 60)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        monitor_thread.start()
        
        # Start display loop
        self._display_loop()
    
    def _monitor_loop(self):
        """Background monitoring loop"""
        while self.running:
            try:
                self._update_performance()
                time.sleep(10)  # Update every 10 seconds
            except Exception as e:
                print(f"Monitor error: {e}")
                time.sleep(30)
    
    def _display_loop(self):
        """Main display loop"""
        try:
            while self.running:
                self._clear_screen()
                self._display_dashboard()
                time.sleep(5)  # Refresh every 5 seconds
        except KeyboardInterrupt:
            print("\n👋 Monitor stopped by user")
            self.running = False
    
    def _clear_screen(self):
        """Clear terminal screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def _update_performance(self):
        """Update performance metrics from logs"""
        try:
            # Read trade logs
            if os.path.exists('logs/trades.log'):
                with open('logs/trades.log', 'r') as f:
                    lines = f.readlines()
                    
                # Parse recent trades
                self.trades = []
                for line in lines[-50:]:  # Last 50 trades
                    if 'TRADE_EXECUTED' in line or 'POSITION_EXITED' in line:
                        try:
                            trade_data = json.loads(line.split('] ')[-1])
                            self.trades.append(trade_data)
                        except:
                            continue
            
            # Calculate current capital from trades
            self._calculate_current_capital()
            
        except Exception as e:
            pass  # Silent fail for monitoring
    
    def _calculate_current_capital(self):
        """Calculate current capital from trade history"""
        capital = self.starting_capital
        
        for trade in self.trades:
            if 'realized_pnl' in trade:
                capital += trade['realized_pnl']
        
        self.current_capital = capital
    
    def _display_dashboard(self):
        """Display real-time dashboard"""
        now = datetime.now()
        returns = ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
        pnl = self.current_capital - self.starting_capital
        
        # Header
        print("💰 ₹100 PAPER TRADING - LIVE DASHBOARD")
        print("=" * 60)
        print(f"🕐 {now.strftime('%Y-%m-%d %H:%M:%S')} IST")
        print()
        
        # Capital Status
        print("💵 CAPITAL STATUS")
        print("-" * 30)
        print(f"Starting Capital: ₹{self.starting_capital:.2f}")
        print(f"Current Capital:  ₹{self.current_capital:.2f}")
        print(f"P&L:             ₹{pnl:.2f}")
        print(f"Returns:         {returns:.1f}%")
        print()
        
        # Target Progress
        target_progress = (pnl / self.daily_target) * 100
        progress_bar = self._create_progress_bar(target_progress, 50)
        
        print("🎯 DAILY TARGET PROGRESS")
        print("-" * 30)
        print(f"Target: ₹{self.daily_target:.2f} (20%)")
        print(f"Progress: {progress_bar} {target_progress:.1f}%")
        
        if pnl >= self.daily_target:
            print("🎉 DAILY TARGET ACHIEVED!")
        elif pnl > 0:
            remaining = self.daily_target - pnl
            print(f"Remaining: ₹{remaining:.2f}")
        else:
            print("📉 Currently in loss")
        print()
        
        # Trade Statistics
        self._display_trade_stats()
        
        # Recent Trades
        self._display_recent_trades()
        
        # Risk Status
        self._display_risk_status()
        
        # Market Status
        self._display_market_status()
        
        print("=" * 60)
        print("Press Ctrl+C to stop monitoring")
    
    def _create_progress_bar(self, percentage, width):
        """Create ASCII progress bar"""
        filled = int((percentage / 100) * width)
        bar = "█" * filled + "░" * (width - filled)
        return f"[{bar}]"
    
    def _display_trade_stats(self):
        """Display trading statistics"""
        if not self.trades:
            print("📊 TRADE STATISTICS")
            print("-" * 30)
            print("No trades executed yet")
            print()
            return
        
        # Calculate stats
        executed_trades = [t for t in self.trades if 'TRADE_EXECUTED' in str(t)]
        exited_trades = [t for t in self.trades if 'realized_pnl' in t]
        
        winning_trades = [t for t in exited_trades if t.get('realized_pnl', 0) > 0]
        losing_trades = [t for t in exited_trades if t.get('realized_pnl', 0) <= 0]
        
        total_trades = len(exited_trades)
        win_rate = (len(winning_trades) / total_trades * 100) if total_trades > 0 else 0
        
        avg_win = sum(t['realized_pnl'] for t in winning_trades) / len(winning_trades) if winning_trades else 0
        avg_loss = sum(t['realized_pnl'] for t in losing_trades) / len(losing_trades) if losing_trades else 0
        
        print("📊 TRADE STATISTICS")
        print("-" * 30)
        print(f"Total Trades:    {total_trades}")
        print(f"Winning Trades:  {len(winning_trades)}")
        print(f"Losing Trades:   {len(losing_trades)}")
        print(f"Win Rate:        {win_rate:.1f}%")
        print(f"Average Win:     ₹{avg_win:.2f}")
        print(f"Average Loss:    ₹{avg_loss:.2f}")
        
        if avg_loss != 0:
            profit_factor = abs(avg_win / avg_loss) if avg_loss < 0 else 0
            print(f"Profit Factor:   {profit_factor:.2f}")
        
        print()
    
    def _display_recent_trades(self):
        """Display recent trades"""
        print("📈 RECENT TRADES")
        print("-" * 30)
        
        recent_trades = self.trades[-5:]  # Last 5 trades
        
        if not recent_trades:
            print("No recent trades")
            print()
            return
        
        for trade in recent_trades:
            if 'symbol' in trade:
                symbol = trade.get('symbol', 'Unknown')
                side = trade.get('side', 'Unknown')
                
                if 'realized_pnl' in trade:
                    pnl = trade['realized_pnl']
                    status = "✅" if pnl > 0 else "❌"
                    print(f"{status} {symbol} {side} P&L: ₹{pnl:.2f}")
                else:
                    print(f"🔄 {symbol} {side} (Active)")
        
        print()
    
    def _display_risk_status(self):
        """Display risk management status"""
        print("🛡️ RISK STATUS")
        print("-" * 30)
        
        # Calculate risk metrics
        daily_loss_limit = 25.0  # ₹25 daily loss limit
        current_loss = min(0, self.current_capital - self.starting_capital)
        risk_used = abs(current_loss / daily_loss_limit * 100) if current_loss < 0 else 0
        
        print(f"Daily Loss Limit: ₹{daily_loss_limit:.2f}")
        print(f"Current Loss:     ₹{abs(current_loss):.2f}")
        print(f"Risk Used:        {risk_used:.1f}%")
        
        if risk_used > 80:
            print("⚠️  HIGH RISK - Consider stopping")
        elif risk_used > 50:
            print("⚠️  MODERATE RISK - Be cautious")
        else:
            print("✅ Risk under control")
        
        print()
    
    def _display_market_status(self):
        """Display market status"""
        print("🕐 MARKET STATUS")
        print("-" * 30)
        
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        
        # Market hours
        if "09:15" <= current_time <= "15:30":
            print("🟢 Market: OPEN")
            
            # High volatility periods
            if ("09:15" <= current_time <= "09:45" or 
                "11:00" <= current_time <= "11:30" or 
                "14:30" <= current_time <= "15:15"):
                print("⚡ High Volatility Period - ACTIVE TRADING")
            else:
                print("🔵 Normal Period - Waiting for signals")
        else:
            print("🔴 Market: CLOSED")
            
            # Next market open
            if current_time > "15:30":
                next_open = "Tomorrow 09:15"
            else:
                next_open = "Today 09:15"
            print(f"Next Open: {next_open}")
        
        print()

def main():
    """Main monitoring function"""
    print("📊 ₹100 Paper Trading Performance Monitor")
    print("Real-time dashboard for maximum returns tracking")
    print()
    
    # Check if bot is running
    if not os.path.exists('logs'):
        print("❌ No logs directory found")
        print("Start the trading bot first: python small_budget_bot.py")
        return 1
    
    try:
        monitor = PerformanceMonitor()
        monitor.start_monitoring()
        return 0
    except KeyboardInterrupt:
        print("\n👋 Monitor stopped")
        return 0
    except Exception as e:
        print(f"❌ Monitor error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
