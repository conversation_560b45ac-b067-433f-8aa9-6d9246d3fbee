# ULTIMATE TRADING BOT - PRODUCTION REQUIREMENTS
# Install with: pip install -r requirements_ultimate.txt

# Core Dependencies
numpy>=1.21.0
pandas>=1.3.0
asyncio-mqtt>=0.11.0
aiohttp>=3.8.0
requests>=2.28.0

# Data Sources
yfinance>=0.2.0
alpha-vantage>=2.3.0

# Angel One SmartAPI
smartapi-python>=1.3.0

# Authentication & Security
pyotp>=2.6.0
python-dotenv>=0.19.0
cryptography>=3.4.0

# Technical Analysis
ta-lib>=0.4.0
talib-binary>=0.4.0  # For Windows users

# Machine Learning (Optional)
scikit-learn>=1.0.0
tensorflow>=2.8.0  # Optional for advanced ML features

# Sentiment Analysis
textblob>=0.17.0
vaderSentiment>=3.3.0
newsapi-python>=0.2.0

# Database & Storage
sqlalchemy>=1.4.0
sqlite3  # Built-in with Python

# Logging & Monitoring
loguru>=0.6.0

# Notifications (Optional)
python-telegram-bot>=13.0.0
smtplib  # Built-in with Python

# Development & Testing
pytest>=7.0.0
pytest-asyncio>=0.20.0
black>=22.0.0
flake8>=4.0.0

# Performance
uvloop>=0.16.0  # For Linux/Mac
cython>=0.29.0

# Visualization (Optional)
matplotlib>=3.5.0
plotly>=5.0.0
dash>=2.0.0

# API Rate Limiting
ratelimit>=2.2.0
backoff>=2.0.0
