"""
Special Configuration for ₹100 Paper Trading - Maximum Returns
Optimized for small budget with aggressive but accurate strategies
"""
import os
from dataclasses import dataclass
from typing import List, Dict
import pytz

@dataclass
class SmallBudgetTradingConfig:
    """Optimized trading configuration for ₹100 budget"""
    
    # Account Settings - Aggressive for small capital
    INITIAL_CAPITAL: float = 100.0      # ₹100 starting capital
    MAX_RISK_PER_TRADE: float = 0.15    # 15% risk per trade (aggressive)
    MAX_DAILY_LOSS: float = 0.25        # 25% daily loss limit (₹25)
    MAX_POSITIONS: int = 1              # Only 1 position at a time for focus
    
    # Trading Hours (IST)
    MARKET_START_TIME: str = "09:15"
    MARKET_END_TIME: str = "15:20"      # Square off 10 minutes before close
    PRE_MARKET_START: str = "09:00"
    
    # Position Sizing - Optimized for ₹100
    MIN_POSITION_SIZE: float = 20.0     # Minimum ₹20 per position (20%)
    MAX_POSITION_SIZE: float = 80.0     # Maximum ₹80 per position (80%)
    LEVERAGE_MULTIPLIER: float = 5.0    # Higher leverage for small capital
    
    # Risk Management - Tight but aggressive
    DEFAULT_STOP_LOSS_PCT: float = 0.02   # 2% stop loss
    DEFAULT_TARGET_PCT: float = 0.06      # 6% target (1:3 risk reward)
    TRAILING_STOP_PCT: float = 0.015      # 1.5% trailing stop
    
    # Technical Analysis - More sensitive for quick moves
    RSI_PERIOD: int = 9                 # Faster RSI
    RSI_OVERSOLD: float = 25.0          # More aggressive oversold
    RSI_OVERBOUGHT: float = 75.0        # More aggressive overbought
    
    EMA_FAST: int = 5                   # Very fast EMA
    EMA_SLOW: int = 13                  # Faster slow EMA
    
    VOLUME_MULTIPLIER: float = 2.0      # Higher volume confirmation
    
    # Market Data
    CANDLE_INTERVAL: str = "ONE_MINUTE"
    HISTORICAL_DAYS: int = 7            # Less history for faster signals
    
    # Timezone
    TIMEZONE = pytz.timezone('Asia/Kolkata')

@dataclass
class HighAccuracyStrategyConfig:
    """High accuracy strategy configuration for maximum returns"""
    
    # Opening Range Breakout (ORB) - More aggressive
    ORB_PERIOD_MINUTES: int = 10        # First 10 minutes range
    ORB_MIN_RANGE_POINTS: float = 5.0   # Smaller minimum range
    ORB_MAX_RANGE_POINTS: float = 50.0  # Smaller maximum range
    
    # VWAP Strategy - More sensitive
    VWAP_DEVIATION_PCT: float = 0.003   # 0.3% deviation from VWAP
    VWAP_VOLUME_CONFIRMATION: bool = True
    
    # Momentum Strategy - Faster signals
    MOMENTUM_PERIOD: int = 10           # Shorter period
    MOMENTUM_THRESHOLD: float = 0.015   # 1.5% momentum threshold
    
    # Mean Reversion - Tighter bands
    MEAN_REVERSION_PERIOD: int = 15     # Shorter period
    MEAN_REVERSION_STD_DEV: float = 1.5 # 1.5 standard deviations
    
    # Strategy Weights - Focused on high accuracy strategies
    STRATEGY_WEIGHTS: Dict[str, float] = None
    
    # Signal Confidence Thresholds
    MIN_SIGNAL_CONFIDENCE: float = 0.75  # 75% minimum confidence
    MIN_CONFLUENCE_SIGNALS: int = 2       # At least 2 strategies agree
    
    def __post_init__(self):
        self.STRATEGY_WEIGHTS = {
            "orb": 0.35,           # Opening Range Breakout
            "vwap": 0.35,          # VWAP Strategy  
            "momentum": 0.25,      # Momentum Strategy
            "mean_reversion": 0.05 # Minimal mean reversion
        }

@dataclass
class HighFrequencyStockUniverse:
    """High frequency, high accuracy stock selection"""
    
    # Focus on most liquid and predictable stocks
    HIGH_ACCURACY_STOCKS: List[str] = None
    
    # Backup stocks if primary not available
    SECONDARY_STOCKS: List[str] = None
    
    def __post_init__(self):
        # Most liquid and trending stocks for accuracy
        self.HIGH_ACCURACY_STOCKS = [
            "RELIANCE",    # Most liquid
            "TCS",         # Stable trends
            "HDFCBANK",    # Banking leader
            "ICICIBANK",   # High volume
            "INFOSYS"      # Tech stability
        ]
        
        self.SECONDARY_STOCKS = [
            "ITC",
            "SBIN", 
            "BHARTIARTL",
            "LT",
            "WIPRO"
        ]

# Performance Optimization Settings
@dataclass
class PerformanceConfig:
    """Performance optimization for small budget trading"""
    
    # Trade Execution
    MAX_SLIPPAGE_PCT: float = 0.001     # 0.1% max slippage
    ORDER_TIMEOUT_SECONDS: int = 30     # Quick order execution
    
    # Signal Processing
    SIGNAL_REFRESH_SECONDS: int = 30    # Check signals every 30 seconds
    PRICE_UPDATE_SECONDS: int = 15      # Update prices every 15 seconds
    
    # Risk Monitoring
    RISK_CHECK_SECONDS: int = 10        # Check risk every 10 seconds
    POSITION_MONITOR_SECONDS: int = 5   # Monitor positions every 5 seconds
    
    # Performance Targets
    DAILY_TARGET_PCT: float = 0.20      # 20% daily target (₹20 profit)
    WEEKLY_TARGET_PCT: float = 1.00     # 100% weekly target (₹100 profit)
    MONTHLY_TARGET_PCT: float = 5.00    # 500% monthly target (₹500 profit)
    
    # Stop Trading Conditions
    PROFIT_LOCK_PCT: float = 0.15       # Lock profit at 15% (₹15)
    MAX_CONSECUTIVE_LOSSES: int = 3     # Stop after 3 consecutive losses

# Global Configuration Instances for Small Budget
small_budget_config = SmallBudgetTradingConfig()
high_accuracy_strategy = HighAccuracyStrategyConfig()
high_freq_stocks = HighFrequencyStockUniverse()
performance_config = PerformanceConfig()

# Environment Variables for Small Budget
class SmallBudgetEnvConfig:
    """Environment configuration for small budget trading"""
    
    @staticmethod
    def get_trading_settings():
        """Get optimized trading settings"""
        return {
            'initial_capital': 100.0,
            'paper_trading': True,
            'max_positions': 1,
            'risk_per_trade': 0.15,
            'daily_target': 20.0,
            'stop_loss': 0.02,
            'target': 0.06
        }
    
    @staticmethod
    def get_strategy_settings():
        """Get strategy settings for high accuracy"""
        return {
            'min_confidence': 0.75,
            'min_confluence': 2,
            'signal_refresh': 30,
            'risk_check': 10
        }

# Market Timing for Maximum Opportunities
def get_high_volatility_periods():
    """Get time periods with highest volatility for maximum returns"""
    return [
        ("09:15", "09:45"),  # Opening volatility
        ("11:00", "11:30"),  # Mid-morning moves
        ("14:30", "15:15"),  # Closing volatility
    ]

def is_high_volatility_time():
    """Check if current time is in high volatility period"""
    from datetime import datetime
    now = datetime.now(small_budget_config.TIMEZONE)
    current_time = now.strftime("%H:%M")
    
    for start_time, end_time in get_high_volatility_periods():
        if start_time <= current_time <= end_time:
            return True
    return False

# Performance Tracking
class PerformanceTracker:
    """Track performance for ₹100 budget trading"""
    
    def __init__(self):
        self.starting_capital = 100.0
        self.current_capital = 100.0
        self.daily_trades = []
        self.total_trades = 0
        self.winning_trades = 0
        
    def calculate_returns(self):
        """Calculate current returns"""
        return ((self.current_capital - self.starting_capital) / self.starting_capital) * 100
    
    def calculate_win_rate(self):
        """Calculate win rate"""
        if self.total_trades == 0:
            return 0
        return (self.winning_trades / self.total_trades) * 100
    
    def should_stop_trading(self):
        """Check if should stop trading for the day"""
        returns = self.calculate_returns()
        
        # Stop if daily target reached
        if returns >= performance_config.DAILY_TARGET_PCT:
            return True, f"Daily target reached: {returns:.1f}%"
        
        # Stop if daily loss limit reached
        if returns <= -performance_config.MAX_DAILY_LOSS * 100:
            return True, f"Daily loss limit reached: {returns:.1f}%"
        
        # Stop if profit lock triggered
        if returns >= performance_config.PROFIT_LOCK_PCT:
            return True, f"Profit locked at: {returns:.1f}%"
        
        return False, ""

# Export for main bot
def get_small_budget_config():
    """Get complete configuration for ₹100 trading"""
    return {
        'trading': small_budget_config,
        'strategy': high_accuracy_strategy,
        'stocks': high_freq_stocks,
        'performance': performance_config,
        'env': SmallBudgetEnvConfig()
    }
