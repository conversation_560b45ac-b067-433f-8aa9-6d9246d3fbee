{% extends "base.html" %}

{% block title %}Live Trading Dashboard - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-line"></i> Live Trading Dashboard
            <span class="badge bg-success ms-2">Real-time</span>
            <span class="real-time-indicator"></span>
        </h1>
    </div>
</div>

<!-- Market Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="total-symbols">0</div>
            <div class="metric-label">Active Symbols</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-success">
            <div class="metric-value" id="market-status">LOADING</div>
            <div class="metric-label">Market Status</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-info">
            <div class="metric-value" id="bot-decisions">0</div>
            <div class="metric-label">Bot Decisions</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-warning">
            <div class="metric-value" id="data-updates">0</div>
            <div class="metric-label">Data Updates</div>
        </div>
    </div>
</div>

<!-- Main Chart Section -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-chart-candlestick"></i> Live Price Chart</span>
                <div class="btn-group btn-group-sm" role="group">
                    <select id="symbolSelect" class="form-select form-select-sm me-2">
                        <option value="">Select Symbol...</option>
                        <option value="RELIANCE.NS">RELIANCE (NSE)</option>
                        <option value="TCS.NS">TCS (NSE)</option>
                        <option value="HDFCBANK.NS">HDFC BANK (NSE)</option>
                        <option value="AAPL">APPLE (NASDAQ)</option>
                        <option value="GOOGL">GOOGLE (NASDAQ)</option>
                        <option value="BTC-USD">BITCOIN (USD)</option>
                        <option value="ETH-USD">ETHEREUM (USD)</option>
                    </select>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleChart('candlestick')">Candlestick</button>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleChart('line')">Line</button>
                </div>
            </div>
            <div class="card-body">
                <div id="tradingview-chart" style="height: 400px; position: relative;">
                    <div class="text-center mt-5">
                        <i class="fas fa-chart-line fa-3x text-muted"></i>
                        <p class="text-muted mt-3">Select a symbol to view live chart</p>
                    </div>
                </div>
                
                <!-- Chart Controls -->
                <div class="mt-3 d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-secondary me-2">Current Price: <span id="current-price">--</span></span>
                        <span class="badge bg-info me-2">Change: <span id="price-change">--</span></span>
                        <span class="badge bg-warning">Volume: <span id="current-volume">--</span></span>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-success" onclick="addTechnicalIndicator('RSI')">
                            <i class="fas fa-plus"></i> RSI
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="addTechnicalIndicator('MA')">
                            <i class="fas fa-plus"></i> Moving Average
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bot Decisions Panel -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-robot"></i> Bot Decisions
                <span class="badge bg-primary ms-2" id="decision-count">0</span>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <div id="bot-decisions-list">
                    <div class="text-center text-muted">
                        <i class="fas fa-robot fa-2x"></i>
                        <p class="mt-2">Waiting for bot decisions...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Market Data Grid -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span><i class="fas fa-table"></i> Live Market Data</span>
                <div>
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshMarketData()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="exportMarketData()">
                        <i class="fas fa-download"></i> Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="marketDataTable">
                        <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Price</th>
                                <th>Change</th>
                                <th>Change %</th>
                                <th>Volume</th>
                                <th>High</th>
                                <th>Low</th>
                                <th>Bot Signal</th>
                                <th>Last Update</th>
                            </tr>
                        </thead>
                        <tbody id="marketDataBody">
                            <tr>
                                <td colspan="9" class="text-center text-muted">
                                    <i class="fas fa-spinner fa-spin"></i> Loading market data...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bot Control Panel -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-play-circle"></i> Bot Controls
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Current Status</h6>
                        <p class="mb-1">
                            <strong>Status:</strong> 
                            <span id="bot-status" class="{% if status.is_running %}status-running{% else %}status-stopped{% endif %}">
                                {% if status.is_running %}RUNNING{% else %}STOPPED{% endif %}
                            </span>
                        </p>
                        <p class="mb-1">
                            <strong>Market:</strong> 
                            <span class="text-primary" id="current-market">{{ status.current_market }}</span>
                        </p>
                        <p class="mb-1">
                            <strong>Mode:</strong> 
                            <span class="badge bg-{% if status.trading_mode == 'paper' %}success{% else %}warning{% endif %}" id="trading-mode">
                                {{ status.trading_mode.upper() }}
                            </span>
                        </p>
                    </div>
                    
                    <div class="col-md-6">
                        {% if not status.is_running %}
                        <form method="POST" action="{{ url_for('start_bot') }}">
                            <div class="mb-2">
                                <select name="market" class="form-select form-select-sm">
                                    <option value="indian">Indian Market</option>
                                    <option value="us">US Market</option>
                                    <option value="crypto">Cryptocurrency</option>
                                </select>
                            </div>
                            <div class="mb-2">
                                <select name="mode" class="form-select form-select-sm">
                                    <option value="paper">Paper Trading</option>
                                    <option value="live">Live Trading</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-start btn-custom">
                                <i class="fas fa-play"></i> Start Bot
                            </button>
                        </form>
                        {% else %}
                        <form method="POST" action="{{ url_for('stop_bot') }}">
                            <button type="submit" class="btn btn-stop btn-custom">
                                <i class="fas fa-stop"></i> Stop Bot
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> Performance Summary
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <h5 class="text-primary" id="total-trades">{{ status.performance.total_trades }}</h5>
                        <small class="text-muted">Total Trades</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-success" id="win-rate">{{ "%.1f"|format(status.performance.win_rate) }}%</h5>
                        <small class="text-muted">Win Rate</small>
                    </div>
                    <div class="col-4">
                        <h5 class="text-info" id="total-pnl">{{ "%.2f"|format(status.performance.total_pnl) }}</h5>
                        <small class="text-muted">Total P&L</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- TradingView Lightweight Charts -->
<script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>

<script>
    // Global variables
    let chart = null;
    let candlestickSeries = null;
    let lineSeries = null;
    let currentSymbol = null;
    let chartType = 'candlestick';
    let updateCount = 0;
    let decisionCount = 0;
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeChart();
        loadMarketData();
        
        // Set up symbol selector
        document.getElementById('symbolSelect').addEventListener('change', function() {
            const symbol = this.value;
            if (symbol) {
                loadSymbolChart(symbol);
            }
        });
    });
    
    // Initialize TradingView chart
    function initializeChart() {
        const chartContainer = document.getElementById('tradingview-chart');
        
        chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 400,
            layout: {
                backgroundColor: '#ffffff',
                textColor: '#333',
            },
            grid: {
                vertLines: {
                    color: '#f0f0f0',
                },
                horzLines: {
                    color: '#f0f0f0',
                },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });
        
        // Create candlestick series
        candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });
        
        // Create line series (hidden initially)
        lineSeries = chart.addLineSeries({
            color: '#2196F3',
            lineWidth: 2,
        });
        lineSeries.applyOptions({ visible: false });
        
        // Handle resize
        window.addEventListener('resize', () => {
            chart.applyOptions({ width: chartContainer.clientWidth });
        });
    }
    
    // Load chart data for symbol
    function loadSymbolChart(symbol) {
        currentSymbol = symbol;
        
        fetch(`/api/chart_data/${symbol}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Chart data error:', data.error);
                    return;
                }
                
                updateChart(data);
                updatePriceInfo(data);
            })
            .catch(error => {
                console.error('Error loading chart data:', error);
            });
    }
    
    // Update chart with new data
    function updateChart(data) {
        if (!data.ohlc_data || data.ohlc_data.length === 0) {
            return;
        }
        
        // Convert data for TradingView format
        const chartData = data.ohlc_data.map(item => ({
            time: item.time,
            open: item.open,
            high: item.high,
            low: item.low,
            close: item.close
        }));
        
        const lineData = data.ohlc_data.map(item => ({
            time: item.time,
            value: item.close
        }));
        
        // Update series
        candlestickSeries.setData(chartData);
        lineSeries.setData(lineData);
        
        // Show appropriate series
        if (chartType === 'candlestick') {
            candlestickSeries.applyOptions({ visible: true });
            lineSeries.applyOptions({ visible: false });
        } else {
            candlestickSeries.applyOptions({ visible: false });
            lineSeries.applyOptions({ visible: true });
        }
    }
    
    // Update price information
    function updatePriceInfo(data) {
        document.getElementById('current-price').textContent = data.current_price.toFixed(2);
        document.getElementById('price-change').textContent = data.change_pct.toFixed(2) + '%';
        
        // Update change color
        const changeElement = document.getElementById('price-change').parentElement;
        if (data.change_pct > 0) {
            changeElement.className = 'badge bg-success me-2';
        } else if (data.change_pct < 0) {
            changeElement.className = 'badge bg-danger me-2';
        } else {
            changeElement.className = 'badge bg-secondary me-2';
        }
    }
    
    // Toggle chart type
    function toggleChart(type) {
        chartType = type;
        if (currentSymbol) {
            loadSymbolChart(currentSymbol);
        }
        
        // Update button states
        document.querySelectorAll('.btn-group button').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }
    
    // Load market data table
    function loadMarketData() {
        fetch('/api/market_data')
            .then(response => response.json())
            .then(data => {
                updateMarketDataTable(data);
                updateCount++;
                document.getElementById('data-updates').textContent = updateCount;
                document.getElementById('total-symbols').textContent = Object.keys(data).length;
            })
            .catch(error => {
                console.error('Error loading market data:', error);
            });
    }
    
    // Update market data table
    function updateMarketDataTable(data) {
        const tbody = document.getElementById('marketDataBody');
        tbody.innerHTML = '';
        
        if (Object.keys(data).length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">No market data available</td></tr>';
            return;
        }
        
        Object.entries(data).forEach(([symbol, info]) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${symbol}</strong></td>
                <td>$${info.price.toFixed(2)}</td>
                <td class="text-${info.change >= 0 ? 'success' : 'danger'}">${info.change >= 0 ? '+' : ''}${info.change.toFixed(2)}</td>
                <td class="text-${info.change_pct >= 0 ? 'success' : 'danger'}">${info.change_pct >= 0 ? '+' : ''}${info.change_pct.toFixed(2)}%</td>
                <td>${info.volume.toLocaleString()}</td>
                <td>${info.day_high ? '$' + info.day_high.toFixed(2) : '--'}</td>
                <td>${info.day_low ? '$' + info.day_low.toFixed(2) : '--'}</td>
                <td><span class="badge bg-secondary">ANALYZING</span></td>
                <td><small>${new Date(info.timestamp).toLocaleTimeString()}</small></td>
            `;
            tbody.appendChild(row);
        });
    }
    
    // WebSocket event handlers
    socket.on('market_data_update', function(data) {
        // Update chart if it's the current symbol
        if (currentSymbol === data.symbol) {
            updateChart(data.data);
            updatePriceInfo(data.data);
        }
        
        // Update market data table
        loadMarketData();
    });
    
    socket.on('bot_decision', function(decision) {
        addBotDecision(decision);
        decisionCount++;
        document.getElementById('bot-decisions').textContent = decisionCount;
        document.getElementById('decision-count').textContent = decisionCount;
        
        // Add decision marker to chart if it's the current symbol
        if (currentSymbol === decision.symbol) {
            addDecisionMarker(decision);
        }
    });
    
    // Add bot decision to the list
    function addBotDecision(decision) {
        const container = document.getElementById('bot-decisions-list');
        
        // Clear placeholder if it exists
        if (container.querySelector('.text-center')) {
            container.innerHTML = '';
        }
        
        const decisionElement = document.createElement('div');
        decisionElement.className = `alert alert-${getActionColor(decision.action)} alert-sm mb-2`;
        decisionElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <strong>${decision.symbol}</strong>
                    <span class="badge bg-${getActionColor(decision.action)} ms-1">${decision.action}</span>
                    <br>
                    <small>Price: $${decision.entry_price.toFixed(2)} | Confidence: ${(decision.confidence * 100).toFixed(1)}%</small>
                    <br>
                    <small class="text-muted">${decision.reasoning.join(', ')}</small>
                </div>
                <small class="text-muted">${new Date(decision.timestamp).toLocaleTimeString()}</small>
            </div>
        `;
        
        // Add to top of list
        container.insertBefore(decisionElement, container.firstChild);
        
        // Keep only last 10 decisions
        while (container.children.length > 10) {
            container.removeChild(container.lastChild);
        }
    }
    
    // Get color for action
    function getActionColor(action) {
        switch(action) {
            case 'STRONG_BUY':
            case 'BUY':
                return 'success';
            case 'STRONG_SELL':
            case 'SELL':
                return 'danger';
            case 'HOLD':
                return 'warning';
            default:
                return 'secondary';
        }
    }
    
    // Add decision marker to chart
    function addDecisionMarker(decision) {
        // This would add markers to the chart
        // Implementation depends on TradingView Lightweight Charts API
        console.log('Adding decision marker:', decision);
    }
    
    // Utility functions
    function refreshMarketData() {
        loadMarketData();
    }
    
    function exportMarketData() {
        // Export functionality
        alert('Export functionality will be implemented');
    }
    
    function addTechnicalIndicator(indicator) {
        // Add technical indicators
        alert(`${indicator} indicator will be implemented`);
    }
    
    // Auto-refresh data every 5 seconds
    setInterval(function() {
        if (currentSymbol) {
            loadSymbolChart(currentSymbol);
        }
        loadMarketData();
    }, 5000);
</script>
{% endblock %}
