#!/usr/bin/env python3
"""
Setup Testing Credentials for Real Data Paper Trading
"""
import os
import sys

def setup_testing_credentials():
    """Setup credentials for testing mode"""
    print("🧪 Setting up Real Data Paper Trading")
    print("=" * 50)
    print("📊 Mode: Paper Trading with REAL Angel One SmartAPI data")
    print("💰 Capital: ₹100 (simulated)")
    print("🛡️ Risk: No real money will be used")
    print()
    
    print("📋 Your Angel One SmartAPI credentials:")
    print("✅ API Key: DxfC3bez (from your screenshot)")
    print("✅ Secret Key: 5262bcc2-f4fe-4025-8e48-761d634e782 (from your screenshot)")
    print()
    
    print("📝 Please provide the remaining credentials:")
    
    # Get Client ID
    client_id = input("Client ID (your Angel One trading account number): ").strip()
    if not client_id:
        print("❌ Client ID is required!")
        return False
    
    # Get Password
    password = input("Password (your Angel One login password): ").strip()
    if not password:
        print("❌ Password is required!")
        return False
    
    # Get TOTP Secret
    print()
    print("🔐 For TOTP Secret (2FA):")
    print("   1. Open Angel One app/website")
    print("   2. Go to Settings → Security → Two Factor Authentication")
    print("   3. If not enabled, enable 2FA and scan QR code")
    print("   4. Copy the secret key (not the 6-digit code)")
    print("   Example: JBSWY3DPEHPK3PXP")
    print()
    
    totp_secret = input("TOTP Secret: ").strip()
    if not totp_secret:
        print("❌ TOTP Secret is required!")
        return False
    
    # Update .env file
    env_content = f"""# Angel One SmartAPI Credentials (from your screenshot)
ANGEL_API_KEY=DxfC3bez
ANGEL_SECRET_KEY=5262bcc2-f4fe-4025-8e48-761d634e782
ANGEL_CLIENT_ID={client_id}
ANGEL_PASSWORD={password}
ANGEL_TOTP_SECRET={totp_secret}

# TESTING MODE - Paper Trading with REAL SmartAPI Data
PAPER_TRADING=True
TESTING_MODE=True
USE_REAL_DATA=True
SIMULATE_ORDERS=True

# ₹100 Paper Trading Configuration - Maximum Returns
INITIAL_CAPITAL=100
MAX_DAILY_LOSS=25
MAX_POSITIONS=1
RISK_PER_TRADE=0.15

# High Accuracy Settings
MIN_SIGNAL_CONFIDENCE=0.75
MIN_CONFLUENCE_SIGNALS=2
SIGNAL_REFRESH_SECONDS=30

# Performance Targets
DAILY_TARGET_PCT=20
WEEKLY_TARGET_PCT=100
MONTHLY_TARGET_PCT=500

# Logging
LOG_LEVEL=INFO
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("\n✅ Testing credentials configured!")
    return True

def test_credentials():
    """Test the configured credentials"""
    print("\n🧪 Testing Angel One SmartAPI connection...")
    
    try:
        import pyotp
        from dotenv import load_dotenv
        load_dotenv()
        
        # Test TOTP generation
        totp_secret = os.getenv('ANGEL_TOTP_SECRET')
        if totp_secret:
            totp = pyotp.TOTP(totp_secret)
            current_totp = totp.now()
            print(f"✅ TOTP generated: {current_totp}")
        else:
            print("❌ TOTP secret not found")
            return False
        
        print("✅ Credentials format validated")
        print("✅ Ready for real data paper trading!")
        return True
        
    except ImportError:
        print("❌ Missing dependencies. Run: pip install pyotp python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def show_next_steps():
    """Show next steps"""
    print("\n🚀 NEXT STEPS")
    print("=" * 30)
    print("1. 🧪 Start real data paper trading:")
    print("   python real_data_paper_trading.py")
    print()
    print("2. 📊 Monitor in another terminal:")
    print("   tail -f logs/real_data_paper_trading.log")
    print()
    print("3. 📈 Check trade logs:")
    print("   tail -f logs/real_data_trades.log")
    print()
    print("💡 What you'll see:")
    print("   ✅ Real Angel One API connection")
    print("   ✅ Live market prices (RELIANCE, TCS, etc.)")
    print("   ✅ Real technical analysis on live data")
    print("   ✅ Paper trades with actual market prices")
    print("   ✅ No real money used (100% safe)")

def main():
    """Main setup function"""
    print("🧪 Real Data Paper Trading Setup")
    print("=" * 60)
    print("Configure paper trading with REAL Angel One SmartAPI data")
    print()
    
    try:
        # Setup credentials
        if not setup_testing_credentials():
            return 1
        
        # Test credentials
        if not test_credentials():
            print("❌ Credential test failed!")
            return 1
        
        # Show next steps
        show_next_steps()
        
        print("\n🎉 SETUP COMPLETE!")
        print("=" * 50)
        print("✅ Real data paper trading configured")
        print("✅ Angel One SmartAPI credentials ready")
        print("✅ Testing mode enabled")
        print("✅ No real money will be used")
        print()
        
        # Ask if user wants to start immediately
        start_now = input("Start real data paper trading now? (y/N): ").strip().lower()
        if start_now == 'y':
            print("\n🚀 Starting real data paper trading...")
            os.system("python real_data_paper_trading.py")
        else:
            print("\n✅ Ready to start! Run: python real_data_paper_trading.py")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Setup error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
