#!/usr/bin/env python3
"""
ULTIMATE TRADING BOT - COMPLETE WEBSITE INTERFACE
Professional web interface with all pages and features (interface only)
"""
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from flask_socketio import SocketIO, emit
from authlib.integrations.flask_client import OAuth
import json
import os
from datetime import datetime, timedelta
import random
import sqlite3
import hashlib
import requests
from functools import wraps
import yfinance as yf
import pandas as pd
import numpy as np
import threading
import time
import asyncio
import websocket
from concurrent.futures import ThreadPoolExecutor
import logging
from dotenv import load_dotenv

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.getenv('SECRET_KEY', 'your-secret-key-change-in-production')
socketio = SocketIO(app, cors_allowed_origins="*")

# Initialize OAuth
oauth = OAuth(app)

# Google OAuth configuration
google = oauth.register(
    name='google',
    client_id=os.getenv('GOOGLE_CLIENT_ID', 'your-google-client-id'),
    client_secret=os.getenv('GOOGLE_CLIENT_SECRET', 'your-google-client-secret'),
    server_metadata_url='https://accounts.google.com/.well-known/openid_configuration',
    client_kwargs={
        'scope': 'openid email profile'
    }
)

# ============================================================================
# DATABASE INITIALIZATION
# ============================================================================

def init_database():
    """Initialize SQLite database for user management"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()

    # Users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            google_id TEXT UNIQUE,
            angel_api_key TEXT,
            angel_client_id TEXT,
            angel_password TEXT,
            angel_totp_secret TEXT,
            dark_mode BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        )
    ''')

    # User trading settings table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            capital REAL DEFAULT 100.0,
            max_positions INTEGER DEFAULT 5,
            position_size_percent REAL DEFAULT 15.0,
            confidence_threshold REAL DEFAULT 42.0,
            stop_loss_percent REAL DEFAULT 2.0,
            take_profit_percent REAL DEFAULT 6.0,
            daily_loss_limit REAL DEFAULT 50.0,
            auto_stop_loss BOOLEAN DEFAULT TRUE,
            trailing_stop BOOLEAN DEFAULT FALSE,
            preferred_market TEXT DEFAULT 'indian',
            trading_mode TEXT DEFAULT 'paper',
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    # User trades table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_trades (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            symbol TEXT NOT NULL,
            market TEXT NOT NULL,
            action TEXT NOT NULL,
            entry_price REAL NOT NULL,
            exit_price REAL,
            quantity INTEGER NOT NULL,
            confidence REAL NOT NULL,
            pnl REAL DEFAULT 0.0,
            status TEXT DEFAULT 'OPEN',
            entry_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            exit_time TIMESTAMP,
            mode TEXT DEFAULT 'paper',
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')

    conn.commit()
    conn.close()

# ============================================================================
# AUTHENTICATION HELPERS
# ============================================================================

def login_required(f):
    """Decorator to require login for protected routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_user_by_email(email):
    """Get user by email from database"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
    user = cursor.fetchone()
    conn.close()
    return user

def create_user(email, name, google_id=None):
    """Create new user in database"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()

    try:
        cursor.execute('''
            INSERT INTO users (email, name, google_id, last_login)
            VALUES (?, ?, ?, ?)
        ''', (email, name, google_id, datetime.now()))

        user_id = cursor.lastrowid

        # Create default settings for new user
        cursor.execute('''
            INSERT INTO user_settings (user_id)
            VALUES (?)
        ''', (user_id,))

        conn.commit()
        return user_id
    except sqlite3.IntegrityError:
        return None
    finally:
        conn.close()

def update_user_login(email):
    """Update user's last login time"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE users SET last_login = ? WHERE email = ?
    ''', (datetime.now(), email))
    conn.commit()
    conn.close()

def get_user_settings(user_id):
    """Get user's trading settings"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM user_settings WHERE user_id = ?', (user_id,))
    settings = cursor.fetchone()
    conn.close()
    return settings

def update_user_settings(user_id, settings_dict):
    """Update user's trading settings"""
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()

    # Build dynamic update query
    set_clause = ', '.join([f"{key} = ?" for key in settings_dict.keys()])
    values = list(settings_dict.values()) + [user_id]

    cursor.execute(f'''
        UPDATE user_settings SET {set_clause} WHERE user_id = ?
    ''', values)

    conn.commit()
    conn.close()

# ============================================================================
# ANGEL ONE API INTEGRATION
# ============================================================================

class AngelOneAPI:
    """Angel One SmartAPI integration"""

    def __init__(self, api_key, client_id, password, totp_secret=None):
        self.api_key = api_key
        self.client_id = client_id
        self.password = password
        self.totp_secret = totp_secret
        self.base_url = "https://apiconnect.angelbroking.com"
        self.access_token = None
        self.refresh_token = None

    def generate_session(self):
        """Generate session with Angel One API"""
        try:
            url = f"{self.base_url}/rest/auth/angelbroking/user/v1/loginByPassword"

            payload = {
                "clientcode": self.client_id,
                "password": self.password
            }

            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-UserType': 'USER',
                'X-SourceID': 'WEB',
                'X-ClientLocalIP': '127.0.0.1',
                'X-ClientPublicIP': '127.0.0.1',
                'X-MACAddress': '00:00:00:00:00:00',
                'X-PrivateKey': self.api_key
            }

            response = requests.post(url, json=payload, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('status'):
                    self.access_token = data['data']['jwtToken']
                    self.refresh_token = data['data']['refreshToken']
                    return True

            return False

        except Exception as e:
            print(f"Angel One API Error: {e}")
            return False

    def get_profile(self):
        """Get user profile from Angel One"""
        if not self.access_token:
            return None

        try:
            url = f"{self.base_url}/rest/secure/angelbroking/user/v1/getProfile"

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-UserType': 'USER',
                'X-SourceID': 'WEB',
                'X-ClientLocalIP': '127.0.0.1',
                'X-ClientPublicIP': '127.0.0.1',
                'X-MACAddress': '00:00:00:00:00:00',
                'X-PrivateKey': self.api_key
            }

            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('status'):
                    return data['data']

            return None

        except Exception as e:
            print(f"Angel One Profile Error: {e}")
            return None

    def place_order(self, symbol, quantity, price, order_type='MARKET', transaction_type='BUY'):
        """Place order through Angel One API"""
        if not self.access_token:
            return None

        try:
            url = f"{self.base_url}/rest/secure/angelbroking/order/v1/placeOrder"

            payload = {
                "variety": "NORMAL",
                "tradingsymbol": symbol,
                "symboltoken": "",  # You'll need to get this from symbol master
                "transactiontype": transaction_type,
                "exchange": "NSE",
                "ordertype": order_type,
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": str(price) if order_type == 'LIMIT' else "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(quantity)
            }

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'X-UserType': 'USER',
                'X-SourceID': 'WEB',
                'X-ClientLocalIP': '127.0.0.1',
                'X-ClientPublicIP': '127.0.0.1',
                'X-MACAddress': '00:00:00:00:00:00',
                'X-PrivateKey': self.api_key
            }

            response = requests.post(url, json=payload, headers=headers)

            if response.status_code == 200:
                data = response.json()
                if data.get('status'):
                    return data['data']

            return None

        except Exception as e:
            print(f"Angel One Order Error: {e}")
            return None

# ============================================================================
# REAL-TIME MARKET DATA SYSTEM
# ============================================================================

class RealTimeMarketData:
    """Real-time market data provider with multiple sources"""

    def __init__(self):
        self.data_cache = {}
        self.subscribers = {}
        self.update_threads = {}
        self.is_running = False
        self.executor = ThreadPoolExecutor(max_workers=10)

        # Market configurations
        self.markets = {
            'indian': {
                'symbols': ['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'ICICIBANK.NS', 'INFOSYS.NS'],
                'update_interval': 5,  # seconds
                'trading_hours': {'start': 9, 'end': 15}
            },
            'us': {
                'symbols': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'],
                'update_interval': 5,
                'trading_hours': {'start': 9, 'end': 16}  # EST
            },
            'crypto': {
                'symbols': ['BTC-USD', 'ETH-USD', 'BNB-USD', 'ADA-USD', 'SOL-USD'],
                'update_interval': 3,
                'trading_hours': {'start': 0, 'end': 24}  # 24/7
            },
            'european': {
                'symbols': ['ASML.AS', 'SAP.DE', 'NESN.SW', 'MC.PA'],
                'update_interval': 5,
                'trading_hours': {'start': 8, 'end': 16}
            }
        }

        logger.info("Real-time market data system initialized")

    def start_data_feeds(self):
        """Start real-time data feeds for all markets"""
        if self.is_running:
            return

        self.is_running = True

        for market_name, config in self.markets.items():
            thread = threading.Thread(
                target=self._market_data_loop,
                args=(market_name, config),
                daemon=True
            )
            thread.start()
            self.update_threads[market_name] = thread

        logger.info("Started real-time data feeds for all markets")

    def stop_data_feeds(self):
        """Stop all data feeds"""
        self.is_running = False
        logger.info("Stopped real-time data feeds")

    def _market_data_loop(self, market_name, config):
        """Main loop for fetching market data"""
        while self.is_running:
            try:
                # Check if market is open
                if self._is_market_open(config['trading_hours']):
                    # Fetch data for all symbols in this market
                    for symbol in config['symbols']:
                        data = self._fetch_symbol_data(symbol)
                        if data:
                            self.data_cache[symbol] = data
                            # Emit to WebSocket subscribers
                            self._emit_data_update(symbol, data)

                # Wait before next update
                time.sleep(config['update_interval'])

            except Exception as e:
                logger.error(f"Error in market data loop for {market_name}: {e}")
                time.sleep(10)  # Wait longer on error

    def _is_market_open(self, trading_hours):
        """Check if market is currently open"""
        current_hour = datetime.now().hour

        # Crypto markets are always open
        if trading_hours['start'] == 0 and trading_hours['end'] == 24:
            return True

        return trading_hours['start'] <= current_hour <= trading_hours['end']

    def _fetch_symbol_data(self, symbol):
        """Fetch real-time data for a symbol"""
        try:
            ticker = yf.Ticker(symbol)

            # Get current price and basic info
            info = ticker.info
            hist = ticker.history(period="1d", interval="1m")

            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                prev_close = info.get('previousClose', current_price)
                change = current_price - prev_close
                change_pct = (change / prev_close) * 100 if prev_close > 0 else 0
                volume = hist['Volume'].iloc[-1]

                # Get OHLC data for charts
                ohlc_data = []
                for i in range(min(100, len(hist))):  # Last 100 data points
                    row = hist.iloc[-(i+1)]
                    ohlc_data.append({
                        'time': int(row.name.timestamp()),
                        'open': float(row['Open']),
                        'high': float(row['High']),
                        'low': float(row['Low']),
                        'close': float(row['Close']),
                        'volume': int(row['Volume'])
                    })

                return {
                    'symbol': symbol,
                    'price': float(current_price),
                    'change': float(change),
                    'change_pct': float(change_pct),
                    'volume': int(volume),
                    'prev_close': float(prev_close),
                    'ohlc_data': list(reversed(ohlc_data)),  # Chronological order
                    'timestamp': datetime.now().isoformat(),
                    'market_cap': info.get('marketCap'),
                    'pe_ratio': info.get('trailingPE'),
                    'day_high': info.get('dayHigh'),
                    'day_low': info.get('dayLow'),
                    'fifty_two_week_high': info.get('fiftyTwoWeekHigh'),
                    'fifty_two_week_low': info.get('fiftyTwoWeekLow')
                }

        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return None

    def _emit_data_update(self, symbol, data):
        """Emit data update to WebSocket subscribers"""
        try:
            socketio.emit('market_data_update', {
                'symbol': symbol,
                'data': data
            })
        except Exception as e:
            logger.error(f"Error emitting data for {symbol}: {e}")

    def get_symbol_data(self, symbol):
        """Get cached data for a symbol"""
        return self.data_cache.get(symbol)

    def get_all_data(self):
        """Get all cached market data"""
        return self.data_cache

# ============================================================================
# TRADING BOT DECISION SYSTEM
# ============================================================================

class TradingBotDecisionEngine:
    """Advanced trading bot with real decision making"""

    def __init__(self, market_data_provider):
        self.market_data = market_data_provider
        self.decisions = {}
        self.active_positions = {}
        self.decision_history = []
        self.is_running = False

        # Technical analysis parameters
        self.rsi_period = 14
        self.ma_short = 10
        self.ma_long = 20
        self.confidence_threshold = 0.42

        logger.info("Trading bot decision engine initialized")

    def start_decision_engine(self):
        """Start the decision making engine"""
        if self.is_running:
            return

        self.is_running = True
        thread = threading.Thread(target=self._decision_loop, daemon=True)
        thread.start()
        logger.info("Trading bot decision engine started")

    def stop_decision_engine(self):
        """Stop the decision engine"""
        self.is_running = False
        logger.info("Trading bot decision engine stopped")

    def _decision_loop(self):
        """Main decision making loop"""
        while self.is_running:
            try:
                # Analyze all symbols with available data
                for symbol, data in self.market_data.get_all_data().items():
                    decision = self._analyze_symbol(symbol, data)
                    if decision:
                        self.decisions[symbol] = decision
                        self.decision_history.append(decision)

                        # Emit decision to WebSocket
                        socketio.emit('bot_decision', decision)

                        logger.info(f"Bot decision: {symbol} {decision['action']} (confidence: {decision['confidence']:.1%})")

                # Wait before next analysis
                time.sleep(10)  # Analyze every 10 seconds

            except Exception as e:
                logger.error(f"Error in decision loop: {e}")
                time.sleep(30)

    def _analyze_symbol(self, symbol, data):
        """Analyze a symbol and make trading decision"""
        try:
            if not data or not data.get('ohlc_data') or len(data['ohlc_data']) < 20:
                return None

            # Convert to DataFrame for analysis
            df = pd.DataFrame(data['ohlc_data'])
            df['timestamp'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('timestamp', inplace=True)

            # Calculate technical indicators
            rsi = self._calculate_rsi(df['close'])
            ma_short = df['close'].rolling(window=self.ma_short).mean()
            ma_long = df['close'].rolling(window=self.ma_long).mean()

            current_price = data['price']
            current_rsi = rsi.iloc[-1] if not rsi.empty else 50
            current_ma_short = ma_short.iloc[-1] if not ma_short.empty else current_price
            current_ma_long = ma_long.iloc[-1] if not ma_long.empty else current_price

            # Decision logic
            signals = []
            reasoning = []

            # RSI signals
            if current_rsi < 30:
                signals.append(1)  # Oversold - buy signal
                reasoning.append(f"RSI oversold ({current_rsi:.1f})")
            elif current_rsi > 70:
                signals.append(-1)  # Overbought - sell signal
                reasoning.append(f"RSI overbought ({current_rsi:.1f})")

            # Moving average signals
            if current_ma_short > current_ma_long:
                signals.append(1)  # Bullish trend
                reasoning.append("Short MA above Long MA (bullish)")
            elif current_ma_short < current_ma_long:
                signals.append(-1)  # Bearish trend
                reasoning.append("Short MA below Long MA (bearish)")

            # Price momentum
            change_pct = data['change_pct']
            if abs(change_pct) > 2:
                signals.append(np.sign(change_pct))
                reasoning.append(f"Strong momentum ({change_pct:+.1f}%)")

            # Volume analysis
            if len(df) > 1:
                avg_volume = df['volume'].rolling(window=10).mean().iloc[-1]
                current_volume = data['volume']
                if current_volume > avg_volume * 1.5:
                    signals.append(1 if change_pct > 0 else -1)
                    reasoning.append("High volume confirmation")

            # Calculate overall signal and confidence
            if not signals:
                return None

            signal_strength = np.mean(signals)
            confidence = min(1.0, abs(signal_strength) * len(signals) / 4)

            # Only generate decision if confidence is above threshold
            if confidence < self.confidence_threshold:
                return None

            # Determine action
            if signal_strength > 0.5:
                action = "STRONG_BUY" if signal_strength > 0.8 else "BUY"
            elif signal_strength < -0.5:
                action = "STRONG_SELL" if signal_strength < -0.8 else "SELL"
            else:
                action = "HOLD"

            # Calculate stop loss and take profit
            if action in ["BUY", "STRONG_BUY"]:
                stop_loss = current_price * 0.98  # 2% stop loss
                take_profit = current_price * 1.06  # 6% take profit
            elif action in ["SELL", "STRONG_SELL"]:
                stop_loss = current_price * 1.02  # 2% stop loss
                take_profit = current_price * 0.94  # 6% take profit
            else:
                stop_loss = take_profit = current_price

            decision = {
                'symbol': symbol,
                'action': action,
                'confidence': confidence,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'take_profit': take_profit,
                'reasoning': reasoning,
                'technical_indicators': {
                    'rsi': current_rsi,
                    'ma_short': current_ma_short,
                    'ma_long': current_ma_long,
                    'signal_strength': signal_strength
                },
                'timestamp': datetime.now().isoformat(),
                'market_data': data
            }

            return decision

        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {e}")
            return None

    def _calculate_rsi(self, prices, period=14):
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series()

    def get_recent_decisions(self, limit=10):
        """Get recent trading decisions"""
        return self.decision_history[-limit:] if self.decision_history else []

# Initialize global instances
market_data_provider = RealTimeMarketData()
trading_bot = TradingBotDecisionEngine(market_data_provider)

# ============================================================================
# MOCK DATA FOR INTERFACE DEMONSTRATION
# ============================================================================

# Mock bot status
mock_bot_status = {
    'is_running': False,
    'current_market': 'indian',
    'trading_mode': 'paper',
    'capital': 100.0,
    'positions': 0,
    'performance': {
        'total_trades': 0,
        'winning_trades': 0,
        'total_pnl': 0.0,
        'win_rate': 0.0,
        'daily_pnl': 0.0
    },
    'last_update': None
}

# Mock markets configuration
mock_markets = {
    'indian': {
        'name': 'Indian Stock Market',
        'symbols': ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS', 'ITC', 'HINDUNILVR'],
        'currency': 'INR',
        'status': 'OPEN'
    },
    'us': {
        'name': 'US Stock Market',
        'symbols': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'NVDA', 'META'],
        'currency': 'USD',
        'status': 'CLOSED'
    },
    'european': {
        'name': 'European Stock Market',
        'symbols': ['ASML.AS', 'SAP.DE', 'NESN.SW', 'MC.PA', 'NOVO-B.CO'],
        'currency': 'EUR',
        'status': 'CLOSED'
    },
    'crypto': {
        'name': 'Cryptocurrency Market',
        'symbols': ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'MATIC'],
        'currency': 'USD',
        'status': '24/7 OPEN'
    },
    'forex': {
        'name': 'Forex Market',
        'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD'],
        'currency': 'USD',
        'status': '24/7 OPEN'
    }
}

# Mock positions
mock_positions = []

# Mock trades history
mock_trades = []

# Mock performance data
mock_performance_data = {
    'daily_pnl': [0, 5, 3, 8, 12, 15, 18],
    'win_rate_history': [0, 60, 65, 70, 68, 72, 75],
    'trades_per_day': [0, 2, 3, 4, 3, 5, 4]
}

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def generate_mock_position():
    """Generate a mock trading position"""
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'AAPL', 'GOOGL', 'BTC', 'ETH']
    actions = ['BUY', 'SELL', 'STRONG_BUY', 'STRONG_SELL']
    
    symbol = random.choice(symbols)
    action = random.choice(actions)
    entry_price = random.uniform(100, 3000)
    current_price = entry_price * random.uniform(0.95, 1.05)
    
    return {
        'symbol': symbol,
        'action': action,
        'entry_price': entry_price,
        'current_price': current_price,
        'confidence': random.uniform(0.4, 0.9),
        'position_size': random.uniform(10, 50),
        'currency': 'INR' if symbol in ['RELIANCE', 'TCS', 'HDFCBANK'] else 'USD',
        'entry_time': datetime.now().strftime('%H:%M:%S'),
        'status': 'OPEN'
    }

def generate_mock_trade():
    """Generate a mock trade record"""
    symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'AAPL', 'GOOGL', 'BTC', 'ETH']
    actions = ['BUY', 'SELL', 'STRONG_BUY', 'STRONG_SELL']
    markets = ['indian', 'us', 'crypto']
    
    return {
        'timestamp': (datetime.now() - timedelta(minutes=random.randint(1, 1440))).isoformat(),
        'symbol': random.choice(symbols),
        'market': random.choice(markets),
        'action': random.choice(actions),
        'entry_price': random.uniform(100, 3000),
        'confidence': random.uniform(0.4, 0.9),
        'position_size': random.uniform(10, 50),
        'currency': random.choice(['INR', 'USD']),
        'mode': 'PAPER'
    }

# ============================================================================
# AUTHENTICATION ROUTES
# ============================================================================

@app.route('/login')
def login():
    """Login page"""
    if 'user' in session:
        return redirect(url_for('dashboard'))
    return render_template('auth/login.html')

@app.route('/signup')
def signup():
    """Signup page"""
    if 'user' in session:
        return redirect(url_for('dashboard'))
    return render_template('auth/signup.html')

@app.route('/auth/google')
def google_auth():
    """Initiate Google OAuth"""
    redirect_uri = url_for('google_callback', _external=True)
    return google.authorize_redirect(redirect_uri)

@app.route('/auth/google/callback')
def google_callback():
    """Handle Google OAuth callback"""
    try:
        token = google.authorize_access_token()
        user_info = token.get('userinfo')

        if user_info:
            email = user_info['email']
            name = user_info['name']
            google_id = user_info['sub']

            # Check if user exists
            user = get_user_by_email(email)

            if not user:
                # Create new user
                user_id = create_user(email, name, google_id)
                if user_id:
                    user = get_user_by_email(email)
                else:
                    flash('Error creating user account', 'error')
                    return redirect(url_for('login'))

            # Update last login
            update_user_login(email)

            # Store user in session
            session['user'] = {
                'id': user[0],
                'email': user[1],
                'name': user[2],
                'dark_mode': bool(user[7])
            }

            flash(f'Welcome back, {name}!', 'success')
            return redirect(url_for('dashboard'))

    except Exception as e:
        flash('Authentication failed. Please try again.', 'error')
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    """Logout user"""
    session.pop('user', None)
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

@app.route('/profile')
@login_required
def profile():
    """User profile page"""
    user_id = session['user']['id']
    settings = get_user_settings(user_id)
    return render_template('auth/profile.html', settings=settings)

@app.route('/toggle_dark_mode', methods=['POST'])
@login_required
def toggle_dark_mode():
    """Toggle dark mode for user"""
    user_id = session['user']['id']
    current_mode = session['user'].get('dark_mode', False)
    new_mode = not current_mode

    # Update in database
    conn = sqlite3.connect('trading_bot.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE users SET dark_mode = ? WHERE id = ?', (new_mode, user_id))
    conn.commit()
    conn.close()

    # Update session
    session['user']['dark_mode'] = new_mode

    return jsonify({'dark_mode': new_mode})

# ============================================================================
# MAIN ROUTES
# ============================================================================

@app.route('/')
@login_required
def dashboard():
    """Main dashboard page"""
    user_id = session['user']['id']
    settings = get_user_settings(user_id)

    return render_template('dashboard.html',
                         status=mock_bot_status,
                         markets=mock_markets,
                         performance_data=mock_performance_data,
                         user_settings=settings)

@app.route('/live')
@login_required
def live_dashboard():
    """Live trading dashboard with real-time charts"""
    user_id = session['user']['id']
    settings = get_user_settings(user_id)

    return render_template('live_dashboard.html',
                         status=mock_bot_status,
                         markets=mock_markets,
                         performance_data=mock_performance_data,
                         user_settings=settings)

@app.route('/positions')
@login_required
def positions():
    """Positions page"""
    return render_template('positions.html', positions=mock_positions)

@app.route('/trades')
@login_required
def trades():
    """Trades history page"""
    return render_template('trades.html', trades=mock_trades)

@app.route('/analytics')
@login_required
def analytics():
    """Analytics and reports page"""
    return render_template('analytics.html',
                         performance_data=mock_performance_data,
                         markets=mock_markets)

@app.route('/settings')
@login_required
def settings():
    """Settings page"""
    user_id = session['user']['id']
    user_settings = get_user_settings(user_id)

    return render_template('settings.html',
                         markets=mock_markets,
                         current_settings=mock_bot_status,
                         user_settings=user_settings)

@app.route('/help')
def help_page():
    """Help and documentation page"""
    return render_template('help.html')

@app.route('/about')
def about():
    """About page"""
    return render_template('about.html')

# ============================================================================
# BOT CONTROL ROUTES (MOCK FUNCTIONALITY)
# ============================================================================

@app.route('/start_bot', methods=['POST'])
@login_required
def start_bot():
    """Start the trading bot with real data feeds"""
    market = request.form.get('market', 'indian')
    mode = request.form.get('mode', 'paper')

    try:
        # Start real-time data feeds
        market_data_provider.start_data_feeds()

        # Start trading bot decision engine
        trading_bot.start_decision_engine()

        # Update bot status
        mock_bot_status['is_running'] = True
        mock_bot_status['current_market'] = market
        mock_bot_status['trading_mode'] = mode
        mock_bot_status['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Generate some initial positions and trades for demo
        global mock_positions, mock_trades
        mock_positions = [generate_mock_position() for _ in range(random.randint(1, 3))]
        mock_trades.extend([generate_mock_trade() for _ in range(random.randint(2, 5))])

        # Update performance
        mock_bot_status['positions'] = len(mock_positions)
        mock_bot_status['performance']['total_trades'] = len(mock_trades)
        mock_bot_status['performance']['winning_trades'] = int(len(mock_trades) * 0.7)
        mock_bot_status['performance']['win_rate'] = 70.0
        mock_bot_status['performance']['total_pnl'] = random.uniform(10, 50)
        mock_bot_status['performance']['daily_pnl'] = random.uniform(5, 15)

        flash(f'Bot started successfully! Real-time trading on {mock_markets[market]["name"]} in {mode} mode.', 'success')

    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        flash('Error starting bot. Please try again.', 'error')

    return redirect(url_for('dashboard'))

@app.route('/stop_bot', methods=['POST'])
@login_required
def stop_bot():
    """Stop the trading bot and data feeds"""
    try:
        # Stop real-time data feeds
        market_data_provider.stop_data_feeds()

        # Stop trading bot decision engine
        trading_bot.stop_decision_engine()

        # Update bot status
        mock_bot_status['is_running'] = False
        mock_bot_status['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        flash('Bot stopped successfully!', 'success')

    except Exception as e:
        logger.error(f"Error stopping bot: {e}")
        flash('Error stopping bot. Please try again.', 'error')

    return redirect(url_for('dashboard'))

@app.route('/add_mock_data', methods=['POST'])
def add_mock_data():
    """Add mock data for demonstration"""
    global mock_positions, mock_trades
    
    # Add mock positions
    mock_positions.extend([generate_mock_position() for _ in range(3)])
    
    # Add mock trades
    mock_trades.extend([generate_mock_trade() for _ in range(10)])
    
    # Update performance
    mock_bot_status['positions'] = len(mock_positions)
    mock_bot_status['performance']['total_trades'] = len(mock_trades)
    mock_bot_status['performance']['winning_trades'] = int(len(mock_trades) * 0.7)
    mock_bot_status['performance']['win_rate'] = 70.0
    mock_bot_status['performance']['total_pnl'] = random.uniform(50, 150)
    mock_bot_status['performance']['daily_pnl'] = random.uniform(10, 30)
    
    flash('Mock data added successfully! Refresh pages to see updates.', 'info')
    return redirect(url_for('dashboard'))

# ============================================================================
# API ENDPOINTS
# ============================================================================

@app.route('/api/status')
@login_required
def api_status():
    """API endpoint for bot status"""
    return jsonify(mock_bot_status)

@app.route('/api/positions')
@login_required
def api_positions():
    """API endpoint for current positions"""
    return jsonify(mock_positions)

@app.route('/api/trades')
@login_required
def api_trades():
    """API endpoint for trades history"""
    limit = request.args.get('limit', 20, type=int)
    return jsonify(mock_trades[-limit:] if mock_trades else [])

@app.route('/api/performance')
@login_required
def api_performance():
    """API endpoint for performance data"""
    return jsonify(mock_performance_data)

@app.route('/api/markets')
@login_required
def api_markets():
    """API endpoint for market data"""
    return jsonify(mock_markets)

@app.route('/api/market_data/<symbol>')
@login_required
def api_market_data(symbol):
    """API endpoint for real-time market data"""
    try:
        data = market_data_provider.get_symbol_data(symbol)
        if data:
            return jsonify(data)
        else:
            return jsonify({'error': 'Symbol not found or no data available'}), 404
    except Exception as e:
        logger.error(f"Error getting market data for {symbol}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/market_data')
@login_required
def api_all_market_data():
    """API endpoint for all real-time market data"""
    try:
        data = market_data_provider.get_all_data()
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error getting all market data: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/bot_decisions')
@login_required
def api_bot_decisions():
    """API endpoint for recent bot decisions"""
    try:
        limit = request.args.get('limit', 10, type=int)
        decisions = trading_bot.get_recent_decisions(limit)
        return jsonify(decisions)
    except Exception as e:
        logger.error(f"Error getting bot decisions: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/api/chart_data/<symbol>')
@login_required
def api_chart_data(symbol):
    """API endpoint for chart data"""
    try:
        data = market_data_provider.get_symbol_data(symbol)
        if data and 'ohlc_data' in data:
            return jsonify({
                'symbol': symbol,
                'ohlc_data': data['ohlc_data'],
                'current_price': data['price'],
                'change_pct': data['change_pct']
            })
        else:
            return jsonify({'error': 'Chart data not available'}), 404
    except Exception as e:
        logger.error(f"Error getting chart data for {symbol}: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# ============================================================================
# WEBSOCKET EVENTS
# ============================================================================

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'status': 'Connected to Trading Bot'})
    emit('bot_update', mock_bot_status)

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

@socketio.on('request_update')
def handle_request_update():
    """Handle request for status update"""
    emit('bot_update', mock_bot_status)
    emit('positions_update', mock_positions)
    emit('trades_update', mock_trades[-10:] if mock_trades else [])

# ============================================================================
# ERROR HANDLERS
# ============================================================================

@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# ============================================================================
# MAIN APPLICATION
# ============================================================================

if __name__ == '__main__':
    # Create directories
    os.makedirs('templates/auth', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('static/img', exist_ok=True)

    # Initialize database
    init_database()

    print("🌐 ULTIMATE TRADING BOT - ENHANCED WEBSITE")
    print("=" * 60)
    print("🔐 Google OAuth Authentication")
    print("📊 Angel One API Integration")
    print("🌙 Dark Mode Support")
    print("👤 User Account Management")
    print("💼 Personal Trading Settings")
    print("📱 Professional responsive design")
    print("⚡ Real-time updates with WebSocket")
    print("📊 Interactive charts and analytics")
    print("🛠️  Ready for live trading")
    print()
    print("🚀 Starting enhanced web server...")
    print("📱 Access your website at: http://localhost:5000")
    print("🌍 Or from any device: http://YOUR_IP:5000")
    print("⏹️  Press Ctrl+C to stop")
    print()
    print("📋 SETUP REQUIRED:")
    print("1. Set GOOGLE_CLIENT_ID in environment variables")
    print("2. Set GOOGLE_CLIENT_SECRET in environment variables")
    print("3. Configure Google OAuth redirect URI: http://localhost:5000/auth/google/callback")
    print()

    # Run the web application
    socketio.run(app,
                host='0.0.0.0',  # Allow access from any device
                port=5000,
                debug=True,
                allow_unsafe_werkzeug=True)
