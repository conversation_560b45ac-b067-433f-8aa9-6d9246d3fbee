"""
Main Trading Bot Engine
Orchestrates all components for autonomous intraday trading
"""
import asyncio
import logging
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import pandas as pd
import schedule

from angel_api import AngelOneAPI
from risk_manager import RiskManager
from technical_analysis import TechnicalAnalyzer, TechnicalSignal
from config import trading_config, stock_universe, is_trading_day, is_market_hours, env_config

logger = logging.getLogger(__name__)

class TradingBot:
    """Main autonomous trading bot class"""
    
    def __init__(self):
        # Initialize components
        self.angel_api = AngelOneAPI()
        self.risk_manager = RiskManager()
        self.technical_analyzer = TechnicalAnalyzer()
        
        # Trading state
        self.is_running = False
        self.is_logged_in = False
        self.market_data_cache = {}
        self.last_signal_time = {}
        
        # Configuration
        self.paper_trading = env_config.is_paper_trading()
        self.trading_symbols = stock_universe.HIGH_VOLUME_STOCKS[:10]  # Start with top 10
        
        # Performance tracking
        self.daily_trades = []
        self.daily_pnl = 0.0
        
        logger.info(f"Trading Bot initialized - Paper Trading: {self.paper_trading}")
    
    async def start(self):
        """Start the trading bot"""
        logger.info("Starting Trading Bot...")
        
        # Check if it's a trading day
        if not is_trading_day():
            logger.info("Today is not a trading day. Bot will not start.")
            return
        
        # Login to Angel One
        if not await self.login():
            logger.error("Failed to login to Angel One. Exiting.")
            return
        
        # Schedule daily tasks
        self._schedule_daily_tasks()
        
        # Start main trading loop
        self.is_running = True
        await self._main_trading_loop()
    
    async def stop(self):
        """Stop the trading bot"""
        logger.info("Stopping Trading Bot...")
        self.is_running = False
        
        # Square off all positions before stopping
        await self.square_off_all_positions("Bot shutdown")
        
        # Logout from Angel One
        if self.is_logged_in:
            self.angel_api.logout()
            self.is_logged_in = False
        
        logger.info("Trading Bot stopped")
    
    async def login(self) -> bool:
        """Login to Angel One API"""
        try:
            success = self.angel_api.login()
            if success:
                self.is_logged_in = True
                
                # Get profile and funds info
                profile = self.angel_api.get_profile()
                funds = self.angel_api.get_funds()
                
                if profile:
                    logger.info(f"Logged in as: {profile.get('name', 'Unknown')}")
                
                if funds:
                    available_cash = funds.get('availablecash', 0)
                    logger.info(f"Available cash: ₹{available_cash}")
                
                return True
            else:
                logger.error("Login failed")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
    
    def _schedule_daily_tasks(self):
        """Schedule daily tasks"""
        # Reset daily metrics at market open
        schedule.every().day.at("09:00").do(self._reset_daily_metrics)
        
        # Square off all positions 10 minutes before market close
        schedule.every().day.at("15:20").do(self._schedule_square_off)
        
        # End of day cleanup
        schedule.every().day.at("15:35").do(self._end_of_day_cleanup)
    
    async def _main_trading_loop(self):
        """Main trading loop - runs every minute during market hours"""
        logger.info("Starting main trading loop")
        
        while self.is_running:
            try:
                # Run scheduled tasks
                schedule.run_pending()
                
                # Check if market is open
                if not is_market_hours():
                    logger.debug("Market is closed, waiting...")
                    await asyncio.sleep(60)  # Wait 1 minute
                    continue
                
                # Main trading logic
                await self._trading_cycle()
                
                # Wait for next cycle (1 minute)
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in main trading loop: {str(e)}")
                await asyncio.sleep(60)
    
    async def _trading_cycle(self):
        """Single trading cycle - analyze and execute trades"""
        start_time = time.time()
        
        try:
            # Update market data for all symbols
            await self._update_market_data()
            
            # Update position prices
            await self._update_position_prices()
            
            # Check stop losses and targets
            await self._check_exit_conditions()
            
            # Generate new signals if we have capacity
            if len(self.risk_manager.positions) < trading_config.MAX_POSITIONS:
                await self._scan_for_signals()
            
            # Log performance metrics
            self._log_performance_metrics()
            
        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}")
        
        cycle_time = time.time() - start_time
        logger.debug(f"Trading cycle completed in {cycle_time:.2f} seconds")
    
    async def _update_market_data(self):
        """Update market data for all trading symbols"""
        for symbol in self.trading_symbols:
            try:
                # Get current LTP
                ltp = self.angel_api.get_ltp(symbol)
                if ltp:
                    if symbol not in self.market_data_cache:
                        self.market_data_cache[symbol] = []
                    
                    # Add current price data
                    current_time = datetime.now(trading_config.TIMEZONE)
                    self.market_data_cache[symbol].append({
                        'timestamp': current_time,
                        'price': ltp
                    })
                    
                    # Keep only last 100 data points
                    if len(self.market_data_cache[symbol]) > 100:
                        self.market_data_cache[symbol] = self.market_data_cache[symbol][-100:]
                
            except Exception as e:
                logger.error(f"Error updating market data for {symbol}: {str(e)}")
    
    async def _update_position_prices(self):
        """Update current prices for all positions"""
        if not self.risk_manager.positions:
            return
        
        price_updates = {}
        
        for symbol in self.risk_manager.positions.keys():
            try:
                ltp = self.angel_api.get_ltp(symbol)
                if ltp:
                    price_updates[symbol] = ltp
            except Exception as e:
                logger.error(f"Error getting LTP for {symbol}: {str(e)}")
        
        if price_updates:
            self.risk_manager.update_position_prices(price_updates)
    
    async def _check_exit_conditions(self):
        """Check stop loss and target conditions for all positions"""
        
        # Check stop losses
        stop_loss_hits = self.risk_manager.check_stop_losses()
        for symbol in stop_loss_hits:
            await self._exit_position(symbol, "Stop Loss Hit")
        
        # Check targets
        target_hits = self.risk_manager.check_targets()
        for symbol in target_hits:
            await self._exit_position(symbol, "Target Hit")
    
    async def _scan_for_signals(self):
        """Scan all symbols for trading signals"""
        
        for symbol in self.trading_symbols:
            try:
                # Skip if we already have a position in this symbol
                if symbol in self.risk_manager.positions:
                    continue
                
                # Skip if we generated a signal recently (avoid overtrading)
                if symbol in self.last_signal_time:
                    time_since_last = datetime.now(trading_config.TIMEZONE) - self.last_signal_time[symbol]
                    if time_since_last < timedelta(minutes=5):
                        continue
                
                # Get historical data for analysis
                df = await self._get_historical_data(symbol)
                if df is None or len(df) < 50:
                    continue
                
                # Analyze for signals
                signals = self.technical_analyzer.analyze_stock(df, symbol)
                
                if signals:
                    # Get confluence signal
                    confluence_signal = self.technical_analyzer.get_confluence_signal(signals)
                    
                    if confluence_signal and confluence_signal.confidence > 0.6:
                        await self._process_signal(confluence_signal)
                        self.last_signal_time[symbol] = datetime.now(trading_config.TIMEZONE)
                
            except Exception as e:
                logger.error(f"Error scanning {symbol} for signals: {str(e)}")
    
    async def _get_historical_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Get historical OHLCV data for technical analysis"""

        try:
            # Get historical data from Angel One API
            df = self.angel_api.get_historical_data(
                symbol=symbol,
                interval="ONE_MINUTE",
                from_date=None,  # Will use default 30 days
                to_date=None     # Will use current time
            )

            if df is not None and len(df) >= 50:
                # Ensure we have enough data for technical analysis
                logger.debug(f"Historical data fetched for {symbol}: {len(df)} records")
                return df
            else:
                logger.warning(f"Insufficient historical data for {symbol}")
                return None

        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {str(e)}")
            return None
    
    async def _process_signal(self, signal: TechnicalSignal):
        """Process a trading signal and execute if valid"""
        
        try:
            logger.info(f"Processing signal: {signal.symbol} {signal.signal_type} "
                       f"Strategy: {signal.strategy} Confidence: {signal.confidence:.2f}")
            
            # Calculate position size
            position_size = self.risk_manager.calculate_position_size(
                signal.entry_price, signal.stop_loss
            )
            
            if position_size <= 0:
                logger.info(f"Position size calculation returned 0 for {signal.symbol}")
                return
            
            # Validate trade
            is_valid, message = self.risk_manager.validate_trade(
                signal.symbol, signal.signal_type, position_size, 
                signal.entry_price, signal.stop_loss
            )
            
            if not is_valid:
                logger.info(f"Trade validation failed for {signal.symbol}: {message}")
                return
            
            # Execute trade
            if self.paper_trading:
                await self._execute_paper_trade(signal, position_size)
            else:
                await self._execute_real_trade(signal, position_size)
                
        except Exception as e:
            logger.error(f"Error processing signal for {signal.symbol}: {str(e)}")
    
    async def _execute_paper_trade(self, signal: TechnicalSignal, position_size: int):
        """Execute trade in paper trading mode"""
        
        # Simulate order execution
        order_id = f"PAPER_{signal.symbol}_{int(time.time())}"
        
        # Add position to risk manager
        success = self.risk_manager.add_position(
            symbol=signal.symbol,
            quantity=position_size,
            entry_price=signal.entry_price,
            side=signal.signal_type,
            stop_loss=signal.stop_loss,
            target=signal.target,
            order_id=order_id
        )
        
        if success:
            logger.info(f"Paper trade executed: {signal.symbol} {signal.signal_type} "
                       f"{position_size} @ {signal.entry_price}")
        else:
            logger.error(f"Failed to add paper position for {signal.symbol}")
    
    async def _execute_real_trade(self, signal: TechnicalSignal, position_size: int):
        """Execute real trade through Angel One API"""
        
        try:
            # Place market order
            order_id = self.angel_api.place_order(
                symbol=signal.symbol,
                transaction_type=signal.signal_type,
                quantity=position_size,
                order_type="MARKET",
                product="MIS"  # Intraday
            )
            
            if order_id:
                # Add position to risk manager
                success = self.risk_manager.add_position(
                    symbol=signal.symbol,
                    quantity=position_size,
                    entry_price=signal.entry_price,
                    side=signal.signal_type,
                    stop_loss=signal.stop_loss,
                    target=signal.target,
                    order_id=order_id
                )
                
                if success:
                    logger.info(f"Real trade executed: {signal.symbol} {signal.signal_type} "
                               f"{position_size} @ {signal.entry_price} Order ID: {order_id}")
                else:
                    logger.error(f"Failed to add position for {signal.symbol}, cancelling order")
                    self.angel_api.cancel_order(order_id)
            else:
                logger.error(f"Failed to place order for {signal.symbol}")
                
        except Exception as e:
            logger.error(f"Error executing real trade for {signal.symbol}: {str(e)}")
    
    async def _exit_position(self, symbol: str, reason: str):
        """Exit a position"""
        
        if symbol not in self.risk_manager.positions:
            logger.warning(f"No position found for {symbol}")
            return
        
        position = self.risk_manager.positions[symbol]
        current_price = position.current_price
        
        try:
            if self.paper_trading:
                # Paper trading exit
                realized_pnl = self.risk_manager.remove_position(symbol, current_price, reason)
                logger.info(f"Paper position closed: {symbol} P&L: {realized_pnl:.2f} Reason: {reason}")
            else:
                # Real trading exit
                exit_side = "SELL" if position.side == "BUY" else "BUY"
                
                order_id = self.angel_api.place_order(
                    symbol=symbol,
                    transaction_type=exit_side,
                    quantity=position.quantity,
                    order_type="MARKET",
                    product="MIS"
                )
                
                if order_id:
                    realized_pnl = self.risk_manager.remove_position(symbol, current_price, reason)
                    logger.info(f"Position closed: {symbol} P&L: {realized_pnl:.2f} "
                               f"Reason: {reason} Order ID: {order_id}")
                else:
                    logger.error(f"Failed to place exit order for {symbol}")
                    
        except Exception as e:
            logger.error(f"Error exiting position for {symbol}: {str(e)}")
    
    async def square_off_all_positions(self, reason: str = "End of day"):
        """Square off all open positions"""
        
        positions_to_close = list(self.risk_manager.positions.keys())
        
        for symbol in positions_to_close:
            await self._exit_position(symbol, reason)
        
        logger.info(f"All positions squared off. Reason: {reason}")
    
    def _reset_daily_metrics(self):
        """Reset daily metrics at start of trading day"""
        self.risk_manager.reset_daily_metrics()
        self.daily_trades = []
        self.daily_pnl = 0.0
        logger.info("Daily metrics reset")
    
    def _schedule_square_off(self):
        """Schedule square off of all positions"""
        asyncio.create_task(self.square_off_all_positions("Market close approaching"))
    
    def _end_of_day_cleanup(self):
        """End of day cleanup tasks"""
        # Generate daily report
        self._generate_daily_report()
        
        # Clear market data cache
        self.market_data_cache = {}
        
        logger.info("End of day cleanup completed")
    
    def _log_performance_metrics(self):
        """Log current performance metrics"""
        
        metrics = self.risk_manager.get_portfolio_summary()
        position_summary = self.risk_manager.get_position_summary()
        
        logger.debug(f"Portfolio: Capital: ₹{metrics.available_capital:.2f} "
                    f"Daily P&L: ₹{metrics.daily_pnl:.2f} "
                    f"Positions: {metrics.current_positions}")
        
        if position_summary:
            for symbol, pos_data in position_summary.items():
                logger.debug(f"Position {symbol}: {pos_data['side']} {pos_data['quantity']} "
                           f"@ {pos_data['entry_price']} P&L: ₹{pos_data['unrealized_pnl']:.2f}")
    
    def _generate_daily_report(self):
        """Generate end of day performance report"""
        
        metrics = self.risk_manager.get_portfolio_summary()
        
        report = f"""
        ===== DAILY TRADING REPORT =====
        Date: {datetime.now(trading_config.TIMEZONE).strftime('%Y-%m-%d')}
        
        Performance:
        - Daily P&L: ₹{metrics.daily_pnl:.2f}
        - Total Trades: {len(self.daily_trades)}
        - Available Capital: ₹{metrics.available_capital:.2f}
        - Max Drawdown: {metrics.max_drawdown:.2%}
        
        Risk Metrics:
        - Max Positions: {trading_config.MAX_POSITIONS}
        - Risk per Trade: ₹{metrics.max_risk_per_trade:.2f}
        - Daily Loss Limit: ₹{metrics.daily_loss_limit:.2f}
        
        ================================
        """
        
        logger.info(report)
