{% extends "base.html" %}

{% block title %}Profile - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-user-circle"></i> User Profile
        </h1>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user"></i> Account Information
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                
                <h5>{{ session.user.name }}</h5>
                <p class="text-muted">{{ session.user.email }}</p>
                
                <div class="mb-3">
                    <span class="badge bg-success">
                        <i class="fas fa-check-circle"></i> Verified Account
                    </span>
                </div>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="toggleDarkMode()">
                        <i class="fas fa-moon" id="darkModeIcon"></i>
                        <span id="darkModeText">Enable Dark Mode</span>
                    </button>
                    
                    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Account Stats -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Account Statistics
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">15</h4>
                        <small class="text-muted">Days Active</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">127</h4>
                        <small class="text-muted">Total Trades</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">78.5%</h4>
                        <small class="text-muted">Win Rate</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">₹2,450</h4>
                        <small class="text-muted">Total P&L</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Angel One Integration -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-link"></i> Angel One Integration
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Connect your Angel One account</strong> to enable live trading with real money.
                    Your credentials are encrypted and stored securely.
                </div>
                
                <form id="angelOneForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="angelApiKey" class="form-label">
                                    <i class="fas fa-key"></i> API Key
                                </label>
                                <input type="password" class="form-control" id="angelApiKey" 
                                       placeholder="Enter your Angel One API key">
                                <small class="form-text text-muted">
                                    Get this from Angel One SmartAPI portal
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="angelClientId" class="form-label">
                                    <i class="fas fa-id-card"></i> Client ID
                                </label>
                                <input type="text" class="form-control" id="angelClientId" 
                                       placeholder="Enter your client ID">
                                <small class="form-text text-muted">
                                    Your Angel One client ID
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="angelPassword" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <input type="password" class="form-control" id="angelPassword" 
                                       placeholder="Enter your Angel One password">
                                <small class="form-text text-muted">
                                    Your Angel One login password
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="angelTotp" class="form-label">
                                    <i class="fas fa-mobile-alt"></i> TOTP Secret (Optional)
                                </label>
                                <input type="password" class="form-control" id="angelTotp" 
                                       placeholder="TOTP secret for 2FA">
                                <small class="form-text text-muted">
                                    For automatic 2FA (if enabled)
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableLiveTrading">
                            <label class="form-check-label" for="enableLiveTrading">
                                <strong>Enable Live Trading</strong>
                            </label>
                        </div>
                        <small class="form-text text-danger">
                            ⚠️ Only enable after thorough testing in paper trading mode
                        </small>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Angel One Settings
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="testConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Trading Preferences -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-cog"></i> Trading Preferences
            </div>
            <div class="card-body">
                <form id="preferencesForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="preferredMarket" class="form-label">Preferred Market</label>
                                <select class="form-select" id="preferredMarket">
                                    <option value="indian" {% if settings and settings[9] == 'indian' %}selected{% endif %}>
                                        Indian Stock Market
                                    </option>
                                    <option value="us" {% if settings and settings[9] == 'us' %}selected{% endif %}>
                                        US Stock Market
                                    </option>
                                    <option value="crypto" {% if settings and settings[9] == 'crypto' %}selected{% endif %}>
                                        Cryptocurrency
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tradingMode" class="form-label">Default Trading Mode</label>
                                <select class="form-select" id="tradingMode">
                                    <option value="paper" {% if settings and settings[10] == 'paper' %}selected{% endif %}>
                                        Paper Trading (Safe)
                                    </option>
                                    <option value="live">Live Trading (Real Money)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="defaultCapital" class="form-label">Default Capital</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="defaultCapital" 
                                           value="{{ settings[2] if settings else 100 }}" min="100">
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="maxPositions" class="form-label">Max Positions</label>
                                <input type="number" class="form-control" id="maxPositions" 
                                       value="{{ settings[3] if settings else 5 }}" min="1" max="20">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="riskTolerance" class="form-label">Risk Tolerance</label>
                        <select class="form-select" id="riskTolerance">
                            <option value="conservative">Conservative (Low Risk)</option>
                            <option value="moderate" selected>Moderate (Balanced)</option>
                            <option value="aggressive">Aggressive (High Risk)</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> Save Preferences
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Security Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-shield-alt"></i> Security & Privacy
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Account Security</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Google OAuth Authentication
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Encrypted Data Storage
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Secure API Connections
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check-circle text-success"></i>
                                Session Management
                            </li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Data Privacy</h6>
                        <p class="small text-muted">
                            Your trading data and personal information are protected with bank-level security. 
                            We never share your data with third parties and all API credentials are encrypted.
                        </p>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-info btn-sm">
                                <i class="fas fa-download"></i> Download My Data
                            </button>
                            <button class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-user-times"></i> Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Dark mode toggle
    function toggleDarkMode() {
        fetch('/toggle_dark_mode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            const icon = document.getElementById('darkModeIcon');
            const text = document.getElementById('darkModeText');
            
            if (data.dark_mode) {
                icon.className = 'fas fa-sun';
                text.textContent = 'Disable Dark Mode';
                document.body.classList.add('dark-mode');
            } else {
                icon.className = 'fas fa-moon';
                text.textContent = 'Enable Dark Mode';
                document.body.classList.remove('dark-mode');
            }
        })
        .catch(error => {
            console.error('Error toggling dark mode:', error);
        });
    }
    
    // Initialize dark mode state
    document.addEventListener('DOMContentLoaded', function() {
        const isDarkMode = {{ 'true' if session.user.dark_mode else 'false' }};
        const icon = document.getElementById('darkModeIcon');
        const text = document.getElementById('darkModeText');
        
        if (isDarkMode) {
            icon.className = 'fas fa-sun';
            text.textContent = 'Disable Dark Mode';
            document.body.classList.add('dark-mode');
        }
    });
    
    // Angel One form submission
    document.getElementById('angelOneForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            api_key: document.getElementById('angelApiKey').value,
            client_id: document.getElementById('angelClientId').value,
            password: document.getElementById('angelPassword').value,
            totp_secret: document.getElementById('angelTotp').value,
            enable_live_trading: document.getElementById('enableLiveTrading').checked
        };
        
        // Here you would send the data to your backend
        console.log('Angel One settings:', formData);
        
        // Show success message
        showAlert('Angel One settings saved successfully!', 'success');
    });
    
    // Preferences form submission
    document.getElementById('preferencesForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            preferred_market: document.getElementById('preferredMarket').value,
            trading_mode: document.getElementById('tradingMode').value,
            default_capital: document.getElementById('defaultCapital').value,
            max_positions: document.getElementById('maxPositions').value,
            risk_tolerance: document.getElementById('riskTolerance').value
        };
        
        console.log('Preferences:', formData);
        showAlert('Trading preferences saved successfully!', 'success');
    });
    
    // Test Angel One connection
    function testConnection() {
        const apiKey = document.getElementById('angelApiKey').value;
        const clientId = document.getElementById('angelClientId').value;
        const password = document.getElementById('angelPassword').value;
        
        if (!apiKey || !clientId || !password) {
            showAlert('Please fill in all required Angel One credentials', 'warning');
            return;
        }
        
        // Show loading state
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
        btn.disabled = true;
        
        // Simulate API test (replace with actual API call)
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
            showAlert('Connection test successful! Angel One API is working.', 'success');
        }, 2000);
    }
    
    // Utility function to show alerts
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // Insert at top of main content
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alertDiv, mainContent.firstChild);
        
        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 3000);
    }
</script>
{% endblock %}
