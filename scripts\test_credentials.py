#!/usr/bin/env python3
"""
Angel One Credentials Testing Script
Tests and validates Angel One SmartAPI credentials
"""
import sys
import os
import time
from datetime import datetime

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def test_environment_setup():
    """Test if environment is properly set up"""
    print("🔧 Testing Environment Setup...")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = '.env'
    if os.path.exists(env_file):
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
        print("   Run: python scripts/setup_bot.py")
        return False
    
    # Check file permissions
    try:
        stat_info = os.stat(env_file)
        permissions = oct(stat_info.st_mode)[-3:]
        if permissions == '600':
            print("✅ .env file permissions secure (600)")
        else:
            print(f"⚠️  .env file permissions: {permissions} (should be 600)")
            print("   Run: chmod 600 .env")
    except Exception as e:
        print(f"⚠️  Could not check file permissions: {e}")
    
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ Environment variables loaded")
    except ImportError:
        print("❌ python-dotenv not installed")
        print("   Run: pip install python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return False
    
    return True

def test_credential_presence():
    """Test if all required credentials are present"""
    print("\n🔑 Testing Credential Presence...")
    print("=" * 50)
    
    try:
        from config import env_config
        creds = env_config.get_angel_credentials()
        
        required_creds = {
            'API Key': creds['api_key'],
            'Client ID': creds['client_id'],
            'Password': creds['password'],
            'TOTP Secret': creds['totp_secret']
        }
        
        all_present = True
        
        for name, value in required_creds.items():
            if value:
                # Mask sensitive data for display
                if name == 'Password':
                    display_value = '*' * len(value)
                else:
                    display_value = value[:8] + '...' if len(value) > 8 else value
                print(f"✅ {name}: {display_value}")
            else:
                print(f"❌ {name}: Missing")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"❌ Error checking credentials: {e}")
        return False

def test_totp_generation():
    """Test TOTP generation"""
    print("\n🔐 Testing TOTP Generation...")
    print("=" * 50)
    
    try:
        import pyotp
        from config import env_config
        
        creds = env_config.get_angel_credentials()
        totp_secret = creds['totp_secret']
        
        if not totp_secret:
            print("❌ TOTP secret not configured")
            return False
        
        # Generate TOTP
        totp = pyotp.TOTP(totp_secret)
        current_totp = totp.now()
        
        print(f"✅ TOTP generated successfully: {current_totp}")
        
        # Test multiple generations
        print("Testing TOTP stability...")
        for i in range(3):
            totp_test = totp.now()
            print(f"   TOTP {i+1}: {totp_test}")
            time.sleep(1)
        
        return True
        
    except ImportError:
        print("❌ pyotp not installed")
        print("   Run: pip install pyotp")
        return False
    except Exception as e:
        print(f"❌ TOTP generation error: {e}")
        print("   Check TOTP secret format (should be base32)")
        return False

def test_api_connectivity():
    """Test Angel One API connectivity"""
    print("\n🔌 Testing API Connectivity...")
    print("=" * 50)
    
    try:
        from angel_api import AngelOneAPI
        
        # Create API instance
        api = AngelOneAPI()
        print("✅ API instance created")
        
        # Test login
        print("Testing login...")
        login_success = api.login()
        
        if login_success:
            print("✅ Login successful")
            
            # Test profile fetch
            print("Testing profile fetch...")
            profile = api.get_profile()
            if profile:
                print(f"✅ Profile: {profile.get('name', 'Unknown')}")
                print(f"   Client ID: {profile.get('clientcode', 'Unknown')}")
            else:
                print("⚠️  Profile fetch failed")
            
            # Test funds fetch
            print("Testing funds fetch...")
            funds = api.get_funds()
            if funds:
                cash = funds.get('availablecash', 0)
                print(f"✅ Available cash: ₹{cash}")
            else:
                print("⚠️  Funds fetch failed")
            
            # Test LTP fetch
            print("Testing LTP fetch...")
            ltp = api.get_ltp('RELIANCE')
            if ltp:
                print(f"✅ RELIANCE LTP: ₹{ltp:.2f}")
            else:
                print("⚠️  LTP fetch failed")
            
            # Logout
            api.logout()
            print("✅ Logout successful")
            
            return True
            
        else:
            print("❌ Login failed")
            print("   Check credentials and try again")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Check if all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ API connectivity error: {e}")
        return False

def test_market_data_access():
    """Test market data access"""
    print("\n📊 Testing Market Data Access...")
    print("=" * 50)
    
    try:
        from angel_api import AngelOneAPI
        
        api = AngelOneAPI()
        
        if not api.login():
            print("❌ Could not login for market data test")
            return False
        
        test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        
        for symbol in test_symbols:
            print(f"Testing {symbol}...")
            
            # Test LTP
            ltp = api.get_ltp(symbol)
            if ltp and ltp > 0:
                print(f"   ✅ LTP: ₹{ltp:.2f}")
            else:
                print(f"   ❌ LTP fetch failed")
                continue
            
            # Test historical data
            df = api.get_historical_data(symbol, "ONE_MINUTE")
            if df is not None and len(df) > 0:
                print(f"   ✅ Historical data: {len(df)} records")
                print(f"      Latest: {df.index[-1]} = ₹{df['close'].iloc[-1]:.2f}")
            else:
                print(f"   ❌ Historical data fetch failed")
        
        api.logout()
        return True
        
    except Exception as e:
        print(f"❌ Market data test error: {e}")
        return False

def test_trading_permissions():
    """Test trading permissions"""
    print("\n💼 Testing Trading Permissions...")
    print("=" * 50)
    
    try:
        from angel_api import AngelOneAPI
        
        api = AngelOneAPI()
        
        if not api.login():
            print("❌ Could not login for permissions test")
            return False
        
        # Test order book access
        print("Testing order book access...")
        orders = api.get_order_book()
        if orders is not None:
            print(f"✅ Order book accessible: {len(orders)} orders")
        else:
            print("❌ Order book access failed")
        
        # Test positions access
        print("Testing positions access...")
        positions = api.get_positions()
        if positions is not None:
            print(f"✅ Positions accessible: {len(positions)} positions")
        else:
            print("❌ Positions access failed")
        
        # Test funds access
        print("Testing funds access...")
        funds = api.get_funds()
        if funds:
            print("✅ Funds accessible")
            
            # Check if sufficient funds for testing
            cash = float(funds.get('availablecash', 0))
            if cash >= 10000:
                print(f"✅ Sufficient funds for testing: ₹{cash:,.2f}")
            else:
                print(f"⚠️  Low funds for testing: ₹{cash:,.2f} (recommend ₹10,000+)")
        else:
            print("❌ Funds access failed")
        
        api.logout()
        return True
        
    except Exception as e:
        print(f"❌ Trading permissions test error: {e}")
        return False

def generate_credential_report():
    """Generate credential test report"""
    print("\n📋 Credential Test Report")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Credential Presence", test_credential_presence),
        ("TOTP Generation", test_totp_generation),
        ("API Connectivity", test_api_connectivity),
        ("Market Data Access", test_market_data_access),
        ("Trading Permissions", test_trading_permissions)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results[test_name] = "PASSED" if result else "FAILED"
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
            results[test_name] = "ERROR"
    
    # Summary
    print("\n" + "=" * 60)
    print("CREDENTIAL TEST SUMMARY")
    print("=" * 60)
    
    for test_name, result in results.items():
        status_icon = "✅" if result == "PASSED" else "❌"
        print(f"{status_icon} {test_name}: {result}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL CREDENTIAL TESTS PASSED!")
        print("✅ Your Angel One credentials are properly configured")
        print("✅ API connectivity is working")
        print("✅ Market data access is available")
        print("✅ Trading permissions are enabled")
        print("\n🚀 Ready to run the trading bot!")
        print("\nNext steps:")
        print("1. Run: python scripts/run_tests.py")
        print("2. Start paper trading: python main.py")
        print("3. Monitor: tail -f logs/trading_bot.log")
        return True
    else:
        print("\n❌ SOME CREDENTIAL TESTS FAILED!")
        print("Please fix the issues above before proceeding.")
        print("\nCommon solutions:")
        print("1. Check .env file has all credentials")
        print("2. Verify Angel One account is active")
        print("3. Ensure SmartAPI subscription is enabled")
        print("4. Contact Angel One support if needed")
        return False

def main():
    """Main function"""
    print("🔐 Angel One Credentials Testing")
    print("=" * 60)
    print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        success = generate_credential_report()
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n\n👋 Testing stopped by user.")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
