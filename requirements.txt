# Angel One SmartAPI Trading Bot Requirements

# Core Trading Libraries
smartapi-python==1.3.0          # Angel One SmartAPI
pandas>=1.5.0                    # Data manipulation
numpy>=1.21.0                    # Numerical computing
ta-lib>=0.4.25                   # Technical analysis indicators

# API & Networking
requests>=2.28.0                 # HTTP requests
websocket-client>=1.4.0          # WebSocket for real-time data
aiohttp>=3.8.0                   # Async HTTP client

# Data Storage
sqlite3                          # Built-in database (for development)
sqlalchemy>=1.4.0               # Database ORM

# Security & Configuration
python-dotenv>=0.20.0            # Environment variables
cryptography>=37.0.0             # Encryption for API keys
keyring>=23.0.0                  # Secure credential storage

# Logging & Monitoring
structlog>=22.0.0                # Structured logging
python-json-logger>=2.0.0        # JSON logging

# Utilities
pytz>=2022.1                     # Timezone handling
schedule>=1.2.0                  # Task scheduling
python-dateutil>=2.8.0           # Date utilities

# Development & Testing
pytest>=7.0.0                    # Testing framework
black>=22.0.0                    # Code formatting
flake8>=5.0.0                    # Code linting

# Optional: Advanced Features
scikit-learn>=1.1.0              # Machine learning (future use)
matplotlib>=3.5.0                # Plotting and visualization
seaborn>=0.11.0                  # Statistical visualization

# Risk Management
psutil>=5.9.0                    # System monitoring
