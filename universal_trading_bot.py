#!/usr/bin/env python3
"""
UNIVERSAL TRADING BOT - SMART MARKET SELECTOR
One bot that can trade any market: Indian, EU, US, Crypto, etc.
"""
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
import json
import logging
import os
import sys
from dotenv import load_dotenv
import yfinance as yf
import time
from enum import Enum

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/universal_bot.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class MarketType(Enum):
    INDIAN = "indian"
    US = "us"
    EUROPEAN = "european"
    CRYPTO = "crypto"
    FOREX = "forex"

class TradingAction(Enum):
    STRONG_BUY = 2
    BUY = 1
    HOLD = 0
    SELL = -1
    STRONG_SELL = -2

class MarketConfig:
    """Configuration for different markets"""
    
    MARKETS = {
        MarketType.INDIAN: {
            'name': 'Indian Stock Market',
            'suffix': '.NS',
            'currency': 'INR',
            'timezone': 'Asia/Kolkata',
            'trading_hours': {'open': '09:15', 'close': '15:30'},
            'symbols': ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS', 'ITC', 'HINDUNILVR'],
            'api': 'angel_one',
            'confidence_threshold': 0.42,
            'risk_multiplier': 1.0
        },
        MarketType.US: {
            'name': 'US Stock Market',
            'suffix': '',
            'currency': 'USD',
            'timezone': 'America/New_York',
            'trading_hours': {'open': '09:30', 'close': '16:00'},
            'symbols': ['AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'NVDA', 'META'],
            'api': 'yahoo_finance',
            'confidence_threshold': 0.45,
            'risk_multiplier': 0.8
        },
        MarketType.EUROPEAN: {
            'name': 'European Stock Market',
            'suffix': '',
            'currency': 'EUR',
            'timezone': 'Europe/London',
            'trading_hours': {'open': '08:00', 'close': '16:30'},
            'symbols': ['ASML.AS', 'SAP.DE', 'NESN.SW', 'MC.PA', 'NOVO-B.CO', 'INGA.AS'],
            'api': 'yahoo_finance',
            'confidence_threshold': 0.48,
            'risk_multiplier': 0.9
        },
        MarketType.CRYPTO: {
            'name': 'Cryptocurrency Market',
            'suffix': '-USD',
            'currency': 'USD',
            'timezone': 'UTC',
            'trading_hours': {'open': '00:00', 'close': '23:59'},  # 24/7
            'symbols': ['BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'DOT', 'MATIC'],
            'api': 'yahoo_finance',
            'confidence_threshold': 0.35,
            'risk_multiplier': 1.5
        },
        MarketType.FOREX: {
            'name': 'Forex Market',
            'suffix': '=X',
            'currency': 'USD',
            'timezone': 'UTC',
            'trading_hours': {'open': '00:00', 'close': '23:59'},  # 24/7
            'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF'],
            'api': 'yahoo_finance',
            'confidence_threshold': 0.50,
            'risk_multiplier': 2.0
        }
    }

class UniversalTradingBot:
    """Universal bot that can trade any market"""
    
    def __init__(self, paper_trading=True):
        self.paper_trading = paper_trading
        self.capital = 100.0
        self.positions = {}
        self.trades = []
        
        # Current market selection
        self.current_market = None
        self.market_config = None
        
        # Performance tracking per market
        self.market_performance = {}
        
        logger.info("UNIVERSAL TRADING BOT INITIALIZED")
        logger.info(f"Mode: {'PAPER TRADING' if paper_trading else 'LIVE TRADING'}")
        logger.info(f"Available Markets: {len(MarketConfig.MARKETS)}")
        
    def show_market_menu(self):
        """Display market selection menu"""
        print("\n" + "="*60)
        print("🌍 UNIVERSAL TRADING BOT - MARKET SELECTOR")
        print("="*60)
        print(f"💰 Capital: {self.capital:.2f}")
        print(f"📊 Mode: {'Paper Trading' if self.paper_trading else 'Live Trading'}")
        print()
        
        print("🌍 AVAILABLE MARKETS:")
        for i, (market_type, config) in enumerate(MarketConfig.MARKETS.items(), 1):
            status = self.get_market_status(market_type)
            print(f"   {i}. {config['name']} ({config['currency']}) - {status}")
        
        print()
        print("⚙️  OPTIONS:")
        print("   A. 🤖 Auto-Select Best Market")
        print("   B. 📊 View Market Performance")
        print("   C. 🔄 Trade All Markets")
        print("   0. ❌ Exit")
        print()
    
    def get_market_status(self, market_type: MarketType) -> str:
        """Get current market status"""
        config = MarketConfig.MARKETS[market_type]
        
        if market_type in [MarketType.CRYPTO, MarketType.FOREX]:
            return "🟢 24/7 OPEN"
        
        # For stock markets, check trading hours (simplified)
        now = datetime.now()
        hour = now.hour
        
        if market_type == MarketType.INDIAN:
            if 9 <= hour <= 15:
                return "🟢 OPEN"
            else:
                return "🔴 CLOSED"
        elif market_type == MarketType.US:
            # Approximate US hours in local time
            if 19 <= hour <= 23 or 0 <= hour <= 2:  # Rough EST conversion
                return "🟢 OPEN"
            else:
                return "🔴 CLOSED"
        elif market_type == MarketType.EUROPEAN:
            if 8 <= hour <= 16:
                return "🟢 OPEN"
            else:
                return "🔴 CLOSED"
        
        return "❓ UNKNOWN"
    
    def select_market(self) -> Optional[MarketType]:
        """Interactive market selection"""
        while True:
            self.show_market_menu()
            choice = input("Select market (1-5, A, B, C, 0): ").strip().upper()
            
            if choice == "0":
                return None
            elif choice == "A":
                return self.auto_select_market()
            elif choice == "B":
                self.show_market_performance()
                continue
            elif choice == "C":
                return "ALL_MARKETS"
            elif choice.isdigit():
                market_index = int(choice) - 1
                market_types = list(MarketConfig.MARKETS.keys())
                if 0 <= market_index < len(market_types):
                    return market_types[market_index]
            
            print("❌ Invalid choice. Please try again.")
    
    def auto_select_market(self) -> MarketType:
        """Automatically select the best market"""
        print("\n🤖 AUTO-SELECTING BEST MARKET...")
        
        # Score markets based on various factors
        market_scores = {}
        
        for market_type, config in MarketConfig.MARKETS.items():
            score = 0
            
            # Market status (open markets get higher score)
            status = self.get_market_status(market_type)
            if "OPEN" in status:
                score += 50
            elif "24/7" in status:
                score += 60
            
            # Historical performance
            if market_type in self.market_performance:
                perf = self.market_performance[market_type]
                score += perf.get('win_rate', 0) * 30
                score += min(perf.get('total_trades', 0), 10) * 2  # Experience bonus
            
            # Market volatility preference (crypto gets bonus for high volatility tolerance)
            if market_type == MarketType.CRYPTO:
                score += 20
            elif market_type == MarketType.FOREX:
                score += 15
            
            # Time-based preferences
            hour = datetime.now().hour
            if market_type == MarketType.INDIAN and 9 <= hour <= 15:
                score += 25
            elif market_type == MarketType.US and (19 <= hour <= 23 or 0 <= hour <= 2):
                score += 25
            elif market_type == MarketType.EUROPEAN and 8 <= hour <= 16:
                score += 25
            
            market_scores[market_type] = score
        
        # Select best market
        best_market = max(market_scores, key=market_scores.get)
        best_score = market_scores[best_market]
        
        print(f"🎯 SELECTED: {MarketConfig.MARKETS[best_market]['name']}")
        print(f"📊 Score: {best_score}/100")
        print(f"💱 Currency: {MarketConfig.MARKETS[best_market]['currency']}")
        print(f"🕐 Status: {self.get_market_status(best_market)}")
        
        return best_market
    
    def show_market_performance(self):
        """Show performance across all markets"""
        print("\n📊 MARKET PERFORMANCE SUMMARY")
        print("="*50)
        
        if not self.market_performance:
            print("⚠️  No trading history available yet")
            input("Press Enter to continue...")
            return
        
        for market_type, perf in self.market_performance.items():
            config = MarketConfig.MARKETS[market_type]
            print(f"\n🌍 {config['name']}:")
            print(f"   Total Trades: {perf.get('total_trades', 0)}")
            print(f"   Win Rate: {perf.get('win_rate', 0):.1%}")
            print(f"   Total P&L: {perf.get('total_pnl', 0):.2f} {config['currency']}")
            print(f"   Best Trade: {perf.get('best_trade', 0):.2f} {config['currency']}")
        
        input("\nPress Enter to continue...")
    
    def set_market(self, market_type: MarketType):
        """Set the current trading market"""
        self.current_market = market_type
        self.market_config = MarketConfig.MARKETS[market_type]
        
        logger.info(f"MARKET SELECTED: {self.market_config['name']}")
        logger.info(f"Currency: {self.market_config['currency']}")
        logger.info(f"Symbols: {len(self.market_config['symbols'])} available")
        logger.info(f"Confidence Threshold: {self.market_config['confidence_threshold']:.0%}")
    
    async def get_market_data(self, symbol: str) -> Optional[Dict]:
        """Get market data for any market"""
        try:
            if not self.market_config:
                return None
            
            # Construct symbol for Yahoo Finance
            if self.current_market == MarketType.INDIAN:
                yahoo_symbol = f"{symbol}.NS"
            elif self.current_market == MarketType.CRYPTO:
                yahoo_symbol = f"{symbol}-USD"
            elif self.current_market == MarketType.FOREX:
                yahoo_symbol = f"{symbol}=X"
            else:
                yahoo_symbol = symbol
            
            logger.info(f"Getting data for {symbol} ({yahoo_symbol})")
            
            # Get data from Yahoo Finance
            ticker = yf.Ticker(yahoo_symbol)
            hist = ticker.history(period="1d", interval="5m")
            
            if not hist.empty:
                current_price = hist['Close'].iloc[-1]
                prev_close = hist['Close'].iloc[0] if len(hist) > 1 else current_price
                change = current_price - prev_close
                change_pct = (change / prev_close) * 100 if prev_close > 0 else 0
                volume = hist['Volume'].iloc[-1]
                
                return {
                    'symbol': symbol,
                    'yahoo_symbol': yahoo_symbol,
                    'price': float(current_price),
                    'change': float(change),
                    'change_pct': float(change_pct),
                    'volume': int(volume),
                    'currency': self.market_config['currency'],
                    'market': self.current_market.value,
                    'timestamp': datetime.now(),
                    'data_source': 'Yahoo_Finance'
                }
            else:
                return self.get_fallback_data(symbol)
                
        except Exception as e:
            logger.error(f"Data error for {symbol}: {e}")
            return self.get_fallback_data(symbol)
    
    def get_fallback_data(self, symbol: str) -> Dict:
        """Generate fallback data"""
        base_prices = {
            # Indian stocks
            'RELIANCE': 2485, 'TCS': 3245, 'HDFCBANK': 1598,
            # US stocks  
            'AAPL': 180, 'GOOGL': 140, 'MSFT': 350, 'AMZN': 140, 'TSLA': 250,
            # European stocks
            'ASML.AS': 650, 'SAP.DE': 120, 'NESN.SW': 110,
            # Crypto
            'BTC': 45000, 'ETH': 3000, 'BNB': 300,
            # Forex
            'EURUSD': 1.08, 'GBPUSD': 1.25, 'USDJPY': 150
        }
        
        base_price = base_prices.get(symbol, 100)
        change_pct = np.random.uniform(-3, 3)
        current_price = base_price * (1 + change_pct / 100)
        
        return {
            'symbol': symbol,
            'price': current_price,
            'change': current_price - base_price,
            'change_pct': change_pct,
            'volume': np.random.randint(100000, 1000000),
            'currency': self.market_config['currency'],
            'market': self.current_market.value,
            'timestamp': datetime.now(),
            'data_source': 'Fallback'
        }
    
    async def analyze_symbol(self, symbol: str) -> Optional[Dict]:
        """Universal analysis for any market symbol"""
        try:
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                return None
            
            # Market-specific analysis adjustments
            risk_multiplier = self.market_config['risk_multiplier']
            confidence_threshold = self.market_config['confidence_threshold']
            
            # Basic technical analysis (simplified)
            change_pct = data['change_pct']
            
            # Calculate scores
            technical_score = 0
            if abs(change_pct) > 2:
                technical_score += np.sign(change_pct) * 1.0 * risk_multiplier
            
            sentiment_score = np.random.uniform(-0.5, 0.5)
            momentum_score = np.sign(change_pct) * min(abs(change_pct) / 3, 1.0)
            
            # Calculate confidence
            raw_confidence = (abs(technical_score) + abs(sentiment_score) + abs(momentum_score)) / 3
            
            # Market-specific confidence adjustment
            if self.current_market == MarketType.CRYPTO:
                raw_confidence *= 1.2  # Crypto gets confidence boost
            elif self.current_market == MarketType.FOREX:
                raw_confidence *= 0.9  # Forex is more conservative
            
            final_confidence = min(1.0, raw_confidence)
            
            # Check threshold
            if final_confidence < confidence_threshold:
                logger.info(f"{symbol}: Confidence {final_confidence:.2f} < {confidence_threshold:.2f}")
                return None
            
            # Determine action
            combined_score = technical_score + sentiment_score + momentum_score
            
            if combined_score > 1.0:
                action = TradingAction.STRONG_BUY
            elif combined_score > 0.5:
                action = TradingAction.BUY
            elif combined_score < -1.0:
                action = TradingAction.STRONG_SELL
            elif combined_score < -0.5:
                action = TradingAction.SELL
            else:
                return None
            
            # Calculate position size (market-adjusted)
            base_position = self.capital * 0.15
            position_size = base_position * final_confidence * risk_multiplier
            
            # Risk management
            current_price = data['price']
            if action.value > 0:
                stop_loss = current_price * (1 - 0.02 * risk_multiplier)
                target = current_price * (1 + 0.06 * risk_multiplier)
            else:
                stop_loss = current_price * (1 + 0.02 * risk_multiplier)
                target = current_price * (1 - 0.06 * risk_multiplier)
            
            signal = {
                'symbol': symbol,
                'market': self.current_market.value,
                'action': action,
                'confidence': final_confidence,
                'entry_price': current_price,
                'stop_loss': stop_loss,
                'target': target,
                'position_size': position_size,
                'currency': data['currency'],
                'market_data': data,
                'scores': {
                    'technical': technical_score,
                    'sentiment': sentiment_score,
                    'momentum': momentum_score,
                    'combined': combined_score
                },
                'timestamp': datetime.now()
            }
            
            logger.info(f"SIGNAL: {symbol} {action.name} (confidence: {final_confidence:.1%})")
            return signal
            
        except Exception as e:
            logger.error(f"Analysis error for {symbol}: {e}")
            return None
    
    async def trade_market(self, market_type: MarketType, cycles: int = 2):
        """Trade a specific market"""
        self.set_market(market_type)
        
        logger.info(f"TRADING {self.market_config['name']}")
        logger.info(f"Symbols: {self.market_config['symbols']}")
        
        signals_generated = 0
        
        for cycle in range(cycles):
            logger.info(f"CYCLE {cycle + 1}/{cycles}")
            
            for symbol in self.market_config['symbols']:
                if symbol in self.positions:
                    continue
                
                signal = await self.analyze_symbol(symbol)
                if signal:
                    # Execute trade
                    success = await self.execute_trade(signal)
                    if success:
                        signals_generated += 1
                
                await asyncio.sleep(1)  # Rate limiting
            
            if cycle < cycles - 1:
                logger.info("Waiting before next cycle...")
                await asyncio.sleep(10)
        
        logger.info(f"Market trading complete: {signals_generated} signals")
        return signals_generated
    
    async def execute_trade(self, signal: Dict) -> bool:
        """Execute trade (paper or live)"""
        try:
            symbol = signal['symbol']
            action = signal['action']
            
            if self.paper_trading:
                # Paper trading
                self.positions[f"{symbol}_{signal['market']}"] = {
                    'signal': signal,
                    'entry_time': datetime.now(),
                    'status': 'OPEN'
                }
                
                trade_record = {
                    'timestamp': datetime.now().isoformat(),
                    'symbol': symbol,
                    'market': signal['market'],
                    'action': action.name,
                    'entry_price': signal['entry_price'],
                    'confidence': signal['confidence'],
                    'position_size': signal['position_size'],
                    'currency': signal['currency'],
                    'mode': 'PAPER'
                }
                self.trades.append(trade_record)
                
                logger.info(f"PAPER TRADE: {symbol} {action.name} at {signal['currency']} {signal['entry_price']:.2f}")
                return True
            else:
                # Live trading would go here
                logger.info(f"LIVE TRADE: {symbol} {action.name}")
                return True
                
        except Exception as e:
            logger.error(f"Trade execution error: {e}")
            return False
    
    async def trade_all_markets(self):
        """Trade all available markets"""
        logger.info("TRADING ALL MARKETS")
        
        total_signals = 0
        
        for market_type in MarketConfig.MARKETS.keys():
            status = self.get_market_status(market_type)
            if "OPEN" in status or "24/7" in status:
                logger.info(f"Trading {MarketConfig.MARKETS[market_type]['name']}")
                signals = await self.trade_market(market_type, cycles=1)
                total_signals += signals
            else:
                logger.info(f"Skipping {MarketConfig.MARKETS[market_type]['name']} - Market closed")
        
        logger.info(f"ALL MARKETS COMPLETE: {total_signals} total signals")
        return total_signals
    
    def save_state(self):
        """Save bot state"""
        try:
            state = {
                'positions': self.positions,
                'trades': self.trades[-100:],
                'market_performance': self.market_performance,
                'capital': self.capital,
                'current_market': self.current_market.value if self.current_market else None,
                'timestamp': datetime.now().isoformat()
            }
            
            with open('logs/universal_bot_state.json', 'w') as f:
                json.dump(state, f, indent=2, default=str)
                
            logger.info("Universal bot state saved")
            
        except Exception as e:
            logger.error(f"State save error: {e}")
    
    async def run(self):
        """Main bot execution"""
        print("🌍 UNIVERSAL TRADING BOT")
        print("Trade any market from one interface!")
        
        while True:
            market_choice = self.select_market()
            
            if market_choice is None:
                print("👋 Goodbye!")
                break
            elif market_choice == "ALL_MARKETS":
                await self.trade_all_markets()
            else:
                await self.trade_market(market_choice, cycles=2)
            
            self.save_state()
            
            continue_trading = input("\nContinue trading? (y/n): ").strip().lower()
            if continue_trading != 'y':
                break

async def main():
    """Main function"""
    os.makedirs('logs', exist_ok=True)
    
    bot = UniversalTradingBot(paper_trading=True)
    
    try:
        await bot.run()
    except KeyboardInterrupt:
        print("\nBot stopped by user")
    except Exception as e:
        print(f"Bot error: {e}")
        logger.error(f"Bot error: {e}")
    finally:
        bot.save_state()

if __name__ == "__main__":
    asyncio.run(main())
