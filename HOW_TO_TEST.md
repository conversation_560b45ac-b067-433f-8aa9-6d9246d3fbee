# 🚀 How to Test Your Angel One Trading Bot

## **Quick Start Testing (30 minutes)**

### 1. **Setup Validation** (5 minutes)
```bash
# Install and verify setup
pip install -r requirements.txt
python test_setup.py
```
**Expected:** ✅ All tests passed!

### 2. **Unit Tests** (10 minutes)
```bash
# Test individual components
python scripts/run_tests.py
```
**Expected:** ✅ All unit tests pass

### 3. **Paper Trading Demo** (15 minutes)
```bash
# Run simulated trading demo
python scripts/paper_trading_demo.py
```
**Expected:** ✅ De<PERSON> completes with trading activity

---

## **Complete Testing Workflow (2-6 weeks)**

### **Week 1: Development Testing**

#### Day 1: Setup & Unit Tests
```bash
# 1. Setup environment
python scripts/setup_bot.py

# 2. Run all tests
python scripts/run_tests.py

# 3. Verify setup
python test_setup.py
```

#### Day 2-3: Backtesting
```bash
# Run backtests on multiple symbols
python tests/test_backtest.py

# Analyze results
python -c "
from tests.test_backtest import BacktestEngine
engine = BacktestEngine()
for symbol in ['RELIANCE', 'TCS', 'HDFCBANK']:
    results = engine.run_backtest(symbol, days=30)
    print(f'{symbol}: Return {results[\"total_return\"]:.2f}%, Win Rate {results[\"win_rate\"]:.1f}%')
"
```

**Success Criteria:**
- ✅ All tests pass
- ✅ Backtest returns > 0%
- ✅ Win rate > 50%
- ✅ Max drawdown < 15%

### **Week 2-3: Paper Trading**

#### Start Paper Trading
```bash
# Ensure paper trading mode
echo "PAPER_TRADING=True" > .env

# Start the bot
python main.py
```

#### Monitor Daily
```bash
# Check logs
tail -f logs/trading_bot.log

# Check trades
tail -f logs/trades.log

# Check performance
grep "PERFORMANCE_METRICS" logs/trading_bot.log | tail -5
```

**Success Criteria:**
- ✅ Bot runs without crashes
- ✅ Generates and executes trades
- ✅ Risk management works
- ✅ Daily P&L reasonable

### **Week 4-5: Small Live Testing**

#### Setup Small Live Testing
```bash
# Switch to live trading with small capital
cat > .env << EOF
PAPER_TRADING=False
INITIAL_CAPITAL=10000
MAX_DAILY_LOSS=500
MAX_POSITIONS=2
EOF

# Start with minimal capital
python main.py
```

#### Monitor Closely
- Check every trade manually
- Monitor P&L hourly
- Verify risk limits work
- Watch for any errors

**Success Criteria:**
- ✅ No major losses
- ✅ System stability
- ✅ Risk management effective

### **Week 6+: Full Deployment**

#### Scale Up Gradually
```bash
# Increase capital gradually
# Week 6: ₹25,000
# Week 7: ₹50,000  
# Week 8: ₹100,000 (full)
```

---

## **Testing Commands Cheat Sheet**

### **Essential Commands**
```bash
# Complete test suite
python scripts/run_tests.py

# Setup validation
python test_setup.py

# Paper trading demo
python scripts/paper_trading_demo.py

# Start bot (paper trading)
PAPER_TRADING=True python main.py

# Start bot (live trading)
PAPER_TRADING=False python main.py
```

### **Monitoring Commands**
```bash
# Real-time logs
tail -f logs/trading_bot.log

# Trade logs
tail -f logs/trades.log

# Error logs
tail -f logs/errors.log

# Performance check
grep "daily_pnl" logs/trading_bot.log | tail -10
```

### **Testing Specific Components**
```bash
# Test risk manager only
python -m pytest tests/test_risk_manager.py -v

# Test technical analysis only
python -m pytest tests/test_technical_analysis.py -v

# Test integration
python -m pytest tests/test_integration.py -v

# Run backtest for specific symbol
python -c "
from tests.test_backtest import BacktestEngine
engine = BacktestEngine()
results = engine.run_backtest('RELIANCE', days=20)
print(f'Return: {results[\"total_return\"]:.2f}%')
"
```

---

## **What Each Test Does**

### **Unit Tests** (`tests/test_*.py`)
- **Risk Manager**: Position sizing, stop losses, limits
- **Technical Analysis**: Indicators, signals, confluence
- **Integration**: Component interactions

### **Backtesting** (`tests/test_backtest.py`)
- Tests strategies on historical data
- Calculates performance metrics
- Validates strategy logic

### **Paper Trading Demo** (`scripts/paper_trading_demo.py`)
- Simulates live trading with fake data
- Tests complete trading pipeline
- Shows how bot behaves in real-time

### **Setup Validation** (`test_setup.py`)
- Verifies all dependencies installed
- Checks configuration
- Tests API connectivity

---

## **Success Indicators**

### **✅ Good Signs**
```
✅ All tests pass
✅ Backtest returns positive
✅ Paper trading profitable
✅ No system crashes
✅ Risk limits respected
✅ Reasonable trade frequency
✅ Stop losses working
```

### **❌ Warning Signs**
```
❌ Tests failing
❌ Negative backtest returns
❌ Excessive trading (>20 trades/day)
❌ Large drawdowns (>15%)
❌ System crashes/errors
❌ Risk limits not working
❌ Stop losses not triggering
```

---

## **Common Issues & Quick Fixes**

### **Issue: Tests Fail**
```bash
# Fix: Reinstall dependencies
pip install -r requirements.txt --force-reinstall
```

### **Issue: TA-Lib Import Error**
```bash
# Fix: Install TA-Lib properly
# Windows: Download wheel from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib
# Linux: sudo apt-get install ta-lib
# macOS: brew install ta-lib
```

### **Issue: Angel One Login Fails**
```bash
# Fix: Check credentials
cat .env | grep ANGEL
# Verify API key, client ID, password, TOTP secret
```

### **Issue: No Trades Generated**
```bash
# Fix: Check market hours and data
python -c "
from config import is_market_hours, is_trading_day
print('Trading Day:', is_trading_day())
print('Market Hours:', is_market_hours())
"
```

---

## **Testing Timeline**

### **Minimum Testing (2 weeks)**
- Day 1: Setup + Unit Tests
- Day 2-3: Backtesting
- Day 4-14: Paper Trading
- Day 15+: Small Live Testing

### **Recommended Testing (4-6 weeks)**
- Week 1: Development Testing
- Week 2-3: Paper Trading
- Week 4-5: Small Live Testing
- Week 6+: Full Deployment

### **Conservative Testing (8+ weeks)**
- Week 1-2: Development + Backtesting
- Week 3-6: Extended Paper Trading
- Week 7-8: Small Live Testing
- Week 9+: Gradual Full Deployment

---

## **Final Pre-Live Checklist**

Before using real money:

### **Technical Checklist**
- [ ] All tests pass (`python scripts/run_tests.py`)
- [ ] Setup validated (`python test_setup.py`)
- [ ] Backtesting positive results
- [ ] Paper trading successful 2+ weeks
- [ ] No critical errors in logs

### **Strategy Checklist**
- [ ] Win rate > 50%
- [ ] Reasonable trade frequency (5-15 trades/day)
- [ ] Stop losses working correctly
- [ ] Risk management effective
- [ ] Consistent daily performance

### **Risk Checklist**
- [ ] Daily loss limits tested and working
- [ ] Position limits enforced
- [ ] Emergency stop functionality tested
- [ ] Auto square-off before market close
- [ ] Capital allocation appropriate

### **Operational Checklist**
- [ ] Angel One account funded
- [ ] API permissions verified
- [ ] Monitoring setup (logs, alerts)
- [ ] Backup plans in place
- [ ] Understanding of all bot functions

**Only proceed when ALL boxes are checked! ✅**

---

## **Emergency Procedures**

### **If Bot Loses Money**
1. **Stop immediately**: `Ctrl+C` or kill process
2. **Check logs**: Look for errors in `logs/errors.log`
3. **Review trades**: Analyze `logs/trades.log`
4. **Switch to paper trading**: Set `PAPER_TRADING=True`
5. **Investigate and fix issues**

### **If Bot Crashes**
1. **Check error logs**: `tail logs/errors.log`
2. **Restart in paper mode**: `PAPER_TRADING=True python main.py`
3. **Run diagnostics**: `python test_setup.py`
4. **Fix issues before resuming live trading**

### **If Unexpected Behavior**
1. **Monitor closely**: Watch logs in real-time
2. **Reduce position sizes**: Lower `INITIAL_CAPITAL`
3. **Increase stop losses**: Tighter risk management
4. **Return to paper trading** if needed

---

## **Remember: Testing Saves Money! 💰**

**The time spent testing is an investment in your trading success. Never skip testing phases, especially when real money is involved.**

**Start small, test thoroughly, scale gradually! 🚀**
