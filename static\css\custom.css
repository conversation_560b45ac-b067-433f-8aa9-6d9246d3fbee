/* Ultimate Trading Bot - Custom Styles */

/* Enhanced animations and transitions */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-50px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(50px); }
    to { opacity: 1; transform: translateX(0); }
}

/* Enhanced card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced metric cards */
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: scale(1.05);
}

.metric-card.bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.metric-card.bg-info {
    background: linear-gradient(135deg, #3498db 0%, #85c1e9 100%);
}

.metric-card.bg-warning {
    background: linear-gradient(135deg, #f39c12 0%, #f7dc6f 100%);
}

.metric-card.bg-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #f1948a 100%);
}

.metric-card.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #adb5bd 100%);
}

/* Enhanced buttons */
.btn-custom {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-custom:hover::before {
    left: 100%;
}

.btn-start {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
}

.btn-start:hover {
    background: linear-gradient(135deg, #20c997 0%, #28a745 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-stop {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
    color: white;
}

.btn-stop:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Enhanced sidebar */
.sidebar {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    min-height: 100vh;
    padding-top: 20px;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: #bdc3c7;
    padding: 15px 25px;
    margin: 5px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Enhanced tables */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.02);
}

/* Enhanced progress bars */
.progress {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-bar {
    transition: width 0.6s ease;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

/* Enhanced alerts */
.alert {
    border-radius: 15px;
    border: none;
    padding: 20px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: rgba(0, 0, 0, 0.2);
}

/* Enhanced badges */
.badge {
    border-radius: 20px;
    padding: 8px 15px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Real-time indicators */
.real-time-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: linear-gradient(45deg, #28a745, #20c997);
    border-radius: 50%;
    margin-right: 8px;
    animation: pulse 2s infinite;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

@keyframes pulse {
    0% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
    50% { 
        opacity: 0.7; 
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(40, 167, 69, 0.8);
    }
    100% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    }
}

/* Enhanced trade items */
.trade-item {
    border-left: 4px solid #007bff;
    padding: 20px;
    margin-bottom: 15px;
    background: white;
    border-radius: 0 15px 15px 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.trade-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.1), transparent);
    transition: width 0.3s ease;
}

.trade-item:hover::before {
    width: 100%;
}

.trade-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.trade-buy {
    border-left-color: #28a745;
}

.trade-sell {
    border-left-color: #dc3545;
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
        padding: 15px;
    }
    
    .metric-card {
        margin-bottom: 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2c3e50;
        color: #ecf0f1;
    }
    
    .table {
        background-color: #34495e;
        color: #ecf0f1;
    }
    
    .table tbody tr:hover {
        background-color: #3c4f66;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
