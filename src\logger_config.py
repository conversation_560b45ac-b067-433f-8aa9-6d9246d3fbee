"""
Logging configuration for the trading bot
"""
import logging
import logging.handlers
import os
from datetime import datetime
import structlog
from pythonjsonlogger import jsonlogger

from config import env_config, trading_config

def setup_logging():
    """Setup comprehensive logging for the trading bot"""
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Get log level from environment
    log_level = getattr(logging, env_config.get_log_level().upper(), logging.INFO)
    
    # Create formatters
    json_formatter = jsonlogger.JsonFormatter(
        '%(asctime)s %(name)s %(levelname)s %(message)s'
    )
    
    console_formatter = logging.Formatter(
        '%(asctime)s [%(levelname)8s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(console_formatter)
    
    # File handler for general logs
    file_handler = logging.handlers.RotatingFileHandler(
        'logs/trading_bot.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(json_formatter)
    
    # File handler for trade logs
    trade_handler = logging.handlers.RotatingFileHandler(
        'logs/trades.log',
        maxBytes=5*1024*1024,   # 5MB
        backupCount=10
    )
    trade_handler.setLevel(logging.INFO)
    trade_handler.setFormatter(json_formatter)
    
    # File handler for error logs
    error_handler = logging.handlers.RotatingFileHandler(
        'logs/errors.log',
        maxBytes=5*1024*1024,   # 5MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(json_formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    
    # Configure trade logger
    trade_logger = logging.getLogger('trades')
    trade_logger.addHandler(trade_handler)
    trade_logger.propagate = False
    
    # Configure specific loggers
    loggers = [
        'trading_bot',
        'angel_api',
        'risk_manager', 
        'technical_analysis'
    ]
    
    for logger_name in loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level)
    
    # Suppress noisy third-party loggers
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('websocket').setLevel(logging.WARNING)
    
    # Setup structured logging
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    logging.info("Logging system initialized")

class TradingLogger:
    """Specialized logger for trading events"""
    
    def __init__(self):
        self.trade_logger = logging.getLogger('trades')
        self.main_logger = logging.getLogger('trading_bot')
    
    def log_trade_signal(self, signal_data: dict):
        """Log trading signal generation"""
        self.trade_logger.info(
            "SIGNAL_GENERATED",
            extra={
                'event_type': 'signal',
                'symbol': signal_data.get('symbol'),
                'signal_type': signal_data.get('signal_type'),
                'strategy': signal_data.get('strategy'),
                'confidence': signal_data.get('confidence'),
                'entry_price': signal_data.get('entry_price'),
                'stop_loss': signal_data.get('stop_loss'),
                'target': signal_data.get('target')
            }
        )
    
    def log_trade_execution(self, trade_data: dict):
        """Log trade execution"""
        self.trade_logger.info(
            "TRADE_EXECUTED",
            extra={
                'event_type': 'execution',
                'symbol': trade_data.get('symbol'),
                'side': trade_data.get('side'),
                'quantity': trade_data.get('quantity'),
                'price': trade_data.get('price'),
                'order_id': trade_data.get('order_id'),
                'timestamp': datetime.now(trading_config.TIMEZONE).isoformat()
            }
        )
    
    def log_trade_exit(self, exit_data: dict):
        """Log trade exit"""
        self.trade_logger.info(
            "TRADE_EXITED",
            extra={
                'event_type': 'exit',
                'symbol': exit_data.get('symbol'),
                'exit_price': exit_data.get('exit_price'),
                'realized_pnl': exit_data.get('realized_pnl'),
                'exit_reason': exit_data.get('exit_reason'),
                'hold_duration': exit_data.get('hold_duration'),
                'timestamp': datetime.now(trading_config.TIMEZONE).isoformat()
            }
        )
    
    def log_risk_event(self, risk_data: dict):
        """Log risk management events"""
        self.main_logger.warning(
            "RISK_EVENT",
            extra={
                'event_type': 'risk',
                'risk_type': risk_data.get('risk_type'),
                'symbol': risk_data.get('symbol'),
                'current_value': risk_data.get('current_value'),
                'limit_value': risk_data.get('limit_value'),
                'action_taken': risk_data.get('action_taken')
            }
        )
    
    def log_system_event(self, event_data: dict):
        """Log system events"""
        self.main_logger.info(
            "SYSTEM_EVENT",
            extra={
                'event_type': 'system',
                'event_name': event_data.get('event_name'),
                'status': event_data.get('status'),
                'details': event_data.get('details')
            }
        )
    
    def log_performance_metrics(self, metrics: dict):
        """Log performance metrics"""
        self.main_logger.info(
            "PERFORMANCE_METRICS",
            extra={
                'event_type': 'performance',
                'daily_pnl': metrics.get('daily_pnl'),
                'total_pnl': metrics.get('total_pnl'),
                'available_capital': metrics.get('available_capital'),
                'current_positions': metrics.get('current_positions'),
                'max_drawdown': metrics.get('max_drawdown'),
                'timestamp': datetime.now(trading_config.TIMEZONE).isoformat()
            }
        )
    
    def log_api_call(self, api_data: dict):
        """Log API calls"""
        self.main_logger.debug(
            "API_CALL",
            extra={
                'event_type': 'api',
                'endpoint': api_data.get('endpoint'),
                'method': api_data.get('method'),
                'status_code': api_data.get('status_code'),
                'response_time': api_data.get('response_time'),
                'error': api_data.get('error')
            }
        )
    
    def log_error(self, error_data: dict):
        """Log errors with context"""
        self.main_logger.error(
            "ERROR_OCCURRED",
            extra={
                'event_type': 'error',
                'error_type': error_data.get('error_type'),
                'error_message': error_data.get('error_message'),
                'function': error_data.get('function'),
                'symbol': error_data.get('symbol'),
                'stack_trace': error_data.get('stack_trace')
            }
        )

# Global trading logger instance
trading_logger = TradingLogger()
