@echo off
REM Windows batch script to run the Angel One Trading Bot

echo ========================================
echo Angel One Autonomous Trading Bot
echo ========================================

REM Check if virtual environment exists
if not exist "venv\" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate

REM Check if requirements are installed
echo Checking dependencies...
pip install -r requirements.txt

REM Check if .env file exists
if not exist ".env" (
    echo .env file not found. Running setup...
    python scripts\setup_bot.py
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs\" mkdir logs

REM Start the trading bot
echo Starting trading bot...
python main.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo Bot stopped with error. Check logs for details.
    pause
)

deactivate
