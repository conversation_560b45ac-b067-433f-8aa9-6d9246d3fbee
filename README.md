# Angel One Autonomous Intraday Trading Bot

An autonomous intraday trading bot specifically designed for Indian stock markets using Angel One SmartAPI. The bot implements multiple technical analysis strategies with comprehensive risk management for safe and profitable trading.

## 🚀 Features

### Core Trading Capabilities
- **Autonomous Trading**: Fully automated intraday trading without human intervention
- **Multiple Strategies**: Opening Range Breakout (ORB), VWAP, Momentum, and Mean Reversion
- **Risk Management**: Position sizing, stop-losses, daily loss limits, and portfolio protection
- **Real-time Analysis**: Live market data processing with technical indicators
- **Paper Trading**: Safe testing mode before live trading

### Indian Market Specific
- **Angel One Integration**: Native support for Angel One SmartAPI
- **Intraday Focus**: All positions automatically squared off before market close
- **NSE/BSE Support**: Trade on National Stock Exchange and Bombay Stock Exchange
- **Market Hours**: Respects Indian market timings (9:15 AM - 3:30 PM IST)
- **Holiday Calendar**: Built-in Indian market holiday calendar

### Technical Analysis
- **Technical Indicators**: RSI, MACD, EMA, SMA, Bollinger Bands, VWAP, ATR
- **Signal Confluence**: Multiple indicators must agree before trade execution
- **Dynamic Stop-Loss**: ATR-based and trailing stop-loss mechanisms
- **Volume Confirmation**: Volume analysis for signal validation

### Risk Management
- **Position Sizing**: Automatic calculation based on risk tolerance
- **Daily Limits**: Maximum daily loss and position count limits
- **Portfolio Protection**: Correlation analysis and concentration limits
- **Emergency Stop**: Automatic shutdown on excessive losses

## 📋 Prerequisites

### System Requirements
- Python 3.8 or higher
- Windows/Linux/macOS
- Stable internet connection
- Minimum 4GB RAM

### Angel One Account
- Active Angel One trading account
- SmartAPI subscription
- API credentials (API Key, Client ID, Password, TOTP Secret)

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone https://github.com/yourusername/angel-one-trading-bot.git
cd angel-one-trading-bot
```

### 2. Create Virtual Environment
```bash
python -m venv venv

# On Windows
venv\Scripts\activate

# On Linux/macOS
source venv/bin/activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install TA-Lib (Technical Analysis Library)
```bash
# On Windows (download from https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib)
pip install TA_Lib-0.4.25-cp39-cp39-win_amd64.whl

# On Linux
sudo apt-get install ta-lib
pip install TA-Lib

# On macOS
brew install ta-lib
pip install TA-Lib
```

## ⚙️ Configuration

### 1. Environment Setup
Copy the example environment file and configure your credentials:
```bash
cp .env.example .env
```

### 2. Configure Angel One Credentials
Edit the `.env` file with your Angel One credentials:
```env
# Angel One SmartAPI Credentials
ANGEL_API_KEY=your_api_key_here
ANGEL_CLIENT_ID=your_client_id_here
ANGEL_PASSWORD=your_password_here
ANGEL_TOTP_SECRET=your_totp_secret_here

# Trading Configuration
PAPER_TRADING=True
INITIAL_CAPITAL=100000
MAX_DAILY_LOSS=5000
```

### 3. Get Angel One Credentials

1. **Login to Angel One**: Visit [Angel One SmartAPI](https://smartapi.angelbroking.com/)
2. **Generate API Key**: Go to API section and generate your API credentials
3. **Setup 2FA**: Configure TOTP (Time-based One-Time Password) for security
4. **Note Credentials**: Save API Key, Client ID, Password, and TOTP Secret

### 4. Configure Trading Parameters
Edit `config.py` to customize trading parameters:
```python
# Risk Management
INITIAL_CAPITAL = 100000.0  # ₹1 Lakh
MAX_RISK_PER_TRADE = 0.02   # 2% per trade
MAX_DAILY_LOSS = 0.05       # 5% daily loss limit
MAX_POSITIONS = 4           # Maximum simultaneous positions

# Technical Analysis
RSI_PERIOD = 14
EMA_FAST = 9
EMA_SLOW = 21
```

## 🚀 Usage

### Start the Trading Bot
```bash
python main.py
```

### Paper Trading (Recommended First)
Start with paper trading to test strategies:
```bash
# Set in .env file
PAPER_TRADING=True
```

### Live Trading
After successful paper trading, switch to live mode:
```bash
# Set in .env file  
PAPER_TRADING=False
```

### Monitor Logs
```bash
# View real-time logs
tail -f logs/trading_bot.log

# View trade logs
tail -f logs/trades.log

# View error logs
tail -f logs/errors.log
```

## 📊 Trading Strategies

### 1. Opening Range Breakout (ORB)
- Identifies the high/low range in the first 15 minutes
- Enters long on breakout above range high
- Enters short on breakdown below range low
- Risk-reward ratio: 1:2

### 2. VWAP Strategy
- Buys when price is above VWAP with volume confirmation
- Sells when price is below VWAP with volume confirmation
- Uses ATR for stop-loss and target calculation

### 3. Momentum Strategy
- Combines RSI, MACD, and EMA signals
- Requires confluence of at least 3 indicators
- Follows strong trending moves

### 4. Mean Reversion
- Uses Bollinger Bands and RSI for oversold/overbought conditions
- Targets mean reversion to middle Bollinger Band
- Conservative risk-reward ratio

## 🛡️ Risk Management

### Position Sizing
- Maximum 2% risk per trade
- Position size calculated based on stop-loss distance
- Minimum ₹5,000 and maximum ₹25,000 per position

### Stop-Loss Mechanisms
- Static stop-loss based on technical levels
- Trailing stop-loss for profit protection
- ATR-based dynamic stop-loss

### Portfolio Limits
- Maximum 4 simultaneous positions
- Daily loss limit of 5% of capital
- Automatic square-off before market close

### Emergency Controls
- Circuit breakers for unusual market conditions
- Manual override capabilities
- Automatic shutdown on excessive losses

## 📈 Performance Monitoring

### Real-time Metrics
- Live P&L tracking
- Position monitoring
- Risk metrics dashboard
- Trade execution logs

### Daily Reports
- End-of-day performance summary
- Trade analysis and statistics
- Risk metrics and drawdown analysis
- Strategy performance breakdown

### Log Files
- `trading_bot.log`: General bot operations
- `trades.log`: All trade executions and signals
- `errors.log`: Error tracking and debugging

## 🔧 Customization

### Adding New Strategies
1. Create strategy class in `src/strategies/`
2. Implement signal generation logic
3. Add to strategy configuration
4. Test in paper trading mode

### Modifying Risk Parameters
Edit `config.py` to adjust:
- Position sizing rules
- Stop-loss percentages
- Daily loss limits
- Maximum positions

### Custom Indicators
Add new technical indicators in `src/technical_analysis.py`:
```python
def custom_indicator(self, df):
    # Your indicator logic here
    return indicator_values
```

## ⚠️ Important Disclaimers

### Risk Warning
- **High Risk**: Trading involves substantial risk of loss
- **No Guarantees**: Past performance does not guarantee future results
- **Capital Loss**: You may lose some or all of your invested capital
- **Market Risk**: Markets can be volatile and unpredictable

### Legal Compliance
- Ensure compliance with local trading regulations
- Understand tax implications of algorithmic trading
- Maintain proper records for regulatory requirements
- Use only with proper authorization from your broker

### Technical Risks
- **System Failures**: Technical issues may cause missed opportunities
- **Internet Connectivity**: Stable connection required for operation
- **API Limits**: Respect broker API rate limits and restrictions
- **Data Quality**: Ensure reliable market data feeds

## 🆘 Troubleshooting

### Common Issues

#### Login Failures
```bash
# Check credentials in .env file
# Verify TOTP secret is correct
# Ensure Angel One account is active
```

#### API Rate Limits
```bash
# Reduce trading frequency
# Check Angel One API documentation
# Implement proper rate limiting
```

#### Technical Indicator Errors
```bash
# Ensure TA-Lib is properly installed
# Check data quality and completeness
# Verify minimum data requirements
```

### Getting Help
1. Check log files for error details
2. Review configuration settings
3. Test in paper trading mode first
4. Contact Angel One support for API issues

## 📞 Support

### Documentation
- [Angel One SmartAPI Docs](https://smartapi.angelbroking.com/docs)
- [Technical Analysis Guide](docs/technical_analysis.md)
- [Risk Management Guide](docs/risk_management.md)

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Wiki for additional documentation

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Angel One for providing SmartAPI
- TA-Lib developers for technical analysis tools
- Python trading community for inspiration and guidance

---

**⚠️ IMPORTANT**: This software is for educational and research purposes. Always test thoroughly in paper trading mode before using real money. Trading involves substantial risk of loss.
