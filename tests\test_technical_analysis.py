"""
Unit tests for Technical Analysis
"""
import pytest
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from technical_analysis import TechnicalAnalyzer, TechnicalSignal
from config import trading_config

class TestTechnicalAnalyzer:
    """Test cases for Technical Analyzer"""
    
    def setup_method(self):
        """Setup for each test"""
        self.analyzer = TechnicalAnalyzer()
        self.sample_data = self.create_sample_data()
    
    def create_sample_data(self, periods=100):
        """Create sample OHLCV data for testing"""
        np.random.seed(42)  # For reproducible tests
        
        dates = pd.date_range('2024-01-01 09:15:00', periods=periods, freq='1min')
        
        # Generate realistic price data
        base_price = 1000
        price_changes = np.random.normal(0, 0.005, periods)  # 0.5% volatility
        
        prices = [base_price]
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # Ensure positive prices
        
        # Create OHLCV data
        df = pd.DataFrame({
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.002))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.002))) for p in prices],
            'close': prices,
            'volume': np.random.randint(10000, 100000, periods)
        }, index=dates)
        
        # Ensure high >= close >= low and high >= open >= low
        for i in range(len(df)):
            df.iloc[i, df.columns.get_loc('high')] = max(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['high'])
            df.iloc[i, df.columns.get_loc('low')] = min(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['low'])
        
        return df
    
    def test_indicator_calculation(self):
        """Test technical indicator calculations"""
        indicators = self.analyzer.calculate_indicators(self.sample_data)
        
        # Check that all expected indicators are present
        expected_indicators = [
            'ema_9', 'ema_21', 'sma_20', 'sma_50', 'rsi', 'macd', 
            'macd_signal', 'bb_upper', 'bb_lower', 'vwap', 'atr'
        ]
        
        for indicator in expected_indicators:
            assert indicator in indicators, f"Missing indicator: {indicator}"
            assert len(indicators[indicator]) == len(self.sample_data), f"Wrong length for {indicator}"
        
        # Check RSI bounds
        rsi = indicators['rsi']
        rsi_valid = rsi[~np.isnan(rsi)]
        assert all(0 <= val <= 100 for val in rsi_valid), "RSI values out of bounds"
        
        # Check that EMA 9 is more responsive than EMA 21
        ema_9 = indicators['ema_9']
        ema_21 = indicators['ema_21']
        
        # In trending data, shorter EMA should be more volatile
        ema_9_std = np.nanstd(np.diff(ema_9))
        ema_21_std = np.nanstd(np.diff(ema_21))
        assert ema_9_std >= ema_21_std, "EMA 9 should be more volatile than EMA 21"
    
    def test_vwap_calculation(self):
        """Test VWAP calculation"""
        vwap = self.analyzer._calculate_vwap(self.sample_data)
        
        assert len(vwap) == len(self.sample_data)
        assert all(val > 0 for val in vwap), "VWAP should be positive"
        
        # VWAP should be between min and max prices
        min_price = self.sample_data['low'].min()
        max_price = self.sample_data['high'].max()
        assert all(min_price <= val <= max_price for val in vwap), "VWAP out of price range"
    
    def test_orb_signal_generation(self):
        """Test Opening Range Breakout signal generation"""
        # Create data with clear breakout pattern
        orb_data = self.sample_data.copy()
        
        # Set up ORB range in first 15 minutes
        orb_data.iloc[:15, orb_data.columns.get_loc('high')] = 1010
        orb_data.iloc[:15, orb_data.columns.get_loc('low')] = 990
        
        # Create breakout
        current_price = 1015  # Above ORB high
        
        signal = self.analyzer.generate_orb_signal(orb_data, current_price)
        
        if signal:  # Signal might be None if range is too small/large
            assert signal.signal_type == 'BUY'
            assert signal.strategy == 'orb'
            assert signal.entry_price == current_price
            assert signal.stop_loss < current_price
            assert signal.target > current_price
    
    def test_vwap_signal_generation(self):
        """Test VWAP signal generation"""
        indicators = self.analyzer.calculate_indicators(self.sample_data)
        
        if 'vwap' in indicators and len(indicators['vwap']) > 0:
            current_vwap = indicators['vwap'][-1]
            
            # Test bullish signal (price above VWAP)
            current_price = current_vwap * 1.01  # 1% above VWAP
            
            signal = self.analyzer.generate_vwap_signal(
                self.sample_data, indicators, current_price
            )
            
            if signal:
                assert signal.signal_type == 'BUY'
                assert signal.strategy == 'vwap'
                assert signal.entry_price == current_price
    
    def test_momentum_signal_generation(self):
        """Test momentum signal generation"""
        indicators = self.analyzer.calculate_indicators(self.sample_data)
        
        if all(key in indicators for key in ['rsi', 'macd', 'macd_signal', 'ema_9', 'ema_21']):
            current_price = self.sample_data['close'].iloc[-1]
            
            signal = self.analyzer.generate_momentum_signal(
                self.sample_data, indicators, current_price
            )
            
            # Signal might be None if conditions not met
            if signal:
                assert signal.strategy == 'momentum'
                assert signal.signal_type in ['BUY', 'SELL']
                assert signal.confidence > 0
    
    def test_mean_reversion_signal_generation(self):
        """Test mean reversion signal generation"""
        indicators = self.analyzer.calculate_indicators(self.sample_data)
        
        if all(key in indicators for key in ['bb_upper', 'bb_lower', 'rsi']):
            # Test oversold condition
            bb_lower = indicators['bb_lower'][-1]
            current_price = bb_lower * 0.99  # Below lower Bollinger Band
            
            # Manually set RSI to oversold
            indicators['rsi'][-1] = 25  # Oversold
            
            signal = self.analyzer.generate_mean_reversion_signal(
                self.sample_data, indicators, current_price
            )
            
            if signal:
                assert signal.signal_type == 'BUY'
                assert signal.strategy == 'mean_reversion'
    
    def test_confluence_signal(self):
        """Test signal confluence logic"""
        # Create multiple signals
        signals = [
            TechnicalSignal(
                symbol="TEST",
                signal_type="BUY",
                strength=0.8,
                strategy="orb",
                indicators={},
                timestamp=pd.Timestamp.now(),
                entry_price=1000,
                stop_loss=980,
                target=1040,
                confidence=0.7
            ),
            TechnicalSignal(
                symbol="TEST",
                signal_type="BUY",
                strength=0.7,
                strategy="vwap",
                indicators={},
                timestamp=pd.Timestamp.now(),
                entry_price=1000,
                stop_loss=985,
                target=1035,
                confidence=0.6
            )
        ]
        
        confluence_signal = self.analyzer.get_confluence_signal(signals)
        
        assert confluence_signal is not None
        assert confluence_signal.signal_type == "BUY"
        assert confluence_signal.strategy == "confluence"
        assert confluence_signal.confidence > 0
    
    def test_insufficient_data(self):
        """Test behavior with insufficient data"""
        # Create very small dataset
        small_data = self.sample_data.head(10)
        
        indicators = self.analyzer.calculate_indicators(small_data)
        
        # Should return empty dict or handle gracefully
        assert isinstance(indicators, dict)
    
    def test_analyze_stock(self):
        """Test complete stock analysis"""
        signals = self.analyzer.analyze_stock(self.sample_data, "TESTSTOCK")
        
        assert isinstance(signals, list)
        
        # Check signal properties if any signals generated
        for signal in signals:
            assert isinstance(signal, TechnicalSignal)
            assert signal.symbol == "TESTSTOCK"
            assert signal.signal_type in ['BUY', 'SELL', 'HOLD']
            assert 0 <= signal.confidence <= 1
            assert 0 <= signal.strength <= 1
            assert signal.entry_price > 0
            assert signal.stop_loss > 0
            assert signal.target > 0
    
    def test_trend_strength_calculation(self):
        """Test trend strength calculation"""
        close_prices = self.sample_data['close'].values
        trend_strength = self.analyzer._calculate_trend_strength(close_prices)
        
        assert len(trend_strength) == len(close_prices)
        assert all(0 <= val <= 1 for val in trend_strength if not np.isnan(val))
    
    def test_support_resistance_calculation(self):
        """Test support and resistance calculation"""
        support, resistance = self.analyzer._calculate_support_resistance(self.sample_data)
        
        assert len(support) == len(self.sample_data)
        assert len(resistance) == len(self.sample_data)
        
        # Support should be <= Resistance
        valid_indices = ~(np.isnan(support) | np.isnan(resistance))
        assert all(support[valid_indices] <= resistance[valid_indices])

if __name__ == "__main__":
    pytest.main([__file__])
