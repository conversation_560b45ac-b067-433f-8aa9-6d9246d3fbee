"""
WebSocket client for real-time market data from Angel One SmartAPI
"""
import json
import logging
import threading
import time
import websocket
from typing import Dict, List, Callable, Optional
import ssl

from config import angel_config, trading_config

logger = logging.getLogger(__name__)

class AngelOneWebSocket:
    """WebSocket client for Angel One SmartAPI real-time data"""
    
    def __init__(self, auth_token: str, api_key: str, client_id: str, feed_token: str):
        self.auth_token = auth_token
        self.api_key = api_key
        self.client_id = client_id
        self.feed_token = feed_token
        
        self.ws = None
        self.is_connected = False
        self.subscribed_tokens = set()
        self.callbacks = {}
        
        # Threading
        self.ws_thread = None
        self.heartbeat_thread = None
        self.stop_event = threading.Event()
        
        # Data handlers
        self.on_tick_callback = None
        self.on_connect_callback = None
        self.on_disconnect_callback = None
        self.on_error_callback = None
        
    def set_on_tick(self, callback: Callable):
        """Set callback for tick data"""
        self.on_tick_callback = callback
    
    def set_on_connect(self, callback: Callable):
        """Set callback for connection"""
        self.on_connect_callback = callback
    
    def set_on_disconnect(self, callback: Callable):
        """Set callback for disconnection"""
        self.on_disconnect_callback = callback
    
    def set_on_error(self, callback: Callable):
        """Set callback for errors"""
        self.on_error_callback = callback
    
    def connect(self):
        """Connect to WebSocket"""
        try:
            # WebSocket URL
            ws_url = f"{angel_config.WS_URL}?jwtToken={self.auth_token}"
            
            # Create WebSocket connection
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start WebSocket in separate thread
            self.ws_thread = threading.Thread(
                target=self._run_websocket,
                daemon=True
            )
            self.ws_thread.start()
            
            # Wait for connection
            timeout = 10
            start_time = time.time()
            while not self.is_connected and time.time() - start_time < timeout:
                time.sleep(0.1)
            
            if self.is_connected:
                logger.info("WebSocket connected successfully")
                return True
            else:
                logger.error("WebSocket connection timeout")
                return False
                
        except Exception as e:
            logger.error(f"WebSocket connection error: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from WebSocket"""
        try:
            self.stop_event.set()
            self.is_connected = False
            
            if self.ws:
                self.ws.close()
            
            if self.ws_thread and self.ws_thread.is_alive():
                self.ws_thread.join(timeout=5)
            
            if self.heartbeat_thread and self.heartbeat_thread.is_alive():
                self.heartbeat_thread.join(timeout=5)
            
            logger.info("WebSocket disconnected")
            
        except Exception as e:
            logger.error(f"WebSocket disconnect error: {str(e)}")
    
    def subscribe(self, tokens: List[str], mode: str = "LTP"):
        """Subscribe to tokens for real-time data"""
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False
        
        try:
            # Prepare subscription message
            subscription_msg = {
                "correlationID": "abcde12345",
                "action": 1,  # Subscribe
                "params": {
                    "mode": mode,  # LTP, QUOTE, SNAP_QUOTE
                    "tokenList": [
                        {
                            "exchangeType": 1,  # NSE
                            "tokens": tokens
                        }
                    ]
                }
            }
            
            # Send subscription
            self.ws.send(json.dumps(subscription_msg))
            
            # Track subscribed tokens
            self.subscribed_tokens.update(tokens)
            
            logger.info(f"Subscribed to {len(tokens)} tokens: {tokens}")
            return True
            
        except Exception as e:
            logger.error(f"Subscription error: {str(e)}")
            return False
    
    def unsubscribe(self, tokens: List[str]):
        """Unsubscribe from tokens"""
        if not self.is_connected:
            logger.error("WebSocket not connected")
            return False
        
        try:
            # Prepare unsubscription message
            unsubscription_msg = {
                "correlationID": "abcde12345",
                "action": 0,  # Unsubscribe
                "params": {
                    "mode": "LTP",
                    "tokenList": [
                        {
                            "exchangeType": 1,  # NSE
                            "tokens": tokens
                        }
                    ]
                }
            }
            
            # Send unsubscription
            self.ws.send(json.dumps(unsubscription_msg))
            
            # Remove from tracked tokens
            self.subscribed_tokens.difference_update(tokens)
            
            logger.info(f"Unsubscribed from {len(tokens)} tokens")
            return True
            
        except Exception as e:
            logger.error(f"Unsubscription error: {str(e)}")
            return False
    
    def _run_websocket(self):
        """Run WebSocket in thread"""
        try:
            self.ws.run_forever(
                sslopt={"cert_reqs": ssl.CERT_NONE},
                ping_interval=30,
                ping_timeout=10
            )
        except Exception as e:
            logger.error(f"WebSocket run error: {str(e)}")
    
    def _on_open(self, ws):
        """WebSocket open handler"""
        self.is_connected = True
        logger.info("WebSocket connection opened")
        
        # Start heartbeat
        self._start_heartbeat()
        
        if self.on_connect_callback:
            try:
                self.on_connect_callback()
            except Exception as e:
                logger.error(f"Connect callback error: {str(e)}")
    
    def _on_message(self, ws, message):
        """WebSocket message handler"""
        try:
            # Parse message
            if isinstance(message, bytes):
                # Binary data - tick data
                self._parse_binary_data(message)
            else:
                # Text data - JSON response
                data = json.loads(message)
                self._handle_json_message(data)
                
        except Exception as e:
            logger.error(f"Message handling error: {str(e)}")
    
    def _on_error(self, ws, error):
        """WebSocket error handler"""
        logger.error(f"WebSocket error: {error}")
        
        if self.on_error_callback:
            try:
                self.on_error_callback(error)
            except Exception as e:
                logger.error(f"Error callback error: {str(e)}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """WebSocket close handler"""
        self.is_connected = False
        logger.info(f"WebSocket connection closed: {close_status_code} - {close_msg}")
        
        if self.on_disconnect_callback:
            try:
                self.on_disconnect_callback()
            except Exception as e:
                logger.error(f"Disconnect callback error: {str(e)}")
    
    def _start_heartbeat(self):
        """Start heartbeat thread"""
        self.heartbeat_thread = threading.Thread(
            target=self._heartbeat_worker,
            daemon=True
        )
        self.heartbeat_thread.start()
    
    def _heartbeat_worker(self):
        """Heartbeat worker to keep connection alive"""
        while not self.stop_event.is_set() and self.is_connected:
            try:
                if self.ws:
                    # Send ping
                    heartbeat_msg = {
                        "correlationID": "heartbeat",
                        "action": 2,  # Heartbeat
                        "params": {}
                    }
                    self.ws.send(json.dumps(heartbeat_msg))
                
                # Wait 30 seconds
                self.stop_event.wait(30)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {str(e)}")
                break
    
    def _parse_binary_data(self, data):
        """Parse binary tick data"""
        try:
            # Angel One binary data format parsing
            # This is a simplified version - actual implementation depends on Angel One's binary format
            
            if len(data) < 8:
                return
            
            # Extract basic tick data (simplified)
            # Actual implementation would need Angel One's binary format specification
            
            tick_data = {
                'token': 'unknown',
                'ltp': 0.0,
                'volume': 0,
                'timestamp': time.time()
            }
            
            if self.on_tick_callback:
                self.on_tick_callback(tick_data)
                
        except Exception as e:
            logger.error(f"Binary data parsing error: {str(e)}")
    
    def _handle_json_message(self, data):
        """Handle JSON message"""
        try:
            message_type = data.get('type', 'unknown')
            
            if message_type == 'connection':
                logger.info("WebSocket connection confirmed")
            elif message_type == 'subscription':
                logger.info(f"Subscription confirmed: {data}")
            elif message_type == 'tick':
                # Handle tick data
                if self.on_tick_callback:
                    self.on_tick_callback(data)
            else:
                logger.debug(f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"JSON message handling error: {str(e)}")

class RealTimeDataManager:
    """Manager for real-time market data"""
    
    def __init__(self, angel_api):
        self.angel_api = angel_api
        self.ws_client = None
        self.subscribed_symbols = set()
        self.latest_prices = {}
        self.price_callbacks = []
        
    def connect(self):
        """Connect to real-time data feed"""
        if not self.angel_api.is_connected:
            logger.error("Angel One API not connected")
            return False
        
        try:
            # Create WebSocket client
            self.ws_client = AngelOneWebSocket(
                auth_token=self.angel_api.auth_token,
                api_key=self.angel_api.api_key,
                client_id=self.angel_api.client_id,
                feed_token=self.angel_api.feed_token
            )
            
            # Set callbacks
            self.ws_client.set_on_tick(self._on_tick)
            self.ws_client.set_on_connect(self._on_connect)
            self.ws_client.set_on_disconnect(self._on_disconnect)
            self.ws_client.set_on_error(self._on_error)
            
            # Connect
            return self.ws_client.connect()
            
        except Exception as e:
            logger.error(f"Real-time data connection error: {str(e)}")
            return False
    
    def disconnect(self):
        """Disconnect from real-time data feed"""
        if self.ws_client:
            self.ws_client.disconnect()
            self.ws_client = None
    
    def subscribe_symbols(self, symbols: List[str]):
        """Subscribe to symbols for real-time data"""
        if not self.ws_client or not self.ws_client.is_connected:
            logger.error("WebSocket not connected")
            return False
        
        # Get tokens for symbols
        tokens = []
        for symbol in symbols:
            token = self.angel_api._get_symbol_token(symbol)
            if token and token != '1234':  # Valid token
                tokens.append(token)
        
        if tokens:
            success = self.ws_client.subscribe(tokens)
            if success:
                self.subscribed_symbols.update(symbols)
            return success
        
        return False
    
    def add_price_callback(self, callback: Callable):
        """Add callback for price updates"""
        self.price_callbacks.append(callback)
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Get latest price for symbol"""
        return self.latest_prices.get(symbol)
    
    def _on_tick(self, tick_data):
        """Handle tick data"""
        try:
            # Extract price information
            token = tick_data.get('token')
            ltp = tick_data.get('ltp', 0.0)
            
            # Find symbol for token
            symbol = self._get_symbol_for_token(token)
            
            if symbol and ltp > 0:
                self.latest_prices[symbol] = ltp
                
                # Notify callbacks
                for callback in self.price_callbacks:
                    try:
                        callback(symbol, ltp)
                    except Exception as e:
                        logger.error(f"Price callback error: {str(e)}")
                        
        except Exception as e:
            logger.error(f"Tick handling error: {str(e)}")
    
    def _on_connect(self):
        """Handle WebSocket connection"""
        logger.info("Real-time data feed connected")
    
    def _on_disconnect(self):
        """Handle WebSocket disconnection"""
        logger.warning("Real-time data feed disconnected")
    
    def _on_error(self, error):
        """Handle WebSocket error"""
        logger.error(f"Real-time data feed error: {error}")
    
    def _get_symbol_for_token(self, token: str) -> Optional[str]:
        """Get symbol for token (reverse lookup)"""
        # This would need a reverse mapping of tokens to symbols
        # For now, return None as we don't have reverse mapping implemented
        return None
