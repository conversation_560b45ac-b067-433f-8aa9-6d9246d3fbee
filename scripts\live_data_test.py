#!/usr/bin/env python3
"""
Live Data Testing Script
Tests the trading bot with real Angel One API data
"""
import sys
import os
import asyncio
import pandas as pd
from datetime import datetime, timedelta
import time

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from angel_api import AngelOneAPI
from trading_bot import TradingBot
from technical_analysis import TechnicalAnalyzer
from risk_manager import RiskManager
from config import trading_config, stock_universe

class LiveDataTester:
    """Test trading bot with live Angel One data"""
    
    def __init__(self):
        self.api = AngelOneAPI()
        self.analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
        self.test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS']
        
    def test_api_connectivity(self):
        """Test basic API connectivity"""
        print("🔌 Testing Angel One API Connectivity...")
        print("=" * 50)
        
        # Test login
        print("1. Testing login...")
        success = self.api.login()
        if success:
            print("   ✅ Login successful")
        else:
            print("   ❌ Login failed")
            return False
        
        # Test profile
        print("2. Testing profile fetch...")
        profile = self.api.get_profile()
        if profile:
            print(f"   ✅ Profile: {profile.get('name', 'Unknown')}")
        else:
            print("   ❌ Profile fetch failed")
        
        # Test funds
        print("3. Testing funds fetch...")
        funds = self.api.get_funds()
        if funds:
            cash = funds.get('availablecash', 0)
            print(f"   ✅ Available cash: ₹{cash}")
        else:
            print("   ❌ Funds fetch failed")
        
        return True
    
    def test_market_data(self):
        """Test market data fetching"""
        print("\n📊 Testing Market Data...")
        print("=" * 50)
        
        for symbol in self.test_symbols:
            print(f"\nTesting {symbol}:")
            
            # Test LTP
            ltp = self.api.get_ltp(symbol)
            if ltp:
                print(f"   ✅ LTP: ₹{ltp:.2f}")
            else:
                print(f"   ❌ LTP fetch failed")
                continue
            
            # Test quote
            quote = self.api.get_quote(symbol)
            if quote:
                print(f"   ✅ Quote: Open ₹{quote.get('open', 'N/A')}, High ₹{quote.get('high', 'N/A')}")
            else:
                print(f"   ❌ Quote fetch failed")
            
            # Test historical data
            df = self.api.get_historical_data(symbol, "ONE_MINUTE")
            if df is not None and len(df) > 0:
                print(f"   ✅ Historical data: {len(df)} records")
                print(f"      Latest: ₹{df['close'].iloc[-1]:.2f} at {df.index[-1]}")
            else:
                print(f"   ❌ Historical data fetch failed")
    
    def test_technical_analysis(self):
        """Test technical analysis with live data"""
        print("\n📈 Testing Technical Analysis...")
        print("=" * 50)
        
        for symbol in self.test_symbols[:3]:  # Test first 3 symbols
            print(f"\nAnalyzing {symbol}:")
            
            # Get historical data
            df = self.api.get_historical_data(symbol, "ONE_MINUTE")
            if df is None or len(df) < 50:
                print(f"   ❌ Insufficient data for {symbol}")
                continue
            
            # Calculate indicators
            indicators = self.analyzer.calculate_indicators(df)
            if indicators:
                print(f"   ✅ Indicators calculated: {len(indicators)} indicators")
                
                # Show some key indicators
                if 'rsi' in indicators:
                    rsi = indicators['rsi'][-1]
                    print(f"      RSI: {rsi:.2f}")
                
                if 'ema_9' in indicators and 'ema_21' in indicators:
                    ema_9 = indicators['ema_9'][-1]
                    ema_21 = indicators['ema_21'][-1]
                    print(f"      EMA 9: ₹{ema_9:.2f}, EMA 21: ₹{ema_21:.2f}")
                
                if 'vwap' in indicators:
                    vwap = indicators['vwap'][-1]
                    current_price = df['close'].iloc[-1]
                    print(f"      VWAP: ₹{vwap:.2f}, Current: ₹{current_price:.2f}")
            else:
                print(f"   ❌ Indicator calculation failed")
                continue
            
            # Generate signals
            signals = self.analyzer.analyze_stock(df, symbol)
            if signals:
                print(f"   ✅ Signals generated: {len(signals)}")
                for signal in signals:
                    print(f"      {signal.strategy}: {signal.signal_type} "
                          f"(Confidence: {signal.confidence:.2f})")
            else:
                print(f"   ℹ️  No signals generated (normal)")
    
    def test_risk_management(self):
        """Test risk management with live prices"""
        print("\n🛡️ Testing Risk Management...")
        print("=" * 50)
        
        for symbol in self.test_symbols[:2]:  # Test first 2 symbols
            print(f"\nTesting risk management for {symbol}:")
            
            # Get current price
            current_price = self.api.get_ltp(symbol)
            if not current_price:
                print(f"   ❌ Could not get price for {symbol}")
                continue
            
            # Test position sizing
            stop_loss = current_price * 0.98  # 2% stop loss
            position_size = self.risk_manager.calculate_position_size(current_price, stop_loss)
            
            print(f"   Current Price: ₹{current_price:.2f}")
            print(f"   Stop Loss: ₹{stop_loss:.2f}")
            print(f"   Position Size: {position_size} shares")
            
            if position_size > 0:
                position_value = position_size * current_price
                print(f"   Position Value: ₹{position_value:.2f}")
                
                # Test trade validation
                is_valid, message = self.risk_manager.validate_trade(
                    symbol, "BUY", position_size, current_price, stop_loss
                )
                
                if is_valid:
                    print(f"   ✅ Trade validation: PASSED")
                else:
                    print(f"   ❌ Trade validation: {message}")
            else:
                print(f"   ℹ️  Position size 0 (within risk limits)")
    
    def test_full_pipeline(self):
        """Test complete trading pipeline with live data"""
        print("\n🔄 Testing Full Trading Pipeline...")
        print("=" * 50)
        
        symbol = 'RELIANCE'  # Use RELIANCE for full test
        
        print(f"Testing complete pipeline for {symbol}:")
        
        # Step 1: Get market data
        print("1. Fetching market data...")
        current_price = self.api.get_ltp(symbol)
        df = self.api.get_historical_data(symbol, "ONE_MINUTE")
        
        if not current_price or df is None:
            print("   ❌ Failed to get market data")
            return
        
        print(f"   ✅ Current price: ₹{current_price:.2f}")
        print(f"   ✅ Historical data: {len(df)} records")
        
        # Step 2: Technical analysis
        print("2. Running technical analysis...")
        signals = self.analyzer.analyze_stock(df, symbol)
        
        if signals:
            print(f"   ✅ Generated {len(signals)} signals")
            
            # Step 3: Signal confluence
            confluence_signal = self.analyzer.get_confluence_signal(signals)
            
            if confluence_signal:
                print(f"   ✅ Confluence signal: {confluence_signal.signal_type} "
                      f"(Confidence: {confluence_signal.confidence:.2f})")
                
                # Step 4: Risk management
                print("3. Validating with risk management...")
                position_size = self.risk_manager.calculate_position_size(
                    confluence_signal.entry_price, confluence_signal.stop_loss
                )
                
                if position_size > 0:
                    is_valid, message = self.risk_manager.validate_trade(
                        symbol, confluence_signal.signal_type, position_size,
                        confluence_signal.entry_price, confluence_signal.stop_loss
                    )
                    
                    if is_valid:
                        print(f"   ✅ Trade would be executed:")
                        print(f"      Symbol: {symbol}")
                        print(f"      Side: {confluence_signal.signal_type}")
                        print(f"      Quantity: {position_size}")
                        print(f"      Entry: ₹{confluence_signal.entry_price:.2f}")
                        print(f"      Stop Loss: ₹{confluence_signal.stop_loss:.2f}")
                        print(f"      Target: ₹{confluence_signal.target:.2f}")
                    else:
                        print(f"   ❌ Trade rejected: {message}")
                else:
                    print(f"   ℹ️  Position size 0 (risk limits)")
            else:
                print(f"   ℹ️  No confluence signal")
        else:
            print(f"   ℹ️  No signals generated")
    
    async def test_live_bot(self, duration_minutes=5):
        """Test live bot operation for a short duration"""
        print(f"\n🤖 Testing Live Bot Operation ({duration_minutes} minutes)...")
        print("=" * 50)
        
        # Create bot in paper trading mode
        bot = TradingBot()
        bot.paper_trading = True
        bot.trading_symbols = self.test_symbols[:3]  # Limit to 3 symbols for testing
        
        # Login
        login_success = await bot.login()
        if not login_success:
            print("❌ Bot login failed")
            return
        
        print("✅ Bot logged in successfully")
        print(f"📊 Testing with symbols: {bot.trading_symbols}")
        print(f"⏱️  Running for {duration_minutes} minutes...")
        print()
        
        start_time = time.time()
        cycle_count = 0
        
        while time.time() - start_time < duration_minutes * 60:
            cycle_count += 1
            cycle_start = time.time()
            
            print(f"Cycle {cycle_count} - {datetime.now().strftime('%H:%M:%S')}")
            
            try:
                # Update market data
                await bot._update_market_data()
                print("  ✅ Market data updated")
                
                # Update position prices (if any positions)
                await bot._update_position_prices()
                
                # Check exit conditions
                await bot._check_exit_conditions()
                
                # Scan for signals (limited frequency)
                if cycle_count % 3 == 0:  # Every 3rd cycle
                    await bot._scan_for_signals()
                    print("  ✅ Signal scan completed")
                
                # Show current status
                metrics = bot.risk_manager.get_portfolio_summary()
                print(f"  📊 Positions: {metrics.current_positions}, "
                      f"P&L: ₹{metrics.daily_pnl:.2f}")
                
                if bot.risk_manager.positions:
                    for symbol, pos in bot.risk_manager.positions.items():
                        print(f"     {symbol}: {pos.side} {pos.quantity} @ ₹{pos.entry_price:.2f}")
                
            except Exception as e:
                print(f"  ❌ Error in cycle: {str(e)}")
            
            # Wait for next cycle (30 seconds)
            cycle_time = time.time() - cycle_start
            sleep_time = max(0, 30 - cycle_time)
            
            if sleep_time > 0:
                print(f"  ⏳ Waiting {sleep_time:.1f}s for next cycle...")
                await asyncio.sleep(sleep_time)
            
            print()
        
        # Final status
        print("🏁 Live bot test completed!")
        final_metrics = bot.risk_manager.get_portfolio_summary()
        print(f"Final P&L: ₹{final_metrics.daily_pnl:.2f}")
        print(f"Total Cycles: {cycle_count}")
        
        # Cleanup
        await bot.square_off_all_positions("Test End")
        
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Angel One Trading Bot - Live Data Testing")
        print("=" * 60)
        print(f"Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        try:
            # Test 1: API Connectivity
            if not self.test_api_connectivity():
                print("\n❌ API connectivity test failed. Cannot proceed.")
                return False
            
            # Test 2: Market Data
            self.test_market_data()
            
            # Test 3: Technical Analysis
            self.test_technical_analysis()
            
            # Test 4: Risk Management
            self.test_risk_management()
            
            # Test 5: Full Pipeline
            self.test_full_pipeline()
            
            # Test 6: Live Bot (optional)
            run_live_test = input("\n🤖 Run live bot test for 5 minutes? (y/N): ").strip().lower()
            if run_live_test == 'y':
                asyncio.run(self.test_live_bot(5))
            
            print("\n🎉 All tests completed successfully!")
            print("\n✅ Your bot is ready for paper trading with real data!")
            print("\nNext steps:")
            print("1. Run: python main.py (with PAPER_TRADING=True)")
            print("2. Monitor: tail -f logs/trading_bot.log")
            print("3. After successful paper trading, consider live trading")
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n👋 Testing stopped by user.")
            return False
        except Exception as e:
            print(f"\n❌ Test error: {str(e)}")
            return False
        finally:
            # Logout
            if self.api.is_connected:
                self.api.logout()

def main():
    """Main function"""
    tester = LiveDataTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🚀 Ready for deployment!")
        return 0
    else:
        print("\n❌ Tests failed. Please fix issues before deployment.")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
