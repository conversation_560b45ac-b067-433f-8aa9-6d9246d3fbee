PS E:\New folder (5)> python deploy_ultimate_bot.py
🚀 DEPLOYING ULTIMATE TRADING BOT
============================================================
Select mode (1=Paper Trading, 2=Live Trading): 2
⚠️  LIVE TRADING MODE - Are you sure? (yes/no): yes
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f511' in position 44: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 627, in __init__
    self.angel_api = AngelOneAPI()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 91, in __init__
    logger.info("🔑 Angel One API initialized")
Message: '🔑 Angel One API initialized'
Arguments: ()
2025-06-02 17:21:24,851 - __main__ - INFO - 🔑 Angel One API initialized
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 56: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 627, in __init__
    self.angel_api = AngelOneAPI()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 92, in __init__
    logger.info(f"   API Key: {'✅ SET' if self.api_key else '❌ NOT SET'}")
Message: '   API Key: ✅ SET'
Arguments: ()
2025-06-02 17:21:24,857 - __main__ - INFO -    API Key: ✅ SET
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 58: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 627, in __init__
    self.angel_api = AngelOneAPI()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 93, in __init__
    logger.info(f"   Client ID: {'✅ SET' if self.client_id else '❌ NOT SET'}")
Message: '   Client ID: ✅ SET'
Arguments: ()
2025-06-02 17:21:24,862 - __main__ - INFO -    Client ID: ✅ SET
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ad' in position 44: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 628, in __init__
    self.sentiment_analyzer = ProductionSentimentAnalyzer()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 375, in __init__
    logger.info("💭 Production sentiment analyzer initialized")
Message: '💭 Production sentiment analyzer initialized'
Arguments: ()
2025-06-02 17:21:24,867 - __main__ - INFO - 💭 Production sentiment analyzer initialized
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 57: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 628, in __init__
    self.sentiment_analyzer = ProductionSentimentAnalyzer()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 376, in __init__
    logger.info(f"   News API: {'✅ SET' if self.news_api_key else '❌ NOT SET'}")
Message: '   News API: ❌ NOT SET'
Arguments: ()
2025-06-02 17:21:24,870 - __main__ - INFO -    News API: ❌ NOT SET
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 62: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 628, in __init__
    self.sentiment_analyzer = ProductionSentimentAnalyzer()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 377, in __init__
    logger.info(f"   Alpha Vantage: {'✅ SET' if self.alpha_vantage_key else '❌ NOT SET'}")
Message: '   Alpha Vantage: ❌ NOT SET'
Arguments: ()
2025-06-02 17:21:24,872 - __main__ - INFO -    Alpha Vantage: ❌ NOT SET
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4ca' in position 44: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 629, in __init__
    self.technical_analyzer = UltimateTechnicalAnalyzer()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 490, in __init__
    logger.info("📊 Ultimate technical analyzer initialized")
Message: '📊 Ultimate technical analyzer initialized'
Arguments: ()
2025-06-02 17:21:24,874 - __main__ - INFO - 📊 Ultimate technical analyzer initialized
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f680' in position 44: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 660, in __init__
    logger.info("🚀 ULTIMATE TRADING BOT DEPLOYED")
Message: '🚀 ULTIMATE TRADING BOT DEPLOYED'
Arguments: ()
2025-06-02 17:21:24,875 - __main__ - INFO - 🚀 ULTIMATE TRADING BOT DEPLOYED
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f4b0' in position 53: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 661, in __init__
    logger.info(f"   Mode: {'📄 PAPER TRADING' if paper_trading else '💰 LIVE TRADING'}")
Message: '   Mode: 💰 LIVE TRADING'
Arguments: ()
2025-06-02 17:21:24,876 - __main__ - INFO -    Mode: 💰 LIVE TRADING
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u20b9' in position 56: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1248, in deploy_bot
    bot = UltimateTradingBot(paper_trading=paper_trading)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 662, in __init__
    logger.info(f"   Capital: ₹{self.capital:.2f}")
Message: '   Capital: ₹100.00'
Arguments: ()
2025-06-02 17:21:24,877 - __main__ - INFO -    Capital: ₹100.00
2025-06-02 17:21:24,879 - __main__ - INFO -    Watchlist: 10 stocks
2025-06-02 17:21:24,879 - __main__ - INFO -    Confidence Threshold: 42%
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f504' in position 44: character maps to <undefined>
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1251, in deploy_bot
    if not await bot.initialize():
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 669, in initialize
    logger.info("🔄 Initializing Ultimate Trading Bot...")
Message: '🔄 Initializing Ultimate Trading Bot...'
Arguments: ()
2025-06-02 17:21:24,879 - __main__ - INFO - 🔄 Initializing Ultimate Trading Bot...
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 45: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1251, in deploy_bot
    if not await bot.initialize():
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 673, in initialize
    auth_success = await self.angel_api.authenticate()
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 139, in authenticate
    logger.error(f"❌ Authentication failed: {data.get('message')}")
Message: '❌ Authentication failed: Invalid apiKey'
Arguments: ()
2025-06-02 17:21:25,312 - __main__ - ERROR - ❌ Authentication failed: Invalid apiKey
--- Logging error ---
Traceback (most recent call last):
  File "E:\New folder\Lib\logging\__init__.py", line 1113, in emit
    stream.write(msg + self.terminator)
  File "E:\New folder\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 45: character maps to <undefined>   
Call stack:
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1273, in <module>
    asyncio.run(deploy_bot())
  File "E:\New folder\Lib\asyncio\runners.py", line 190, in run
    return runner.run(main)
  File "E:\New folder\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "E:\New folder\Lib\asyncio\base_events.py", line 640, in run_until_complete
    self.run_forever()
  File "E:\New folder\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "E:\New folder\Lib\asyncio\base_events.py", line 607, in run_forever
    self._run_once()
  File "E:\New folder\Lib\asyncio\base_events.py", line 1922, in _run_once
    handle._run()
  File "E:\New folder\Lib\asyncio\events.py", line 80, in _run
    self._context.run(self._callback, *self._args)
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 1251, in deploy_bot
    if not await bot.initialize():
  File "E:\New folder (5)\deploy_ultimate_bot.py", line 675, in initialize
    logger.error("❌ Angel One authentication failed")
Message: '❌ Angel One authentication failed'
Arguments: ()
2025-06-02 17:21:25,321 - __main__ - ERROR - ❌ Angel One authentication failed
❌ Bot initialization failed
PS E:\New folder (5)>