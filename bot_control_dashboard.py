#!/usr/bin/env python3
"""
ULTIMATE TRADING BOT - CONTROL DASHBOARD
Easy-to-use interface for managing your trading bot
"""
import os
import sys
import json
import subprocess
import signal
import psutil
from datetime import datetime
from typing import Optional

class BotControlDashboard:
    """Control dashboard for the Ultimate Trading Bot"""
    
    def __init__(self):
        self.bot_process = None
        self.bot_pid_file = "logs/bot.pid"
        self.bot_state_file = "logs/bot_state.json"
        
    def show_main_menu(self):
        """Display main control menu"""
        os.system('cls' if os.name == 'nt' else 'clear')
        print("🤖 ULTIMATE TRADING BOT - CONTROL DASHBOARD")
        print("=" * 60)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Show bot status
        status = self.get_bot_status()
        print(f"🤖 Bot Status: {status['status']}")
        if status['running']:
            print(f"   PID: {status['pid']}")
            print(f"   Runtime: {status['runtime']}")
            print(f"   Mode: {status['mode']}")
        print()
        
        # Show menu options
        print("🎛️  CONTROL OPTIONS:")
        print("   1. 🚀 Start Bot (Paper Trading)")
        print("   2. 💰 Start Bot (Live Trading)")
        print("   3. ⏹️  Stop Bot")
        print("   4. 🔄 Restart Bot")
        print("   5. 📊 View Bot Status")
        print("   6. 📈 View Performance")
        print("   7. 📋 View Logs")
        print("   8. ⚙️  Configuration")
        print("   9. 🧪 Test Deployment")
        print("   0. ❌ Exit Dashboard")
        print()
        
    def get_bot_status(self) -> dict:
        """Get current bot status"""
        try:
            # Check if PID file exists
            if os.path.exists(self.bot_pid_file):
                with open(self.bot_pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                # Check if process is running
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    create_time = datetime.fromtimestamp(process.create_time())
                    runtime = datetime.now() - create_time
                    
                    # Try to determine mode from state file
                    mode = "Unknown"
                    if os.path.exists(self.bot_state_file):
                        try:
                            with open(self.bot_state_file, 'r') as f:
                                state = json.load(f)
                                mode = "Paper Trading" if state.get('paper_trading', True) else "Live Trading"
                        except:
                            pass
                    
                    return {
                        'status': '🟢 RUNNING',
                        'running': True,
                        'pid': pid,
                        'runtime': str(runtime).split('.')[0],
                        'mode': mode
                    }
                else:
                    # PID file exists but process is dead
                    os.remove(self.bot_pid_file)
            
            return {
                'status': '🔴 STOPPED',
                'running': False,
                'pid': None,
                'runtime': None,
                'mode': None
            }
            
        except Exception as e:
            return {
                'status': f'❌ ERROR: {e}',
                'running': False,
                'pid': None,
                'runtime': None,
                'mode': None
            }
    
    def start_bot(self, live_trading: bool = False):
        """Start the trading bot"""
        status = self.get_bot_status()
        if status['running']:
            print("⚠️  Bot is already running!")
            print(f"   PID: {status['pid']}")
            print(f"   Mode: {status['mode']}")
            input("\nPress Enter to continue...")
            return
        
        mode = "Live Trading" if live_trading else "Paper Trading"
        print(f"🚀 Starting bot in {mode} mode...")
        
        if live_trading:
            confirm = input("⚠️  LIVE TRADING MODE - Are you sure? (yes/no): ").strip().lower()
            if confirm != "yes":
                print("❌ Start cancelled")
                input("Press Enter to continue...")
                return
        
        try:
            # Create logs directory
            os.makedirs('logs', exist_ok=True)
            
            # Start bot process
            cmd = [sys.executable, 'deploy_ultimate_bot.py']
            
            # Set environment variable for auto-mode
            env = os.environ.copy()
            env['BOT_AUTO_MODE'] = 'true'
            env['BOT_PAPER_TRADING'] = 'false' if live_trading else 'true'
            
            # Start process in background
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            
            # Save PID
            with open(self.bot_pid_file, 'w') as f:
                f.write(str(process.pid))
            
            print(f"✅ Bot started successfully!")
            print(f"   PID: {process.pid}")
            print(f"   Mode: {mode}")
            print(f"   Logs: logs/ultimate_bot.log")
            
        except Exception as e:
            print(f"❌ Failed to start bot: {e}")
        
        input("\nPress Enter to continue...")
    
    def stop_bot(self):
        """Stop the trading bot"""
        status = self.get_bot_status()
        if not status['running']:
            print("⚠️  Bot is not running!")
            input("Press Enter to continue...")
            return
        
        print("⏹️  Stopping bot...")
        
        try:
            # Send SIGTERM to gracefully stop
            os.kill(status['pid'], signal.SIGTERM)
            
            # Wait a bit for graceful shutdown
            import time
            time.sleep(3)
            
            # Check if still running
            if psutil.pid_exists(status['pid']):
                # Force kill if still running
                os.kill(status['pid'], signal.SIGKILL)
                print("🔨 Force stopped bot")
            else:
                print("✅ Bot stopped gracefully")
            
            # Remove PID file
            if os.path.exists(self.bot_pid_file):
                os.remove(self.bot_pid_file)
                
        except Exception as e:
            print(f"❌ Failed to stop bot: {e}")
        
        input("\nPress Enter to continue...")
    
    def restart_bot(self):
        """Restart the trading bot"""
        print("🔄 Restarting bot...")
        
        status = self.get_bot_status()
        if status['running']:
            self.stop_bot()
        
        # Ask for mode
        print("\nSelect restart mode:")
        print("1. Paper Trading")
        print("2. Live Trading")
        choice = input("Enter choice (1-2): ").strip()
        
        if choice == "1":
            self.start_bot(live_trading=False)
        elif choice == "2":
            self.start_bot(live_trading=True)
        else:
            print("❌ Invalid choice")
            input("Press Enter to continue...")
    
    def view_performance(self):
        """View bot performance"""
        print("📈 BOT PERFORMANCE")
        print("=" * 40)
        
        try:
            if os.path.exists(self.bot_state_file):
                with open(self.bot_state_file, 'r') as f:
                    state = json.load(f)
                
                metrics = state.get('performance_metrics', {})
                positions = state.get('positions', {})
                trades = state.get('trades', [])
                
                print(f"📊 PERFORMANCE METRICS:")
                print(f"   Total Trades: {metrics.get('total_trades', 0)}")
                print(f"   Winning Trades: {metrics.get('winning_trades', 0)}")
                print(f"   Win Rate: {metrics.get('win_rate', 0):.1%}")
                print(f"   Total P&L: ₹{metrics.get('total_pnl', 0):.2f}")
                print(f"   Max Drawdown: ₹{metrics.get('max_drawdown', 0):.2f}")
                print()
                
                print(f"📋 CURRENT POSITIONS: {len(positions)}")
                for symbol, pos in positions.items():
                    print(f"   {symbol}: {pos.get('status', 'Unknown')}")
                print()
                
                print(f"📜 RECENT TRADES: {len(trades)}")
                for trade in trades[-5:]:  # Last 5 trades
                    timestamp = trade.get('timestamp', 'Unknown')
                    signal = trade.get('signal', {})
                    symbol = signal.get('symbol', 'Unknown')
                    action = signal.get('action', 'Unknown')
                    print(f"   {timestamp}: {symbol} {action}")
                
            else:
                print("⚠️  No performance data available")
                print("   Start the bot to generate performance data")
                
        except Exception as e:
            print(f"❌ Error reading performance data: {e}")
        
        input("\nPress Enter to continue...")
    
    def view_logs(self):
        """View bot logs"""
        print("📋 BOT LOGS")
        print("=" * 40)
        
        log_file = "logs/ultimate_bot.log"
        if os.path.exists(log_file):
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                
                # Show last 20 lines
                print("📜 LAST 20 LOG ENTRIES:")
                for line in lines[-20:]:
                    print(line.strip())
                    
            except Exception as e:
                print(f"❌ Error reading log file: {e}")
        else:
            print("⚠️  No log file found")
            print("   Start the bot to generate logs")
        
        input("\nPress Enter to continue...")
    
    def view_configuration(self):
        """View and edit configuration"""
        print("⚙️  CONFIGURATION")
        print("=" * 40)
        
        env_file = ".env"
        if os.path.exists(env_file):
            print("📋 CURRENT CONFIGURATION:")
            try:
                with open(env_file, 'r') as f:
                    lines = f.readlines()
                
                for line in lines:
                    if line.strip() and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        # Hide sensitive values
                        if 'KEY' in key or 'PASSWORD' in key or 'SECRET' in key:
                            value = '*' * min(len(value), 8)
                        print(f"   {key}: {value}")
                        
            except Exception as e:
                print(f"❌ Error reading configuration: {e}")
        else:
            print("⚠️  No .env file found")
            print("   Copy .env.example to .env and configure")
        
        print("\n💡 To edit configuration:")
        print("   1. Stop the bot")
        print("   2. Edit .env file")
        print("   3. Restart the bot")
        
        input("\nPress Enter to continue...")
    
    def test_deployment(self):
        """Run deployment test"""
        print("🧪 RUNNING DEPLOYMENT TEST")
        print("=" * 40)
        
        try:
            result = subprocess.run([sys.executable, 'test_deployment.py'], 
                                  capture_output=True, text=True, timeout=60)
            
            print("📋 TEST OUTPUT:")
            print(result.stdout)
            
            if result.stderr:
                print("❌ ERRORS:")
                print(result.stderr)
                
        except subprocess.TimeoutExpired:
            print("⏰ Test timed out")
        except Exception as e:
            print(f"❌ Test error: {e}")
        
        input("\nPress Enter to continue...")
    
    def run(self):
        """Run the control dashboard"""
        while True:
            self.show_main_menu()
            
            choice = input("Enter your choice (0-9): ").strip()
            
            if choice == "1":
                self.start_bot(live_trading=False)
            elif choice == "2":
                self.start_bot(live_trading=True)
            elif choice == "3":
                self.stop_bot()
            elif choice == "4":
                self.restart_bot()
            elif choice == "5":
                status = self.get_bot_status()
                print(f"\n🤖 Bot Status: {status['status']}")
                if status['running']:
                    print(f"   PID: {status['pid']}")
                    print(f"   Runtime: {status['runtime']}")
                    print(f"   Mode: {status['mode']}")
                input("\nPress Enter to continue...")
            elif choice == "6":
                self.view_performance()
            elif choice == "7":
                self.view_logs()
            elif choice == "8":
                self.view_configuration()
            elif choice == "9":
                self.test_deployment()
            elif choice == "0":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please try again.")
                input("Press Enter to continue...")

def main():
    """Main function"""
    try:
        dashboard = BotControlDashboard()
        dashboard.run()
    except KeyboardInterrupt:
        print("\n👋 Dashboard closed by user")
    except Exception as e:
        print(f"\n❌ Dashboard error: {e}")

if __name__ == "__main__":
    main()
