# 🔐 Google OAuth Setup Guide - COMPLETE PRODUCTION SETUP

## 🚀 Setting up Google Authentication for Ultimate Trading Bot

**IMPORTANT:** This is required for the website to work. Follow these steps carefully to enable Google sign-in for your production-ready trading bot website.

### Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: "Ultimate Trading Bot"
4. Click "Create"

### Step 2: Enable Google+ API

1. In the Google Cloud Console, go to "APIs & Services" → "Library"
2. Search for "Google+ API"
3. Click on it and press "Enable"

### Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" → "OAuth consent screen"
2. Choose "External" (unless you have a Google Workspace account)
3. Fill in the required information:
   - **App name**: Ultimate Trading Bot
   - **User support email**: Your email
   - **Developer contact information**: Your email
4. Add scopes:
   - `../auth/userinfo.email`
   - `../auth/userinfo.profile`
   - `openid`
5. Add test users (your email addresses that can test the app)
6. Save and continue

### Step 4: Create OAuth 2.0 Credentials

1. Go to "APIs & Services" → "Credentials"
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Set the name: "Trading Bot Web Client"
5. Add Authorized redirect URIs:
   - `http://localhost:5000/auth/google/callback`
   - `http://127.0.0.1:5000/auth/google/callback`
   - (Add your production domain when deploying)
6. Click "Create"

### Step 5: Get Your Credentials

1. Copy the **Client ID** and **Client Secret**
2. Create a `.env` file in your project root (copy from `.env.example`)
3. Add your credentials:
   ```
   GOOGLE_CLIENT_ID=your-client-id-here.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your-client-secret-here
   ```

### Step 6: Test the Setup

1. Start your trading bot website:
   ```bash
   python website_interface.py
   ```
2. Go to `http://localhost:5000`
3. You should be redirected to the login page
4. Click "Sign in with Google"
5. Complete the OAuth flow

## 🔧 Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch" error**
   - Make sure the redirect URI in Google Console exactly matches your app
   - Check for trailing slashes and http vs https

2. **"This app isn't verified" warning**
   - This is normal for development
   - Click "Advanced" → "Go to Ultimate Trading Bot (unsafe)"
   - For production, you'll need to verify your app

3. **"Access blocked" error**
   - Make sure you added your email as a test user
   - Check that the OAuth consent screen is properly configured

### Environment Variables:

Make sure your `.env` file contains:
```
SECRET_KEY=your-super-secret-key-change-this
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 🚀 Production Deployment

For production deployment:

1. **Update redirect URIs** in Google Console:
   - Add your production domain: `https://yourdomain.com/auth/google/callback`

2. **Verify your app** (optional but recommended):
   - Go through Google's app verification process
   - This removes the "unverified app" warning

3. **Use HTTPS** in production:
   - Google OAuth requires HTTPS for production apps
   - Use services like Cloudflare, Let's Encrypt, or your hosting provider's SSL

4. **Environment variables**:
   - Use secure environment variable management
   - Never commit `.env` files to version control

## 📱 Features Enabled

Once Google OAuth is set up, users can:

- ✅ Sign in with their Google account
- ✅ Automatic account creation
- ✅ Secure session management
- ✅ Profile management
- ✅ Dark mode preferences
- ✅ Personal trading settings
- ✅ Angel One API integration

## 🔒 Security Notes

- User passwords are never stored (Google handles authentication)
- All API credentials are encrypted in the database
- Sessions are secure and time-limited
- CSRF protection is enabled
- SQL injection protection through parameterized queries

## 📞 Support

If you encounter issues:

1. Check the console logs for error messages
2. Verify all environment variables are set correctly
3. Ensure Google Cloud project is properly configured
4. Test with a fresh browser session (clear cookies)

Happy trading! 🚀📈
