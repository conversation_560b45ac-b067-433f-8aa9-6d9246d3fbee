"""
Live API Testing with Real Angel One SmartAPI
Tests the bot with actual market data and API calls
"""
import pytest
import sys
import os
import asyncio
import pandas as pd
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from angel_api import AngelOneAPI
from trading_bot import TradingBot
from risk_manager import RiskManager
from technical_analysis import TechnicalAnalyzer
from config import trading_config, env_config

class TestLiveAPI:
    """Test cases using real Angel One API"""
    
    @classmethod
    def setup_class(cls):
        """Setup for all tests"""
        cls.api = AngelOneAPI()
        cls.logged_in = False
        
        # Check if credentials are available
        creds = env_config.get_angel_credentials()
        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            pytest.skip("Angel One credentials not configured")
    
    def test_api_login(self):
        """Test Angel One API login"""
        success = self.api.login()
        assert success == True, "Failed to login to Angel One API"
        
        self.__class__.logged_in = True
        print("✅ Successfully logged in to Angel One")
    
    def test_get_profile(self):
        """Test getting user profile"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        profile = self.api.get_profile()
        assert profile is not None, "Failed to get profile"
        assert 'name' in profile, "Profile missing name field"
        
        print(f"✅ Profile retrieved: {profile.get('name', 'Unknown')}")
    
    def test_get_funds(self):
        """Test getting funds information"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        funds = self.api.get_funds()
        assert funds is not None, "Failed to get funds"
        assert 'availablecash' in funds, "Funds missing availablecash field"
        
        available_cash = funds['availablecash']
        print(f"✅ Available cash: ₹{available_cash}")
        
        # Ensure sufficient funds for testing
        assert float(available_cash) >= 1000, "Insufficient funds for testing"
    
    def test_get_ltp(self):
        """Test getting Last Traded Price"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        
        for symbol in test_symbols:
            ltp = self.api.get_ltp(symbol)
            assert ltp is not None, f"Failed to get LTP for {symbol}"
            assert ltp > 0, f"Invalid LTP for {symbol}: {ltp}"
            
            print(f"✅ {symbol} LTP: ₹{ltp:.2f}")
    
    def test_get_historical_data(self):
        """Test getting historical data"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        symbol = 'RELIANCE'
        
        # Get last 7 days of data
        from_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d 09:15")
        to_date = datetime.now().strftime("%Y-%m-%d 15:30")
        
        df = self.api.get_historical_data(
            symbol=symbol,
            interval="ONE_MINUTE",
            from_date=from_date,
            to_date=to_date
        )
        
        assert df is not None, f"Failed to get historical data for {symbol}"
        assert len(df) > 0, f"No historical data returned for {symbol}"
        
        # Check DataFrame structure
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in expected_columns:
            assert col in df.columns, f"Missing column: {col}"
        
        # Check data quality
        assert df['high'].min() >= df['low'].max(), "Invalid OHLC data"
        assert df['volume'].min() >= 0, "Invalid volume data"
        
        print(f"✅ Historical data for {symbol}: {len(df)} records")
        print(f"   Date range: {df.index.min()} to {df.index.max()}")
    
    def test_get_quote(self):
        """Test getting detailed quote"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        symbol = 'RELIANCE'
        quote = self.api.get_quote(symbol)
        
        assert quote is not None, f"Failed to get quote for {symbol}"
        
        # Check essential quote fields
        essential_fields = ['ltp', 'open', 'high', 'low', 'close']
        for field in essential_fields:
            if field in quote:
                assert float(quote[field]) > 0, f"Invalid {field} in quote"
        
        print(f"✅ Quote for {symbol}: LTP ₹{quote.get('ltp', 'N/A')}")
    
    def test_search_scrips(self):
        """Test scrip search functionality"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        search_text = "RELI"
        results = self.api.search_scrips(search_text)
        
        assert results is not None, "Failed to search scrips"
        assert len(results) > 0, "No search results returned"
        
        # Check if RELIANCE is in results
        reliance_found = any('RELIANCE' in str(result) for result in results)
        assert reliance_found, "RELIANCE not found in search results"
        
        print(f"✅ Scrip search for '{search_text}': {len(results)} results")
    
    def test_order_book(self):
        """Test getting order book"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        orders = self.api.get_order_book()
        assert orders is not None, "Failed to get order book"
        
        print(f"✅ Order book retrieved: {len(orders)} orders")
    
    def test_positions(self):
        """Test getting positions"""
        if not self.logged_in:
            pytest.skip("Not logged in")
        
        positions = self.api.get_positions()
        assert positions is not None, "Failed to get positions"
        
        print(f"✅ Positions retrieved: {len(positions)} positions")
    
    def test_symbol_tokens(self):
        """Test symbol token mapping"""
        test_symbols = ['RELIANCE', 'TCS', 'HDFCBANK', 'ICICIBANK', 'INFOSYS']
        
        for symbol in test_symbols:
            token = self.api._get_symbol_token(symbol)
            assert token is not None, f"No token for {symbol}"
            assert token != '1234', f"Default token returned for {symbol}"
            
            print(f"✅ {symbol} token: {token}")

class TestLiveTradingBot:
    """Test trading bot with live API data"""
    
    @classmethod
    def setup_class(cls):
        """Setup for all tests"""
        # Check if credentials are available
        creds = env_config.get_angel_credentials()
        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            pytest.skip("Angel One credentials not configured")
    
    def test_bot_initialization_with_live_api(self):
        """Test bot initialization with live API"""
        bot = TradingBot()
        bot.paper_trading = True  # Ensure paper trading for safety
        
        # Test login
        login_success = asyncio.run(bot.login())
        assert login_success == True, "Bot failed to login"
        
        print("✅ Trading bot initialized and logged in successfully")
    
    def test_live_market_data_update(self):
        """Test market data update with live API"""
        bot = TradingBot()
        bot.paper_trading = True
        
        # Login first
        login_success = asyncio.run(bot.login())
        if not login_success:
            pytest.skip("Failed to login")
        
        # Test market data update
        asyncio.run(bot._update_market_data())
        
        # Check that data was cached
        assert len(bot.market_data_cache) > 0, "No market data cached"
        
        for symbol in list(bot.market_data_cache.keys())[:3]:  # Check first 3
            data = bot.market_data_cache[symbol]
            assert len(data) > 0, f"No data for {symbol}"
            assert 'price' in data[0], f"Missing price in data for {symbol}"
            assert data[0]['price'] > 0, f"Invalid price for {symbol}"
        
        print(f"✅ Market data updated for {len(bot.market_data_cache)} symbols")
    
    def test_live_historical_data_fetch(self):
        """Test historical data fetching with live API"""
        bot = TradingBot()
        bot.paper_trading = True
        
        # Login first
        login_success = asyncio.run(bot.login())
        if not login_success:
            pytest.skip("Failed to login")
        
        # Test historical data fetch
        symbol = 'RELIANCE'
        df = asyncio.run(bot._get_historical_data(symbol))
        
        assert df is not None, f"Failed to get historical data for {symbol}"
        assert len(df) >= 50, f"Insufficient historical data for {symbol}"
        
        # Check data structure
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in expected_columns:
            assert col in df.columns, f"Missing column: {col}"
        
        print(f"✅ Historical data fetched for {symbol}: {len(df)} records")
    
    def test_live_technical_analysis(self):
        """Test technical analysis with live data"""
        bot = TradingBot()
        bot.paper_trading = True
        
        # Login first
        login_success = asyncio.run(bot.login())
        if not login_success:
            pytest.skip("Failed to login")
        
        # Get historical data
        symbol = 'RELIANCE'
        df = asyncio.run(bot._get_historical_data(symbol))
        
        if df is None or len(df) < 50:
            pytest.skip("Insufficient historical data")
        
        # Run technical analysis
        analyzer = TechnicalAnalyzer()
        signals = analyzer.analyze_stock(df, symbol)
        
        # Signals may or may not be generated depending on market conditions
        print(f"✅ Technical analysis completed for {symbol}: {len(signals)} signals")
        
        # If signals generated, check their structure
        for signal in signals:
            assert signal.symbol == symbol, "Signal symbol mismatch"
            assert signal.signal_type in ['BUY', 'SELL'], "Invalid signal type"
            assert 0 <= signal.confidence <= 1, "Invalid confidence"
            assert signal.entry_price > 0, "Invalid entry price"
    
    def test_live_risk_management(self):
        """Test risk management with live data"""
        bot = TradingBot()
        bot.paper_trading = True
        
        # Login first
        login_success = asyncio.run(bot.login())
        if not login_success:
            pytest.skip("Failed to login")
        
        # Get current price
        symbol = 'RELIANCE'
        current_price = bot.angel_api.get_ltp(symbol)
        
        if current_price is None:
            pytest.skip("Failed to get current price")
        
        # Test position sizing
        stop_loss = current_price * 0.98
        position_size = bot.risk_manager.calculate_position_size(current_price, stop_loss)
        
        assert position_size >= 0, "Invalid position size"
        
        if position_size > 0:
            # Test trade validation
            is_valid, message = bot.risk_manager.validate_trade(
                symbol, "BUY", position_size, current_price, stop_loss
            )
            
            print(f"✅ Risk management test: Position size {position_size}, Valid: {is_valid}")
        else:
            print("✅ Risk management test: Position size 0 (within limits)")

class TestLiveDataQuality:
    """Test data quality from live API"""
    
    @classmethod
    def setup_class(cls):
        """Setup for all tests"""
        cls.api = AngelOneAPI()
        
        # Check credentials and login
        creds = env_config.get_angel_credentials()
        if not all([creds['api_key'], creds['client_id'], creds['password']]):
            pytest.skip("Angel One credentials not configured")
        
        success = cls.api.login()
        if not success:
            pytest.skip("Failed to login to Angel One")
    
    def test_data_consistency(self):
        """Test data consistency across different API calls"""
        symbol = 'RELIANCE'
        
        # Get LTP
        ltp = self.api.get_ltp(symbol)
        
        # Get quote
        quote = self.api.get_quote(symbol)
        
        # Get historical data (last few minutes)
        to_date = datetime.now().strftime("%Y-%m-%d %H:%M")
        from_date = (datetime.now() - timedelta(minutes=10)).strftime("%Y-%m-%d %H:%M")
        
        df = self.api.get_historical_data(symbol, "ONE_MINUTE", from_date, to_date)
        
        if ltp and quote and df is not None and len(df) > 0:
            quote_ltp = float(quote.get('ltp', 0))
            latest_close = df['close'].iloc[-1]
            
            # Prices should be reasonably close (within 1%)
            if quote_ltp > 0:
                price_diff = abs(ltp - quote_ltp) / quote_ltp
                assert price_diff < 0.01, f"LTP mismatch: {ltp} vs {quote_ltp}"
            
            print(f"✅ Data consistency check passed for {symbol}")
            print(f"   LTP: ₹{ltp:.2f}, Quote LTP: ₹{quote_ltp:.2f}, Latest Close: ₹{latest_close:.2f}")
    
    def test_data_completeness(self):
        """Test data completeness for multiple symbols"""
        symbols = ['RELIANCE', 'TCS', 'HDFCBANK']
        
        for symbol in symbols:
            # Test LTP
            ltp = self.api.get_ltp(symbol)
            assert ltp is not None and ltp > 0, f"Invalid LTP for {symbol}"
            
            # Test historical data
            df = self.api.get_historical_data(symbol, "ONE_MINUTE")
            assert df is not None and len(df) > 0, f"No historical data for {symbol}"
            
            # Check for missing values
            missing_data = df.isnull().sum()
            assert missing_data.sum() == 0, f"Missing data found for {symbol}: {missing_data.to_dict()}"
            
            print(f"✅ Data completeness check passed for {symbol}")

if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "-s"])
