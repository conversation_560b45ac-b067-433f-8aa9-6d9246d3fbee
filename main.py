#!/usr/bin/env python3
"""
Angel One Autonomous Intraday Trading Bot
Main entry point for the trading bot
"""
import asyncio
import signal
import sys
import os
from datetime import datetime
import logging

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.logger_config import setup_logging, trading_logger
from src.trading_bot import TradingBot
from src.config import trading_config, is_trading_day, is_market_hours
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

class TradingBotManager:
    """Manager class for the trading bot lifecycle"""
    
    def __init__(self):
        self.bot = None
        self.is_running = False
        
    async def start_bot(self):
        """Start the trading bot"""
        try:
            logger.info("=" * 60)
            logger.info("ANGEL ONE AUTONOMOUS INTRADAY TRADING BOT")
            logger.info("=" * 60)
            logger.info(f"Start Time: {datetime.now(trading_config.TIMEZONE)}")
            logger.info(f"Paper Trading: {os.getenv('PAPER_TRADING', 'True')}")
            logger.info(f"Initial Capital: ₹{trading_config.INITIAL_CAPITAL:,.2f}")
            logger.info(f"Max Risk Per Trade: {trading_config.MAX_RISK_PER_TRADE:.1%}")
            logger.info(f"Max Daily Loss: {trading_config.MAX_DAILY_LOSS:.1%}")
            logger.info("=" * 60)
            
            # Check if today is a trading day
            if not is_trading_day():
                logger.info("Today is not a trading day. Exiting.")
                return
            
            # Initialize trading bot
            self.bot = TradingBot()
            self.is_running = True
            
            # Log system startup
            trading_logger.log_system_event({
                'event_name': 'bot_startup',
                'status': 'success',
                'details': 'Trading bot initialized successfully'
            })
            
            # Start the bot
            await self.bot.start()
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
            await self.stop_bot()
        except Exception as e:
            logger.error(f"Fatal error in trading bot: {str(e)}")
            trading_logger.log_error({
                'error_type': 'fatal_error',
                'error_message': str(e),
                'function': 'start_bot'
            })
            await self.stop_bot()
    
    async def stop_bot(self):
        """Stop the trading bot gracefully"""
        if self.bot and self.is_running:
            logger.info("Shutting down trading bot...")
            
            try:
                await self.bot.stop()
                
                trading_logger.log_system_event({
                    'event_name': 'bot_shutdown',
                    'status': 'success',
                    'details': 'Trading bot shut down gracefully'
                })
                
                logger.info("Trading bot shut down successfully")
                
            except Exception as e:
                logger.error(f"Error during bot shutdown: {str(e)}")
                trading_logger.log_error({
                    'error_type': 'shutdown_error',
                    'error_message': str(e),
                    'function': 'stop_bot'
                })
            
            self.is_running = False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.stop_bot())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def validate_environment():
    """Validate that all required environment variables are set"""
    required_vars = [
        'ANGEL_API_KEY',
        'ANGEL_CLIENT_ID', 
        'ANGEL_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.error("Please check your .env file and ensure all Angel One credentials are set")
        return False
    
    return True

def print_startup_banner():
    """Print startup banner with important information"""
    banner = f"""
╔══════════════════════════════════════════════════════════════╗
║                 ANGEL ONE TRADING BOT                        ║
║                  Autonomous Intraday Trading                 ║
╠══════════════════════════════════════════════════════════════╣
║ Version: 1.0.0                                              ║
║ Mode: {'PAPER TRADING' if os.getenv('PAPER_TRADING', 'True') == 'True' else 'LIVE TRADING'}                                        ║
║ Capital: ₹{trading_config.INITIAL_CAPITAL:,.2f}                                    ║
║ Max Risk/Trade: {trading_config.MAX_RISK_PER_TRADE:.1%}                                   ║
║ Max Daily Loss: {trading_config.MAX_DAILY_LOSS:.1%}                                    ║
║ Max Positions: {trading_config.MAX_POSITIONS}                                        ║
╠══════════════════════════════════════════════════════════════╣
║ DISCLAIMER: Trading involves substantial risk of loss.       ║
║ Past performance does not guarantee future results.          ║
║ Use at your own risk.                                       ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def main():
    """Main entry point"""
    try:
        # Print startup banner
        print_startup_banner()
        
        # Validate environment
        if not validate_environment():
            sys.exit(1)
        
        # Check if market is open (for immediate feedback)
        if not is_trading_day():
            print(f"\n🚫 Today is not a trading day.")
            print(f"Current time: {datetime.now(trading_config.TIMEZONE)}")
            print("The bot will not start.")
            return
        
        if not is_market_hours():
            print(f"\n⏰ Market is currently closed.")
            print(f"Current time: {datetime.now(trading_config.TIMEZONE)}")
            print(f"Market hours: {trading_config.MARKET_START_TIME} - {trading_config.MARKET_END_TIME}")
            print("The bot will wait for market to open.")
        
        # Create and start bot manager
        bot_manager = TradingBotManager()
        bot_manager.setup_signal_handlers()
        
        # Run the bot
        asyncio.run(bot_manager.start_bot())
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye! Trading bot stopped by user.")
    except Exception as e:
        logger.error(f"Fatal error in main: {str(e)}")
        print(f"\n❌ Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
