# 🔐 Angel One SmartAPI Credentials Setup Guide

This guide walks you through obtaining and configuring your Angel One SmartAPI credentials for the trading bot.

## 📋 **Prerequisites**

### **1. Angel One Trading Account**
- ✅ Active Angel One trading account
- ✅ Account should be operational for trading
- ✅ Sufficient funds for testing (minimum ₹10,000 recommended)
- ✅ Mobile number and email verified

### **2. SmartAPI Subscription**
- ✅ SmartAPI subscription activated (usually free)
- ✅ API access permissions enabled
- ✅ Trading permissions granted

---

## 🔑 **Step 1: Get Angel One SmartAPI Credentials**

### **A. Login to Angel One**
1. Go to [Angel One SmartAPI Portal](https://smartapi.angelbroking.com/)
2. Login with your Angel One trading credentials
3. Navigate to "API" section

### **B. Generate API Credentials**
1. **API Key**: 
   - Click "Generate API Key"
   - Copy the generated API key
   - Example: `abcd1234efgh5678`

2. **Client ID**: 
   - This is your Angel One client ID
   - Usually your trading account number
   - Example: `A123456`

3. **Password**: 
   - Your Angel One login password
   - Same password you use for Angel One app/website

4. **TOTP Secret**: 
   - Enable 2FA (Two Factor Authentication)
   - Scan QR code with authenticator app
   - Copy the TOTP secret key
   - Example: `JBSWY3DPEHPK3PXP`

### **C. Test Credentials**
```bash
# Test TOTP generation
python -c "
import pyotp
totp_secret = 'YOUR_TOTP_SECRET_HERE'
totp = pyotp.TOTP(totp_secret)
print(f'Current TOTP: {totp.now()}')
"
```

---

## ⚙️ **Step 2: Configure Bot Credentials**

### **Method 1: Interactive Setup (Recommended)**
```bash
# Run interactive setup script
python scripts/setup_bot.py
```

This will prompt you for:
- Angel One API Key
- Angel One Client ID  
- Angel One Password
- Angel One TOTP Secret
- Trading preferences

### **Method 2: Manual .env File**
```bash
# Create .env file manually
cat > .env << 'EOF'
# Angel One SmartAPI Credentials
ANGEL_API_KEY=your_api_key_here
ANGEL_CLIENT_ID=your_client_id_here
ANGEL_PASSWORD=your_password_here
ANGEL_TOTP_SECRET=your_totp_secret_here

# Trading Configuration
PAPER_TRADING=True
INITIAL_CAPITAL=100000
MAX_DAILY_LOSS=5000
MAX_POSITIONS=4
RISK_PER_TRADE=0.02

# Logging
LOG_LEVEL=INFO
EOF
```

### **Method 3: Environment Variables**
```bash
# Set environment variables (Linux/macOS)
export ANGEL_API_KEY="your_api_key_here"
export ANGEL_CLIENT_ID="your_client_id_here"
export ANGEL_PASSWORD="your_password_here"
export ANGEL_TOTP_SECRET="your_totp_secret_here"

# Windows PowerShell
$env:ANGEL_API_KEY="your_api_key_here"
$env:ANGEL_CLIENT_ID="your_client_id_here"
$env:ANGEL_PASSWORD="your_password_here"
$env:ANGEL_TOTP_SECRET="your_totp_secret_here"
```

---

## 🧪 **Step 3: Test Credentials**

### **Quick Credential Test**
```bash
# Test basic connectivity
python -c "
from src.angel_api import AngelOneAPI
api = AngelOneAPI()
success = api.login()
print(f'✅ Login Success: {success}')
if success:
    profile = api.get_profile()
    print(f'✅ User: {profile.get(\"name\", \"Unknown\")}')
    funds = api.get_funds()
    print(f'✅ Available Cash: ₹{funds.get(\"availablecash\", 0)}')
    api.logout()
else:
    print('❌ Login failed - check credentials')
"
```

### **Comprehensive Credential Test**
```bash
# Run full credential validation
python scripts/test_credentials.py
```

---

## 🔒 **Step 4: Secure Your Credentials**

### **Security Best Practices**

#### **1. File Permissions**
```bash
# Secure .env file (Linux/macOS)
chmod 600 .env

# Verify permissions
ls -la .env
# Should show: -rw------- (owner read/write only)
```

#### **2. Git Ignore**
```bash
# Ensure .env is in .gitignore
echo ".env" >> .gitignore
echo "*.env" >> .gitignore

# Verify .env is ignored
git status  # .env should not appear
```

#### **3. Backup Credentials Securely**
```bash
# Create encrypted backup
gpg -c .env  # Creates .env.gpg

# Store .env.gpg safely, delete original if needed
```

#### **4. Environment-Specific Files**
```bash
# Different environments
.env.development
.env.testing  
.env.production

# Load specific environment
python -c "
from dotenv import load_dotenv
load_dotenv('.env.testing')
"
```

---

## 🚨 **Troubleshooting Credentials**

### **Common Issues & Solutions**

#### **Issue 1: Login Fails**
```
Error: Invalid credentials or authentication failed
```

**Solutions:**
```bash
# 1. Check API key format
echo $ANGEL_API_KEY  # Should be alphanumeric string

# 2. Verify client ID
echo $ANGEL_CLIENT_ID  # Should be your trading account number

# 3. Test TOTP generation
python -c "
import pyotp
totp = pyotp.TOTP('$ANGEL_TOTP_SECRET')
print(f'TOTP: {totp.now()}')
"

# 4. Check password (no special characters issues)
# Try logging into Angel One website with same password
```

#### **Issue 2: TOTP Errors**
```
Error: Invalid TOTP or 2FA failed
```

**Solutions:**
```bash
# 1. Verify TOTP secret format
echo $ANGEL_TOTP_SECRET  # Should be base32 string (A-Z, 2-7)

# 2. Test TOTP generation
python -c "
import pyotp
import time
totp = pyotp.TOTP('$ANGEL_TOTP_SECRET')
for i in range(3):
    print(f'TOTP {i}: {totp.now()}')
    time.sleep(30)
"

# 3. Re-setup 2FA in Angel One portal
# 4. Ensure system time is synchronized
```

#### **Issue 3: API Permissions**
```
Error: API access denied or insufficient permissions
```

**Solutions:**
```bash
# 1. Check SmartAPI subscription status
# 2. Verify trading permissions in Angel One account
# 3. Contact Angel One support for API access
# 4. Check account status (active/suspended)
```

#### **Issue 4: Rate Limiting**
```
Error: Too many requests or rate limit exceeded
```

**Solutions:**
```bash
# 1. Add delays between API calls
# 2. Reduce testing frequency
# 3. Check Angel One API rate limits
# 4. Implement exponential backoff
```

---

## 📝 **Step 5: Credential Validation Checklist**

### **Before Running Tests:**
- [ ] Angel One trading account active
- [ ] SmartAPI subscription enabled
- [ ] API key generated and copied
- [ ] Client ID confirmed
- [ ] Password verified (can login to Angel One)
- [ ] TOTP secret configured and tested
- [ ] .env file created with all credentials
- [ ] File permissions secured (600)
- [ ] .env added to .gitignore
- [ ] Basic login test successful

### **Validation Commands:**
```bash
# 1. Check .env file exists and has content
cat .env | grep ANGEL_

# 2. Test environment loading
python -c "
from dotenv import load_dotenv
import os
load_dotenv()
print('API Key:', os.getenv('ANGEL_API_KEY')[:8] + '...')
print('Client ID:', os.getenv('ANGEL_CLIENT_ID'))
print('TOTP Secret:', os.getenv('ANGEL_TOTP_SECRET')[:8] + '...')
"

# 3. Test API connectivity
python scripts/test_credentials.py

# 4. Run basic tests
python test_setup.py
```

---

## 🎯 **Step 6: Ready for Testing**

Once credentials are configured and validated:

### **Start with Paper Trading**
```bash
# Ensure paper trading mode
echo "PAPER_TRADING=True" >> .env

# Run comprehensive tests
python scripts/run_tests.py

# Start paper trading
python main.py
```

### **Monitor Initial Runs**
```bash
# Watch logs
tail -f logs/trading_bot.log

# Check for credential issues
grep -i "auth\|login\|credential" logs/trading_bot.log

# Verify API calls working
grep -i "api\|request" logs/trading_bot.log
```

---

## 📞 **Support & Help**

### **Angel One Support:**
- **SmartAPI Documentation**: https://smartapi.angelbroking.com/docs
- **Support Email**: <EMAIL>
- **Phone**: Check Angel One website for current support numbers

### **Common Support Requests:**
1. **API Access**: "I need SmartAPI access for my trading account"
2. **Permissions**: "Please enable API trading permissions"
3. **Rate Limits**: "What are the API rate limits?"
4. **Documentation**: "Where can I find API documentation?"

### **Bot-Specific Issues:**
```bash
# Check logs for specific errors
grep "ERROR" logs/errors.log

# Test individual components
python -c "from src.angel_api import AngelOneAPI; print('Import OK')"

# Verify configuration
python -c "from config import env_config; print(env_config.get_angel_credentials())"
```

---

## ⚠️ **Important Security Notes**

### **DO NOT:**
- ❌ Share your API credentials with anyone
- ❌ Commit .env file to version control
- ❌ Use production credentials for testing
- ❌ Store credentials in plain text files
- ❌ Use weak passwords

### **DO:**
- ✅ Use strong, unique passwords
- ✅ Enable 2FA on your Angel One account
- ✅ Regularly rotate API keys
- ✅ Monitor API usage and logs
- ✅ Use paper trading for initial testing
- ✅ Keep credentials secure and backed up

---

## 🚀 **Next Steps**

After credential setup:

1. **Test Connectivity**: `python scripts/test_credentials.py`
2. **Run Full Tests**: `python scripts/run_tests.py`
3. **Start Paper Trading**: `python main.py`
4. **Monitor Performance**: `tail -f logs/trading_bot.log`
5. **Gradual Live Testing**: Start with small capital

**Your credentials are the key to successful trading - keep them secure! 🔐**
