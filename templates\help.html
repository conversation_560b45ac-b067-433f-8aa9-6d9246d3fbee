{% extends "base.html" %}

{% block title %}Help & Documentation - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-question-circle"></i> Help & Documentation
        </h1>
    </div>
</div>

<div class="row">
    <!-- Quick Start Guide -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-rocket"></i> Quick Start Guide
            </div>
            <div class="card-body">
                <div class="accordion" id="quickStartAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="step1">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                <i class="fas fa-play me-2"></i> Step 1: Getting Started
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse show" data-bs-parent="#quickStartAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li><strong>Access the Dashboard:</strong> Navigate to the main dashboard to see your bot's current status</li>
                                    <li><strong>Choose Your Market:</strong> Select from Indian, US, European, Crypto, or Forex markets</li>
                                    <li><strong>Select Trading Mode:</strong> Start with Paper Trading for safe testing</li>
                                    <li><strong>Configure Settings:</strong> Set your capital, risk parameters, and confidence thresholds</li>
                                </ol>
                                <div class="alert alert-info">
                                    <i class="fas fa-lightbulb"></i>
                                    <strong>Tip:</strong> Always start with Paper Trading to test your strategies without risking real money.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="step2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                <i class="fas fa-cog me-2"></i> Step 2: Configuration
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#quickStartAccordion">
                            <div class="accordion-body">
                                <h6>Essential Settings:</h6>
                                <ul>
                                    <li><strong>Trading Capital:</strong> Amount available for trading (start with ₹100)</li>
                                    <li><strong>Position Size:</strong> Percentage of capital per trade (recommended: 10-15%)</li>
                                    <li><strong>Confidence Threshold:</strong> Minimum confidence for trades (recommended: 40-50%)</li>
                                    <li><strong>Stop Loss:</strong> Maximum loss per trade (recommended: 2-3%)</li>
                                    <li><strong>Take Profit:</strong> Target profit per trade (recommended: 5-8%)</li>
                                </ul>
                                
                                <h6 class="mt-3">API Configuration:</h6>
                                <p>For live trading, you'll need to configure your broker API keys:</p>
                                <ul>
                                    <li>Angel One SmartAPI credentials for Indian markets</li>
                                    <li>News API key for enhanced sentiment analysis (optional)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="step3">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                                <i class="fas fa-chart-line me-2"></i> Step 3: Start Trading
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#quickStartAccordion">
                            <div class="accordion-body">
                                <ol>
                                    <li><strong>Start the Bot:</strong> Click "Start Bot" on the dashboard</li>
                                    <li><strong>Monitor Performance:</strong> Watch real-time updates on positions and trades</li>
                                    <li><strong>Review Analytics:</strong> Check the Analytics page for detailed performance metrics</li>
                                    <li><strong>Adjust Settings:</strong> Fine-tune parameters based on performance</li>
                                </ol>
                                
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Important:</strong> Monitor your bot regularly and be prepared to stop it if performance degrades.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- FAQ Section -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-question"></i> Frequently Asked Questions
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                What is Paper Trading and why should I use it?
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Paper Trading is simulated trading using real market data but virtual money. It allows you to:
                                <ul>
                                    <li>Test your bot's strategies without financial risk</li>
                                    <li>Understand how the bot makes decisions</li>
                                    <li>Fine-tune settings and parameters</li>
                                    <li>Build confidence before live trading</li>
                                </ul>
                                We strongly recommend using Paper Trading for at least a week before considering live trading.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                How does the bot make trading decisions?
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                The bot uses a sophisticated multi-factor analysis system:
                                <ul>
                                    <li><strong>Technical Analysis:</strong> RSI, Moving Averages, MACD, Volume indicators</li>
                                    <li><strong>Sentiment Analysis:</strong> News sentiment, social media sentiment, market sentiment</li>
                                    <li><strong>Momentum Analysis:</strong> Price momentum, trend strength, volatility</li>
                                    <li><strong>Adaptive Weights:</strong> Dynamic weight allocation based on market conditions</li>
                                    <li><strong>Confidence Scoring:</strong> Only trades with sufficient confidence are executed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                What markets can the bot trade?
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                The bot supports multiple markets:
                                <ul>
                                    <li><strong>Indian Stock Market:</strong> NSE stocks via Angel One SmartAPI</li>
                                    <li><strong>US Stock Market:</strong> NASDAQ and NYSE stocks</li>
                                    <li><strong>European Markets:</strong> Major European exchanges</li>
                                    <li><strong>Cryptocurrency:</strong> Major crypto pairs (BTC, ETH, etc.)</li>
                                    <li><strong>Forex:</strong> Major currency pairs</li>
                                </ul>
                                Each market has optimized parameters and risk management settings.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                How much money should I start with?
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <strong>For Paper Trading:</strong> Start with ₹100-₹1000 to understand the bot's behavior.
                                <br><br>
                                <strong>For Live Trading:</strong>
                                <ul>
                                    <li>Beginners: ₹5,000-₹10,000</li>
                                    <li>Experienced: ₹25,000-₹50,000</li>
                                    <li>Advanced: ₹1,00,000+</li>
                                </ul>
                                <div class="alert alert-danger">
                                    <strong>Risk Warning:</strong> Never invest more than you can afford to lose. Trading involves significant risk.
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                What are the recommended settings for beginners?
                            </button>
                        </h2>
                        <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <strong>Conservative Settings for Beginners:</strong>
                                <ul>
                                    <li>Trading Capital: ₹100 (Paper Trading)</li>
                                    <li>Position Size: 10-15% per trade</li>
                                    <li>Confidence Threshold: 50-60%</li>
                                    <li>Stop Loss: 2-3%</li>
                                    <li>Take Profit: 5-6%</li>
                                    <li>Maximum Positions: 3-5</li>
                                    <li>Daily Loss Limit: ₹50</li>
                                </ul>
                                These settings prioritize capital preservation over aggressive growth.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Links -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-link"></i> Quick Links
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                        <i class="fas fa-tachometer-alt"></i> Go to Dashboard
                    </a>
                    <a href="{{ url_for('settings') }}" class="btn btn-outline-primary">
                        <i class="fas fa-cog"></i> Bot Settings
                    </a>
                    <a href="{{ url_for('analytics') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> View Analytics
                    </a>
                    <a href="{{ url_for('about') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-info-circle"></i> About the Bot
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Support -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-life-ring"></i> Support & Resources
            </div>
            <div class="card-body">
                <h6>Documentation</h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-decoration-none"><i class="fas fa-book"></i> User Manual</a></li>
                    <li><a href="#" class="text-decoration-none"><i class="fas fa-code"></i> API Documentation</a></li>
                    <li><a href="#" class="text-decoration-none"><i class="fas fa-video"></i> Video Tutorials</a></li>
                </ul>
                
                <h6 class="mt-3">Community</h6>
                <ul class="list-unstyled">
                    <li><a href="#" class="text-decoration-none"><i class="fab fa-discord"></i> Discord Community</a></li>
                    <li><a href="#" class="text-decoration-none"><i class="fab fa-telegram"></i> Telegram Group</a></li>
                    <li><a href="#" class="text-decoration-none"><i class="fab fa-reddit"></i> Reddit Forum</a></li>
                </ul>
                
                <h6 class="mt-3">Contact</h6>
                <ul class="list-unstyled">
                    <li><a href="mailto:<EMAIL>" class="text-decoration-none"><i class="fas fa-envelope"></i> Email Support</a></li>
                    <li><a href="#" class="text-decoration-none"><i class="fas fa-ticket-alt"></i> Submit Ticket</a></li>
                </ul>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-server"></i> System Status
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>API Status:</span>
                        <span class="text-success"><i class="fas fa-circle"></i> Online</span>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Data Feed:</span>
                        <span class="text-success"><i class="fas fa-circle"></i> Active</span>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Trading Engine:</span>
                        <span class="text-success"><i class="fas fa-circle"></i> Running</span>
                    </div>
                </div>
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>Last Update:</span>
                        <span class="text-muted">2 min ago</span>
                    </div>
                </div>
                
                <hr>
                
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    All systems operational. Check our status page for detailed uptime information.
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Risk Warning -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-danger">
            <h5><i class="fas fa-exclamation-triangle"></i> Important Risk Warning</h5>
            <p class="mb-0">
                <strong>Trading involves substantial risk and may not be suitable for all investors.</strong> 
                Past performance is not indicative of future results. You should carefully consider your financial situation 
                and risk tolerance before using this trading bot. Never invest money you cannot afford to lose. 
                The bot's performance can vary significantly based on market conditions, and there is always a risk of loss.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-expand first FAQ item
    document.addEventListener('DOMContentLoaded', function() {
        // Add any initialization code here
    });
</script>
{% endblock %}
