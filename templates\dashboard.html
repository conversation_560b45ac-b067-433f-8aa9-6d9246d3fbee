{% extends "base.html" %}

{% block title %}Dashboard - Ultimate Trading Bot{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt"></i> Trading Bot Dashboard
            <span class="badge bg-primary ms-2">Real-time</span>
            <a href="{{ url_for('live_dashboard') }}" class="btn btn-success ms-3">
                <i class="fas fa-chart-line"></i> Live Charts
            </a>
        </h1>
    </div>
</div>

<!-- Bot Status and Controls -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-robot"></i> Bot Status & Controls
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Current Status</h5>
                        <p class="mb-1">
                            <strong>Status:</strong> 
                            <span id="bot-status" class="{% if status.is_running %}status-running{% else %}status-stopped{% endif %}">
                                {% if status.is_running %}RUNNING{% else %}STOPPED{% endif %}
                            </span>
                        </p>
                        <p class="mb-1">
                            <strong>Market:</strong> 
                            <span class="text-primary">{{ markets[status.current_market].name }}</span>
                        </p>
                        <p class="mb-1">
                            <strong>Mode:</strong> 
                            <span class="badge bg-{% if status.trading_mode == 'paper' %}success{% else %}warning{% endif %}">
                                {{ status.trading_mode.upper() }}
                            </span>
                        </p>
                        <p class="mb-1">
                            <strong>Capital:</strong> 
                            <span class="text-success">{{ markets[status.current_market].currency }} {{ "%.2f"|format(status.capital) }}</span>
                        </p>
                        <p class="mb-0">
                            <strong>Last Update:</strong> 
                            <span id="last-update" class="text-muted">{{ status.last_update or 'Never' }}</span>
                        </p>
                    </div>
                    
                    <div class="col-md-6">
                        <h5>Bot Controls</h5>
                        
                        {% if not status.is_running %}
                        <!-- Start Bot Form -->
                        <form method="POST" action="{{ url_for('start_bot') }}" class="mb-3">
                            <div class="mb-2">
                                <label for="market" class="form-label">Select Market:</label>
                                <select name="market" id="market" class="form-select form-select-sm">
                                    {% for key, market in markets.items() %}
                                    <option value="{{ key }}" {% if key == status.current_market %}selected{% endif %}>
                                        {{ market.name }} ({{ market.currency }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="mb-2">
                                <label for="mode" class="form-label">Trading Mode:</label>
                                <select name="mode" id="mode" class="form-select form-select-sm">
                                    <option value="paper" {% if status.trading_mode == 'paper' %}selected{% endif %}>
                                        Paper Trading (Safe)
                                    </option>
                                    <option value="live">Live Trading (Real Money)</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-start btn-custom">
                                <i class="fas fa-play"></i> Start Bot
                            </button>
                        </form>
                        {% else %}
                        <!-- Stop Bot Form -->
                        <form method="POST" action="{{ url_for('stop_bot') }}">
                            <button type="submit" class="btn btn-stop btn-custom">
                                <i class="fas fa-stop"></i> Stop Bot
                            </button>
                        </form>
                        {% endif %}

                        <!-- Demo Data Button -->
                        <div class="mt-3">
                            <form method="POST" action="{{ url_for('add_mock_data') }}">
                                <button type="submit" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-database"></i> Add Demo Data
                                </button>
                            </form>
                        </div>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                {% if status.trading_mode == 'paper' %}
                                Paper trading uses real market data but simulated money.
                                {% else %}
                                Live trading uses real money. Be careful!
                                {% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-pie"></i> Quick Stats
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="metric-card bg-primary">
                            <div class="metric-value" id="total-trades">{{ status.performance.total_trades }}</div>
                            <div class="metric-label">Total Trades</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-card bg-success">
                            <div class="metric-value" id="positions-count">{{ status.positions }}</div>
                            <div class="metric-label">Open Positions</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row">
    <div class="col-md-3">
        <div class="metric-card">
            <div class="metric-value" id="win-rate">{{ "%.1f"|format(status.performance.win_rate) }}%</div>
            <div class="metric-label">Win Rate</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-info">
            <div class="metric-value" id="total-pnl">{{ "%.2f"|format(status.performance.total_pnl) }}</div>
            <div class="metric-label">Total P&L</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-warning">
            <div class="metric-value">{{ "%.2f"|format(status.performance.daily_pnl) }}</div>
            <div class="metric-label">Daily P&L</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="metric-card bg-secondary">
            <div class="metric-value">{{ status.performance.winning_trades }}</div>
            <div class="metric-label">Winning Trades</div>
        </div>
    </div>
</div>

<!-- Market Overview -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-globe"></i> Available Markets
            </div>
            <div class="card-body">
                <div class="row">
                    {% for key, market in markets.items() %}
                    <div class="col-md-6 mb-3">
                        <div class="card {% if key == status.current_market %}border-primary{% endif %}">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ market.name }}</h6>
                                <p class="card-text">
                                    <span class="badge bg-secondary">{{ market.currency }}</span>
                                    <br>
                                    <small class="text-muted">{{ market.symbols|length }} symbols</small>
                                </p>
                                {% if key == status.current_market %}
                                <span class="badge bg-primary">Current</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> Performance Chart
            </div>
            <div class="card-body">
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-clock"></i> Recent Activity
                <span class="badge bg-light text-dark ms-2">Live Updates</span>
            </div>
            <div class="card-body">
                <div id="recent-activity">
                    {% if status.is_running %}
                    <div class="trade-item">
                        <i class="fas fa-play text-success"></i>
                        <strong>Bot Started</strong> - Trading {{ markets[status.current_market].name }} in {{ status.trading_mode }} mode
                        <small class="text-muted float-end">{{ status.last_update or 'Just now' }}</small>
                    </div>
                    {% else %}
                    <div class="trade-item">
                        <i class="fas fa-pause text-warning"></i>
                        <strong>Bot Stopped</strong> - Ready to start trading
                        <small class="text-muted float-end">{{ status.last_update or 'Unknown' }}</small>
                    </div>
                    {% endif %}
                    
                    <div class="text-center text-muted mt-3">
                        <i class="fas fa-info-circle"></i>
                        Real-time trading activity will appear here when the bot is running
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Performance Chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    const performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
            datasets: [{
                label: 'P&L',
                data: [0, 5, 3, 8, 12, 15, {{ status.performance.total_pnl }}],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    
    // Update chart with real-time data
    socket.on('bot_update', function(data) {
        if (data.performance) {
            // Update the last data point
            performanceChart.data.datasets[0].data[6] = data.performance.total_pnl;
            performanceChart.update('none');
        }
    });
    
    // Handle new trade notifications
    socket.on('new_trade', function(data) {
        const activityDiv = document.getElementById('recent-activity');
        const newActivity = document.createElement('div');
        newActivity.className = 'trade-item ' + (data.action.includes('BUY') ? 'trade-buy' : 'trade-sell');
        newActivity.innerHTML = `
            <i class="fas fa-${data.action.includes('BUY') ? 'arrow-up' : 'arrow-down'} text-${data.action.includes('BUY') ? 'success' : 'danger'}"></i>
            <strong>${data.symbol} ${data.action}</strong> - ${data.price.toFixed(2)} (${(data.confidence * 100).toFixed(1)}% confidence)
            <small class="text-muted float-end">${data.timestamp}</small>
        `;
        
        // Add to top of activity list
        activityDiv.insertBefore(newActivity, activityDiv.firstChild);
        
        // Keep only last 5 activities
        const activities = activityDiv.querySelectorAll('.trade-item');
        if (activities.length > 5) {
            activityDiv.removeChild(activities[activities.length - 1]);
        }
    });
    
    // Auto-refresh page data every 60 seconds
    setInterval(function() {
        if (!document.hidden) {
            // Only refresh if page is visible
            socket.emit('request_update');
        }
    }, 60000);
</script>
{% endblock %}
