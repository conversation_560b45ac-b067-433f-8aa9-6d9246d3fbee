#!/usr/bin/env python3
"""
Setup script for Angel One Trading Bot
Helps users configure the bot for first-time use
"""
import os
import sys
import getpass
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'data', 'backtest_results']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")

def setup_environment_file():
    """Setup .env file with user credentials"""
    print("\n" + "="*60)
    print("ANGEL ONE SMARTAPI CREDENTIALS SETUP")
    print("="*60)
    print("Please enter your Angel One SmartAPI credentials.")
    print("You can get these from: https://smartapi.angelbroking.com/")
    print()
    
    # Get credentials from user
    api_key = input("Enter your API Key: ").strip()
    client_id = input("Enter your Client ID: ").strip()
    password = getpass.getpass("Enter your Password: ").strip()
    totp_secret = input("Enter your TOTP Secret (optional): ").strip()
    
    # Trading configuration
    print("\n" + "-"*40)
    print("TRADING CONFIGURATION")
    print("-"*40)
    
    paper_trading = input("Start with Paper Trading? (Y/n): ").strip().lower()
    paper_trading = "True" if paper_trading != 'n' else "False"
    
    try:
        initial_capital = float(input("Initial Capital (₹) [100000]: ") or "100000")
        max_daily_loss = float(input("Max Daily Loss (₹) [5000]: ") or "5000")
        max_positions = int(input("Max Simultaneous Positions [4]: ") or "4")
    except ValueError:
        print("Invalid input, using default values")
        initial_capital = 100000
        max_daily_loss = 5000
        max_positions = 4
    
    # Create .env file
    env_content = f"""# Angel One SmartAPI Credentials
ANGEL_API_KEY={api_key}
ANGEL_CLIENT_ID={client_id}
ANGEL_PASSWORD={password}
ANGEL_TOTP_SECRET={totp_secret}

# Trading Configuration
PAPER_TRADING={paper_trading}
INITIAL_CAPITAL={initial_capital}
MAX_DAILY_LOSS={max_daily_loss}
MAX_POSITIONS={max_positions}

# Database Configuration
DATABASE_URL=sqlite:///trading_bot.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/trading_bot.log

# Risk Management
MAX_RISK_PER_TRADE=2000
STOP_LOSS_PERCENTAGE=1.5
TARGET_PERCENTAGE=3.0
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print("\n✓ Environment file (.env) created successfully!")
    
    if paper_trading == "True":
        print("\n🟡 PAPER TRADING MODE ENABLED")
        print("   - No real money will be used")
        print("   - Perfect for testing strategies")
        print("   - Switch to live trading after testing")
    else:
        print("\n🔴 LIVE TRADING MODE ENABLED")
        print("   - REAL MONEY WILL BE USED")
        print("   - Ensure you understand the risks")
        print("   - Start with small amounts")

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("\n" + "="*60)
    print("CHECKING DEPENDENCIES")
    print("="*60)
    
    required_packages = [
        'pandas',
        'numpy', 
        'requests',
        'talib',
        'python-dotenv',
        'schedule'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ All dependencies are installed!")
        return True

def validate_credentials():
    """Validate Angel One credentials"""
    print("\n" + "="*60)
    print("VALIDATING CREDENTIALS")
    print("="*60)
    
    if not os.path.exists('.env'):
        print("❌ .env file not found. Please run setup first.")
        return False
    
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['ANGEL_API_KEY', 'ANGEL_CLIENT_ID', 'ANGEL_PASSWORD']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing credentials: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required credentials are present!")
    
    # Test connection (optional)
    test_connection = input("\nTest connection to Angel One? (Y/n): ").strip().lower()
    if test_connection != 'n':
        try:
            sys.path.append('src')
            from angel_api import AngelOneAPI
            
            api = AngelOneAPI()
            if api.login():
                print("✅ Successfully connected to Angel One!")
                api.logout()
                return True
            else:
                print("❌ Failed to connect to Angel One. Please check credentials.")
                return False
        except Exception as e:
            print(f"❌ Connection test failed: {str(e)}")
            return False
    
    return True

def show_next_steps():
    """Show next steps to user"""
    print("\n" + "="*60)
    print("SETUP COMPLETE!")
    print("="*60)
    print("Your Angel One Trading Bot is ready to use!")
    print()
    print("Next Steps:")
    print("1. Review configuration in config.py if needed")
    print("2. Start the bot: python main.py")
    print("3. Monitor logs: tail -f logs/trading_bot.log")
    print("4. Check performance in logs/trades.log")
    print()
    print("Important Notes:")
    print("- Start with paper trading to test strategies")
    print("- Monitor the bot closely during initial runs")
    print("- Understand the risks before live trading")
    print("- Keep your API credentials secure")
    print()
    print("Documentation: README.md")
    print("Support: Check GitHub issues")
    print("="*60)

def main():
    """Main setup function"""
    print("🤖 Angel One Trading Bot Setup")
    print("="*60)
    
    # Create directories
    create_directories()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first.")
        sys.exit(1)
    
    # Setup environment
    if not os.path.exists('.env'):
        setup_environment_file()
    else:
        overwrite = input("\n.env file exists. Overwrite? (y/N): ").strip().lower()
        if overwrite == 'y':
            setup_environment_file()
    
    # Validate credentials
    if validate_credentials():
        show_next_steps()
    else:
        print("\n❌ Setup incomplete. Please check your credentials.")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nSetup cancelled by user.")
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")
        sys.exit(1)
