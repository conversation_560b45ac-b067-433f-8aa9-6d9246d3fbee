#!/bin/bash
# Linux/macOS script to run the Angel One Trading Bot

echo "========================================"
echo "Angel One Autonomous Trading Bot"
echo "========================================"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Check if requirements are installed
echo "Checking dependencies..."
pip install -r requirements.txt

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo ".env file not found. Running setup..."
    python scripts/setup_bot.py
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start the trading bot
echo "Starting trading bot..."
python main.py

# Deactivate virtual environment
deactivate

echo "Trading bot stopped."
